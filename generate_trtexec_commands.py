#!/usr/bin/env python3
"""
TensorRT Command Generator

This script generates the correct trtexec commands for your TensorRT version
and ONNX models, specifically optimized for RTX 3050 Ti.

Usage:
    python generate_trtexec_commands.py
"""

import os
import glob
import subprocess

def get_tensorrt_version():
    """Get TensorRT version"""
    try:
        import tensorrt as trt
        return trt.__version__
    except ImportError:
        return "Unknown"

def find_trtexec():
    """Find trtexec executable"""
    possible_paths = [
        "trtexec",
        "trtexec.exe",
        "/usr/src/tensorrt/bin/trtexec",
        "/opt/tensorrt/bin/trtexec",
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\TensorRT\\bin\\trtexec.exe",
        "C:\\TensorRT\\bin\\trtexec.exe",
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def get_onnx_info(onnx_path):
    """Get ONNX model information"""
    try:
        import onnx
        model = onnx.load(onnx_path)
        input_info = model.graph.input[0]
        input_name = input_info.name
        
        # Extract shape
        shape = []
        for dim in input_info.type.tensor_type.shape.dim:
            if dim.dim_value:
                shape.append(dim.dim_value)
            else:
                shape.append(-1)  # Dynamic dimension
        
        return input_name, shape
    except Exception as e:
        return "input", [1, 3, 1280, 1280]  # Default

def generate_commands():
    """Generate trtexec commands for all ONNX models"""
    
    print("TensorRT Command Generator for RTX 3050 Ti")
    print("=" * 50)
    
    # Check TensorRT version
    trt_version = get_tensorrt_version()
    print(f"TensorRT version: {trt_version}")
    
    # Find trtexec
    trtexec_path = find_trtexec()
    if trtexec_path:
        print(f"trtexec found: {trtexec_path}")
    else:
        print("❌ trtexec not found!")
        trtexec_path = "trtexec"  # Use generic name
    
    # Find ONNX models
    onnx_models = sorted(glob.glob("*.onnx"))
    if not onnx_models:
        print("No ONNX models found in current directory")
        return
    
    print(f"\nFound {len(onnx_models)} ONNX models:")
    for model in onnx_models:
        size_mb = os.path.getsize(model) / (1024 * 1024)
        print(f"  {model} ({size_mb:.1f} MB)")
    
    print("\n" + "=" * 50)
    print("RECOMMENDED COMMANDS FOR RTX 3050 Ti")
    print("=" * 50)
    
    for onnx_model in onnx_models:
        base_name = os.path.splitext(onnx_model)[0]
        engine_name = f"{base_name}.plan"
        
        input_name, shape = get_onnx_info(onnx_model)
        
        print(f"\n📁 {onnx_model}")
        print(f"   Input: {input_name}, Shape: {shape}")
        
        # Basic command (most compatible)
        print(f"\n🔹 Basic conversion (FP16, 1GB workspace):")
        basic_cmd = f'{trtexec_path} --onnx={onnx_model} --saveEngine={engine_name} --fp16 --memPoolSize=workspace:1024'
        print(f"   {basic_cmd}")
        
        # Fallback command (no memory specification)
        print(f"\n🔹 Fallback conversion (if above fails):")
        fallback_cmd = f'{trtexec_path} --onnx={onnx_model} --saveEngine={engine_name} --fp16'
        print(f"   {fallback_cmd}")
        
        # Conservative command (FP32)
        print(f"\n🔹 Conservative conversion (FP32, more memory):")
        conservative_cmd = f'{trtexec_path} --onnx={onnx_model} --saveEngine={engine_name} --memPoolSize=workspace:512'
        print(f"   {conservative_cmd}")
        
        print("-" * 50)
    
    print(f"\n💡 USAGE TIPS:")
    print(f"1. Try the basic command first")
    print(f"2. If it fails with memory errors, try the fallback command")
    print(f"3. If still failing, try the conservative command")
    print(f"4. After conversion, test with:")
    print(f"   python run/InferenceTensorRT.py --source input.png --engine <engine_name>")
    
    print(f"\n🔧 BATCH CONVERSION SCRIPT:")
    print("# Copy and paste these commands to convert all models:")
    print()
    
    for onnx_model in onnx_models:
        base_name = os.path.splitext(onnx_model)[0]
        engine_name = f"{base_name}.plan"
        cmd = f'{trtexec_path} --onnx={onnx_model} --saveEngine={engine_name} --fp16 --memPoolSize=workspace:1024'
        print(f"echo 'Converting {onnx_model}...'")
        print(cmd)
        print(f"if [ $? -eq 0 ]; then echo '✅ {engine_name} created'; else echo '❌ {onnx_model} failed'; fi")
        print()
    
    print(f"\n📋 ALTERNATIVE: Use the simple converter script:")
    print("python simple_onnx_to_tensorrt.py --list")
    for onnx_model in onnx_models[:3]:  # Show first 3 as examples
        print(f"python simple_onnx_to_tensorrt.py {onnx_model} --test")

def main():
    try:
        generate_commands()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
