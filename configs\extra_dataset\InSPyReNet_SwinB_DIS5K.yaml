Model:
    name: "InSPyReNet_SwinB"
    depth: 64
    pretrained: True
    base_size: [1024, 1024]
    threshold: NULL

Train:
    Dataset:
        type: "RGB_Dataset"
        root: "data/Train_Dataset"
        sets: ['DIS-TR']
        transforms:
            static_resize: 
                size: [1024, 1024]
            random_scale_crop:
                range: [0.75, 1.25]
            random_flip:
                lr: True
                ud: False
            random_rotate:
                range: [-10, 10]
            random_image_enhance:
                methods: ['contrast', 'sharpness', 'brightness']
            tonumpy: NULL
            normalize: 
                mean: [0.485, 0.456, 0.406]
                std: [0.229, 0.224, 0.225]
            totensor: NULL
    Dataloader:
        batch_size: 1
        shuffle: True
        num_workers: 8
        pin_memory: False
    Optimizer:
        type: "Adam"
        lr: 1.0e-05
        weight_decay: 0.0
        mixed_precision: False
    Scheduler:
        type: "PolyLr"
        epoch: 180
        gamma: 0.9
        minimum_lr: 1.0e-07
        warmup_iteration: 12000
    Checkpoint:
        checkpoint_epoch: 1
        checkpoint_dir: "snapshots/InSPyReNet_SwinB_DIS5K"
    Debug:
        keys: ['saliency', 'laplacian']
    
Test:
    Dataset:
        type: "RGB_Dataset"
        root: "data/Test_Dataset"
        sets: ['DIS-VD', 'DIS-TE1', 'DIS-TE2', 'DIS-TE3', 'DIS-TE4']
        transforms:
            static_resize:
                size: [1024, 1024]
            tonumpy: NULL
            normalize: 
                mean: [0.485, 0.456, 0.406]
                std: [0.229, 0.224, 0.225]
            totensor: NULL
    Dataloader:
        num_workers: 8
        pin_memory: True
    Checkpoint:
        checkpoint_dir: "snapshots/InSPyReNet_SwinB_DIS5K"

Eval:
    gt_root: "data/Test_Dataset"
    pred_root: "snapshots/InSPyReNet_SwinB_DIS5K"
    result_path: "results"
    datasets: ['DIS-VD', 'DIS-TE1', 'DIS-TE2', 'DIS-TE3', 'DIS-TE4']
    metrics: ['Sm', 'mae', 'adpEm', 'maxEm', 'avgEm', 'adpFm', 'maxFm', 'avgFm', 'wFm', 'mBA']
