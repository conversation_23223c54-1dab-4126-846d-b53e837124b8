import os
import torch
import torch.nn as nn
import argparse

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

# In torchscript_create_plus_ultra.py

class InferenceWrapper(nn.Module):
    def __init__(self, model):
        super().__init__()
        self.model = model
        self.model.eval()
        if hasattr(self.model, 'forward_inference'):
            self.model.forward = self.model.forward_inference

    def forward(self, x):
        """
        Forward pass that handles dynamic shapes
        Args:
            x: Input tensor of shape (B, C, H, W)
        Returns:
            Output tensor
        """
        # Get input dimensions
        B, C, H, W = x.shape
        
        # Ensure dimensions are divisible by patch size
        patch_size = 4
        pad_h = (patch_size - H % patch_size) % patch_size
        pad_w = (patch_size - W % patch_size) % patch_size
        
        # Pad if needed
        if pad_h > 0 or pad_w > 0:
            x = torch.nn.functional.pad(x, (0, pad_w, 0, pad_h))
        
        # Store original dimensions for Swin Transformer
        self.H = H
        self.W = W
        
        # Forward pass
        output = self.model({'image': x})
        
        # Return only the prediction
        return output['pred']

def export_to_torchscript(config_path, checkpoint_dir, torchscript_path, batch_size=1, input_size=(1280, 1280)):
    """
    Export PyTorch model to TorchScript format with dynamic input sizes
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        torchscript_path: Path to save the TorchScript model
        batch_size: Batch size for dummy input
        input_size: Input size (height, width)
    """
    try:
        # Load configuration
        opt = load_config(config_path)

        # Load model
        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        # Wrap the model
        wrapper = InferenceWrapper(model)

        # Create dummy input tensor
        dummy_input = torch.randn(batch_size, 3, *input_size)

        # Export to TorchScript using tracing
        print("Tracing model...")
        # Use script instead of trace for better dynamic shape handling
        # Add example inputs for better tracing
        traced_model = torch.jit.trace(wrapper, dummy_input, strict=False)
        
        # Optimize for CUDA
        print("Optimizing for CUDA...")
        traced_model = torch.jit.optimize_for_inference(traced_model)
        
        # Save the model
        torch.jit.save(traced_model, torchscript_path)
        print(f"Model has been successfully exported to {torchscript_path}")

        # Verify the model
        try:
            # Load the model
            loaded_model = torch.jit.load(torchscript_path)
            print("TorchScript model loaded successfully")
            
            # Test with a different size
            # First, create a dummy input with the same size as the original
            test_input = torch.randn(batch_size, 3, *input_size)
            with torch.no_grad():
                output = loaded_model(test_input)
            print("Model handles original size successfully")
            
            # Then try with a different size
            try:
                test_input = torch.randn(1, 3, 2160, 3840)
                with torch.no_grad():
                    output = loaded_model(test_input)
                print("Model handles dynamic shapes successfully")
            except Exception as e:
                print(f"Warning: Dynamic shape verification failed: {e}")
                print("Note: Dynamic shapes may still work during inference")

        except Exception as e:
            print(f"Warning: Model verification failed: {e}")

    except Exception as e:
        print(f"Error exporting model: {e}")
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Export PyTorch model to TorchScript format.')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to the configuration file.')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint.')
    parser.add_argument('--torchscript-path', '-t', type=str, default="Plus_Ultra.pt", help='Path to save the TorchScript model.')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for dummy input.')
    parser.add_argument('--input-size', type=int, nargs=2, default=[1280, 1280], help='Input size (height width).')
    
    args = parser.parse_args()
    
    export_to_torchscript(
        config_path=args.config,
        checkpoint_dir=args.checkpoint_dir,
        torchscript_path=args.torchscript_path,
        batch_size=args.batch_size,
        input_size=tuple(args.input_size)
    )

    import torch

