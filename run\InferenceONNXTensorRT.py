"""
InSPyReNet ONNX Inference with TensorRT Provider

This script uses ONNX Runtime with TensorRT execution provider for accelerated inference.
It's more memory-efficient than loading PyTorch models and converting to TensorRT.

Usage examples:
    python run/InferenceONNXTensorRT.py --source input.png --onnx Plus_Ultra_800x800.onnx
    python run/InferenceONNXTensorRT.py --source video.mp4 --onnx Plus_Ultra_1280x720.onnx
"""

import os
import cv2
import sys
import tqdm
import torch
import argparse
import numpy as np
import onnxruntime as ort

from PIL import Image

filepath = os.path.split(os.path.abspath(__file__))[0]
repopath = os.path.split(filepath)[0]
sys.path.append(repopath)

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

def _args():
    parser = argparse.ArgumentParser(description='InSPyReNet ONNX Inference with TensorRT')
    parser.add_argument('--config', '-c', type=str, default='configs/extra_dataset/Plus_Ultra.yaml',
                       help='Path to model configuration file')
    parser.add_argument('--source', '-s', type=str, default='input.png',
                       help='Input source: image file, video file, directory, or webcam index')
    parser.add_argument('--dest', '-d', type=str, default='output.png',
                       help='Output destination directory or file')
    parser.add_argument('--type', '-t', type=str, default='map',
                       help='Output type: map, rgba, green, blur, overlay, or background image path')
    parser.add_argument('--onnx', '-o', type=str, default='Plus_Ultra_800x800.onnx',
                       help='Path to ONNX model file')
    parser.add_argument('--verbose', '-v', action='store_true', default=True,
                       help='Enable verbose output')
    parser.add_argument('--use_cpu', action='store_true', default=False,
                       help='Force CPU execution (disable TensorRT)')
    parser.add_argument('--trt_cache_dir', type=str, default='./trt_cache',
                       help='Directory for TensorRT engine cache')
    return parser.parse_args()

def get_format(source):
    img_count = len([i for i in source if i.lower().endswith(('.jpg', '.png', '.jpeg'))])
    vid_count = len([i for i in source if i.lower().endswith(('.mp4', '.avi', '.mov' ))])
    
    if img_count * vid_count != 0:
        return ''
    elif img_count != 0:
        return 'Image'
    elif vid_count != 0:
        return 'Video'
    else:
        return ''

def create_onnx_session(onnx_path, use_cpu=False, trt_cache_dir='./trt_cache'):
    """Create ONNX Runtime session with TensorRT provider"""
    
    if not os.path.exists(onnx_path):
        raise FileNotFoundError(f"ONNX model not found: {onnx_path}")
    
    # Create TensorRT cache directory
    os.makedirs(trt_cache_dir, exist_ok=True)
    
    providers = []
    provider_options = []
    
    if not use_cpu:
        try:
            # Try TensorRT provider first
            providers.append('TensorrtExecutionProvider')
            provider_options.append({
                'trt_engine_cache_enable': True,
                'trt_engine_cache_path': trt_cache_dir,
                'trt_fp16_enable': True,
                'trt_max_workspace_size': **********,  # 1GB for RTX 3050 Ti
                'trt_max_partition_iterations': 1000,
                'trt_min_subgraph_size': 3,  # Larger subgraphs to avoid reshape issues
                'trt_force_sequential_engine_build': True,  # More stable for complex models
                'trt_engine_cache_prefix': 'inspyrenet_'  # Unique prefix
            })
            print("✓ TensorRT provider configured")
        except Exception as e:
            print(f"⚠️ TensorRT provider failed: {e}")
    
    # Add CUDA provider as fallback
    try:
        providers.append('CUDAExecutionProvider')
        provider_options.append({
            'device_id': 0,
            'arena_extend_strategy': 'kNextPowerOfTwo',
            'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB limit for RTX 3050 Ti
            'cudnn_conv_algo_search': 'EXHAUSTIVE',
            'do_copy_in_default_stream': True,
        })
        print("✓ CUDA provider configured")
    except Exception as e:
        print(f"⚠️ CUDA provider failed: {e}")
    
    # Always add CPU as final fallback
    providers.append('CPUExecutionProvider')
    provider_options.append({})
    
    # Session options for better performance
    sess_options = ort.SessionOptions()
    sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
    
    try:
        session = ort.InferenceSession(
            onnx_path,
            sess_options=sess_options,
            providers=providers,
            provider_options=provider_options
        )
        
        print(f"✓ ONNX session created successfully")
        print(f"Active providers: {session.get_providers()}")
        
        # Print model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        print(f"Input: {input_info.name}, shape: {input_info.shape}, type: {input_info.type}")
        print(f"Output: {output_info.name}, shape: {output_info.shape}, type: {output_info.type}")
        
        return session
        
    except Exception as e:
        print(f"❌ Failed to create ONNX session: {e}")
        raise

def inference(opt, args):
    # Create ONNX session
    try:
        session = create_onnx_session(args.onnx, args.use_cpu, args.trt_cache_dir)
    except Exception as e:
        print(f"Failed to load ONNX model: {e}")
        print("Available ONNX files:")
        for f in os.listdir('.'):
            if f.endswith('.onnx'):
                print(f"  {f}")
        return
    
    # Get input/output names
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name
    
    save_dir = None
    _format = None
    
    if args.source.isnumeric() is True:
        _format = 'Webcam'
    elif os.path.isdir(args.source):
        save_dir = os.path.join('results', args.source.split(os.sep)[-1])
        _format = get_format(os.listdir(args.source))
    elif os.path.isfile(args.source):
        save_dir = 'results'
        _format = get_format([args.source])
        
    if args.dest is not None:
        save_dir = args.dest
        
    if save_dir is not None:
        os.makedirs(save_dir, exist_ok=True)
    
    sample_list = eval(_format + 'Loader')(args.source, opt.Test.Dataset.transforms)

    if args.verbose is True:
        samples = tqdm.tqdm(sample_list, desc='ONNX-TensorRT Inference', total=len(
            sample_list), position=0, leave=False, bar_format='{desc:<30}{percentage:3.0f}%|{bar:50}{r_bar}')
    else:
        samples = sample_list
        
    writer = None
    background = None

    for sample in samples:
        if _format == 'Video' and writer is None:
            writer = cv2.VideoWriter(os.path.join(save_dir, sample['name'] + '.mp4'), 
                                   cv2.VideoWriter_fourcc(*'mp4v'), sample_list.fps, sample['shape'][::-1])
            samples.total += int(sample_list.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if _format == 'Video' and sample['image'] is None:
            if writer is not None:
                writer.release()
            writer = None
            continue
        
        # Prepare input for ONNX
        input_tensor = sample['image'].cpu().numpy()
        
        # Run ONNX inference
        try:
            outputs = session.run([output_name], {input_name: input_tensor})
            output_tensor = torch.from_numpy(outputs[0])
            
            # Apply post-processing (sigmoid + normalization)
            pred = torch.sigmoid(output_tensor)
            pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-8)
            
            out = {'pred': pred}
            
        except Exception as e:
            print(f"Inference failed: {e}")
            continue
                
        pred = to_numpy(out['pred'], sample['shape'])
        img = np.array(sample['original'])
        
        if args.type == 'map':
            img = (np.stack([pred] * 3, axis=-1) * 255).astype(np.uint8)
        elif args.type == 'rgba':
            r, g, b = cv2.split(img)
            pred = (pred * 255).astype(np.uint8)
            img = cv2.merge([r, g, b, pred])
        elif args.type == 'green':
            bg = np.stack([np.ones_like(pred)] * 3, axis=-1) * [120, 255, 155]
            img = img * pred[..., np.newaxis] + bg * (1 - pred[..., np.newaxis])
        elif args.type == 'blur':
            img = img * pred[..., np.newaxis] + cv2.GaussianBlur(img, (0, 0), 15) * (1 - pred[..., np.newaxis])
        elif args.type == 'overlay':
            bg = (np.stack([np.ones_like(pred)] * 3, axis=-1) * [120, 255, 155] + img) // 2
            img = bg * pred[..., np.newaxis] + img * (1 - pred[..., np.newaxis])
            border = cv2.Canny(((pred > .5) * 255).astype(np.uint8), 50, 100)
            img[border != 0] = [120, 255, 155]
        elif args.type.lower().endswith(('.jpg', '.jpeg', '.png')):
            if background is None:
                background = cv2.cvtColor(cv2.imread(args.type), cv2.COLOR_BGR2RGB)
                background = cv2.resize(background, img.shape[:2][::-1])
            img = img * pred[..., np.newaxis] + background * (1 - pred[..., np.newaxis])
            
        img = img.astype(np.uint8)
        
        if _format == 'Image':
            Image.fromarray(img).save(os.path.join(save_dir, sample['name'] + '.png'))
        elif _format == 'Video' and writer is not None:
            writer.write(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        elif _format == 'Webcam':
            cv2.imshow('InSPyReNet ONNX-TensorRT', img)

if __name__ == "__main__":
    args = _args()
    opt = load_config(args.config)
    inference(opt, args)
