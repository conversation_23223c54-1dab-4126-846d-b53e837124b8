#!/usr/bin/env python3
"""
Mixed Precision ONNX Converter for InSPyReNet

This module creates ONNX models with selective FP16/FP32 precision based on the
analysis results from fp16_layer_analyzer.py. Problematic operations (sigmoid,
softmax, layer normalization, division, etc.) are kept in FP32 while safe
operations are converted to FP16 for memory efficiency.

Key Features:
1. Selective precision conversion based on operation type
2. Maintains numerical stability for sensitive operations
3. Optimizes memory usage with FP16 for safe operations
4. Preserves model accuracy while improving performance

Usage:
    python mixed_precision_onnx_converter.py --input_size 800 800
"""

import torch
import torch.nn as nn
import torch.onnx
import onnx
import onnxruntime as ort
import numpy as np
import argparse
import os
import sys
from typing import Dict, List, Set, Tuple, Optional
import yaml

# Add lib to path for imports
sys.path.append('lib')
from lib import *
from utils.misc import *

class ONNXExportWrapper(nn.Module):
    """Wrapper for InSPyReNet to simplify ONNX export"""

    def __init__(self, model):
        super().__init__()
        self.model = model
        self.model.eval()
        # Use forward_inference instead of forward_inspyre for ONNX export
        if hasattr(self.model, 'forward_inference'):
            self.model.forward = self.model.forward_inference

    def forward(self, x):
        """Forward pass that returns only the main saliency output"""
        # Apply padding to ensure dimensions are divisible by patch_size
        _, _, H, W = x.shape
        patch_size = 4
        new_H = ((H + patch_size - 1) // patch_size) * patch_size
        new_W = ((W + patch_size - 1) // patch_size) * patch_size

        if new_H != H or new_W != W:
            pad_H = new_H - H
            pad_W = new_W - W
            x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

        # Use the same input format as the working module
        output = self.model({'image': x})
        # Extract prediction using the same logic as working module
        pred = output.get('pred', torch.sigmoid(output['saliency'][3]))

        # Apply normalization and clamping like the working module
        pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-4)
        return torch.clamp(pred, 0.0, 1.0)

class MixedPrecisionONNXConverter:
    """Converts PyTorch models to mixed precision ONNX with selective FP16/FP32"""

    def __init__(self):
        # Operations that should remain in FP32 for numerical stability
        self.fp32_required_ops = {
            'Sigmoid', 'Softmax', 'LayerNormalization', 'BatchNormalization',
            'Div', 'Exp', 'Log', 'Sqrt', 'Pow', 'ReduceMean', 'ReduceSum',
            'ReduceMin', 'ReduceMax', 'ReduceProd'
        }

        # Operations that can safely use FP16
        self.fp16_safe_ops = {
            'Conv', 'ConvTranspose', 'Gemm', 'Add', 'Sub', 'Mul',
            'Relu', 'LeakyRelu', 'Clip', 'Concat', 'Reshape', 'Transpose',
            'Squeeze', 'Unsqueeze', 'Flatten', 'Gather', 'Slice',
            'Pad', 'Resize', 'Upsample', 'MaxPool', 'AveragePool',
            'GlobalAveragePool', 'GlobalMaxPool', 'Dropout'
        }

        # Special handling for attention operations
        self.attention_ops = {'MatMul'}  # Keep in FP32 if in attention context

        self.conversion_stats = {
            'total_nodes': 0,
            'fp32_nodes': 0,
            'fp16_nodes': 0,
            'unchanged_nodes': 0
        }

    def load_model(self, config_path: str = None, model_name: str = None, **model_kwargs):
        """Load the InSPyReNet model"""
        print("🔄 Loading InSPyReNet model...")

        try:
            # Use the same logic as the working module - Plus_Ultra for 800x800
            config = "configs/extra_dataset/Plus_Ultra.yaml"
            checkpoint = "snapshots/Plus_Ultra"

            print(f"📋 Using config: {config}")
            print(f"📋 Using checkpoint: {checkpoint}")

            opt = load_config(config)
            model = eval(opt.Model.name)(**opt.Model)

            checkpoint_path = os.path.join(checkpoint, 'latest.pth')
            print(f"📋 Loading from checkpoint: {checkpoint_path}")
            model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)

            model.eval()
            print("✅ Model loaded successfully")
            return model

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            raise

    def export_to_onnx(self, model: nn.Module, input_size: Tuple[int, int],
                       output_path: str, opset_version: int = 17):
        """Export PyTorch model to ONNX format"""
        print(f"🔄 Exporting model to ONNX: {output_path}")
        print(f"📐 Input size: {input_size}, Opset version: {opset_version}")

        try:
            # Create output directory
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Create dummy input
            dummy_input = torch.randn(1, 3, input_size[0], input_size[1])

            # Set model to evaluation mode and disable gradients
            model.eval()
            with torch.no_grad():
                # Test forward pass first using wrapper
                print("🔍 Testing forward pass...")
                try:
                    # Create wrapper for ONNX export
                    wrapper_model = ONNXExportWrapper(model)
                    test_output = wrapper_model(dummy_input)
                    print(f"✅ Forward pass successful, output shape: {test_output.shape}")

                    # Use wrapper for export
                    model = wrapper_model

                except Exception as e:
                    print(f"❌ Forward pass failed: {e}")
                    raise

                # Export to ONNX with more conservative settings
                print("🔄 Exporting to ONNX...")
                torch.onnx.export(
                    model,
                    dummy_input,
                    output_path,
                    export_params=True,
                    opset_version=opset_version,
                    do_constant_folding=False,  # Disable constant folding to avoid issues
                    input_names=['input'],
                    output_names=['output'],
                    dynamic_axes={
                        'input': {0: 'batch_size'},
                        'output': {0: 'batch_size'}
                    },
                    verbose=False,
                    training=torch.onnx.TrainingMode.EVAL
                )

            print("✅ ONNX export successful")
            return True

        except Exception as e:
            print(f"❌ Error exporting to ONNX: {e}")
            print(f"💡 Trying alternative export method...")

            # Try alternative export with simpler settings
            try:
                model.eval()
                with torch.no_grad():
                    torch.onnx.export(
                        model,
                        dummy_input,
                        output_path,
                        export_params=True,
                        opset_version=11,  # Use older opset version
                        do_constant_folding=False,
                        input_names=['input'],
                        output_names=['output'],
                        verbose=False
                    )
                print("✅ Alternative ONNX export successful")
                return True
            except Exception as e2:
                print(f"❌ Alternative export also failed: {e2}")
                return False

    def analyze_onnx_graph(self, onnx_model):
        """Analyze ONNX graph to identify nodes for precision conversion"""
        print("🔍 Analyzing ONNX graph for precision assignment...")
        
        graph = onnx_model.graph
        precision_assignments = {}
        attention_context_nodes = set()
        
        # First pass: identify attention context
        for node in graph.node:
            if node.op_type == 'MatMul':
                # Check if this MatMul is in attention context
                for input_name in node.input:
                    if any(keyword in input_name.lower() for keyword in 
                          ['attention', 'attn', 'query', 'key', 'value', 'qkv']):
                        attention_context_nodes.add(node.name)
                        break
        
        # Second pass: assign precision based on operation type and context
        for node in graph.node:
            node_name = node.name or f"{node.op_type}_{len(precision_assignments)}"
            
            if node.op_type in self.fp32_required_ops:
                precision_assignments[node_name] = 'fp32'
                reason = f"Required FP32: {node.op_type} is sensitive to precision"
            elif node.op_type in self.attention_ops and node_name in attention_context_nodes:
                precision_assignments[node_name] = 'fp32'
                reason = f"Required FP32: {node.op_type} in attention context"
            elif node.op_type in self.fp16_safe_ops:
                precision_assignments[node_name] = 'fp16'
                reason = f"Safe for FP16: {node.op_type} is stable"
            else:
                precision_assignments[node_name] = 'fp32'  # Conservative default
                reason = f"Default FP32: {node.op_type} unknown safety"
            
            print(f"  📌 {node_name}: {reason}")
        
        return precision_assignments

    def convert_to_mixed_precision(self, input_onnx_path: str, output_onnx_path: str):
        """Convert ONNX model to mixed precision"""
        print(f"🔄 Converting {input_onnx_path} to mixed precision...")
        
        try:
            # Load ONNX model
            onnx_model = onnx.load(input_onnx_path)
            
            # Analyze graph for precision assignments
            precision_assignments = self.analyze_onnx_graph(onnx_model)
            
            # Convert model using ONNX tools
            from onnxconverter_common import float16
            
            # Create list of nodes to keep in FP32
            fp32_nodes = [name for name, precision in precision_assignments.items() 
                         if precision == 'fp32']
            
            print(f"📊 Conversion statistics:")
            print(f"  • Total nodes: {len(precision_assignments)}")
            print(f"  • FP32 nodes: {len(fp32_nodes)}")
            print(f"  • FP16 nodes: {len(precision_assignments) - len(fp32_nodes)}")
            
            # Convert to mixed precision
            # Keep specified operations in FP32, convert others to FP16
            fp16_model = float16.convert_float_to_float16(
                onnx_model,
                keep_io_types=True,  # Keep input/output in original precision
                disable_shape_infer=False,
                op_block_list=list(self.fp32_required_ops),  # Operations to keep in FP32
                node_block_list=fp32_nodes  # Specific nodes to keep in FP32
            )
            
            # Save converted model
            onnx.save(fp16_model, output_onnx_path)
            
            # Update statistics
            self.conversion_stats['total_nodes'] = len(precision_assignments)
            self.conversion_stats['fp32_nodes'] = len(fp32_nodes)
            self.conversion_stats['fp16_nodes'] = len(precision_assignments) - len(fp32_nodes)
            
            print(f"✅ Mixed precision conversion complete: {output_onnx_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error in mixed precision conversion: {e}")
            return False

    def validate_model(self, original_path: str, converted_path: str, 
                      input_size: Tuple[int, int], tolerance: float = 1e-3):
        """Validate that the converted model produces similar outputs"""
        print("🔍 Validating converted model...")
        
        try:
            # Create test input
            test_input = np.random.randn(1, 3, input_size[0], input_size[1]).astype(np.float32)
            
            # Run original model
            session_fp32 = ort.InferenceSession(original_path, providers=['CPUExecutionProvider'])
            output_fp32 = session_fp32.run(None, {'input': test_input})[0]
            
            # Run converted model
            session_mixed = ort.InferenceSession(converted_path, providers=['CPUExecutionProvider'])
            output_mixed = session_mixed.run(None, {'input': test_input})[0]
            
            # Calculate differences
            abs_diff = np.abs(output_fp32 - output_mixed)
            rel_diff = abs_diff / (np.abs(output_fp32) + 1e-8)
            
            max_abs_diff = np.max(abs_diff)
            max_rel_diff = np.max(rel_diff)
            mean_abs_diff = np.mean(abs_diff)
            mean_rel_diff = np.mean(rel_diff)
            
            print(f"📊 Validation Results:")
            print(f"  • Max absolute difference: {max_abs_diff:.6f}")
            print(f"  • Max relative difference: {max_rel_diff:.6f}")
            print(f"  • Mean absolute difference: {mean_abs_diff:.6f}")
            print(f"  • Mean relative difference: {mean_rel_diff:.6f}")
            
            # Check if within tolerance
            if max_abs_diff < tolerance and max_rel_diff < 0.1:  # 10% relative tolerance
                print("✅ Validation passed - outputs are similar")
                return True
            else:
                print("⚠️  Validation warning - significant differences detected")
                return False
                
        except Exception as e:
            print(f"❌ Error during validation: {e}")
            return False

    def create_mixed_precision_model(self, input_size: Tuple[int, int], 
                                   output_dir: str = ".", model_name: str = "SwinB"):
        """Complete pipeline to create mixed precision ONNX model"""
        print("🚀 Starting Mixed Precision ONNX Conversion Pipeline")
        print("=" * 60)
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Define file paths
        size_str = f"{input_size[0]}x{input_size[1]}"
        fp32_onnx_path = os.path.join(output_dir, f"InsPyReNet_{size_str}_fp32.onnx")
        mixed_onnx_path = os.path.join(output_dir, f"InsPyReNet_{size_str}_mixed_precision.onnx")
        
        try:
            # Step 1: Load model
            model = self.load_model(model_name=model_name)
            
            # Step 2: Export to FP32 ONNX
            if not os.path.exists(fp32_onnx_path):
                print(f"\n📋 Step 2: Exporting FP32 ONNX model...")
                success = self.export_to_onnx(model, input_size, fp32_onnx_path)
                if not success:
                    raise Exception("Failed to export FP32 ONNX model")
            else:
                print(f"\n📋 Step 2: Using existing FP32 ONNX model: {fp32_onnx_path}")
            
            # Step 3: Convert to mixed precision
            print(f"\n📋 Step 3: Converting to mixed precision...")
            success = self.convert_to_mixed_precision(fp32_onnx_path, mixed_onnx_path)
            if not success:
                raise Exception("Failed to convert to mixed precision")
            
            # Step 4: Validate model
            print(f"\n📋 Step 4: Validating converted model...")
            self.validate_model(fp32_onnx_path, mixed_onnx_path, input_size)
            
            # Step 5: Print summary
            print(f"\n🎉 Mixed Precision Conversion Complete!")
            print(f"📁 Output files:")
            print(f"  • FP32 model: {fp32_onnx_path}")
            print(f"  • Mixed precision model: {mixed_onnx_path}")
            print(f"📊 Conversion statistics:")
            print(f"  • Total nodes: {self.conversion_stats['total_nodes']}")
            print(f"  • FP32 nodes: {self.conversion_stats['fp32_nodes']}")
            print(f"  • FP16 nodes: {self.conversion_stats['fp16_nodes']}")
            
            if self.conversion_stats['total_nodes'] > 0:
                fp16_percentage = (self.conversion_stats['fp16_nodes'] / 
                                 self.conversion_stats['total_nodes']) * 100
                print(f"  • FP16 percentage: {fp16_percentage:.1f}%")
            
            return mixed_onnx_path
            
        except Exception as e:
            print(f"❌ Pipeline failed: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Convert InSPyReNet to mixed precision ONNX')
    parser.add_argument('--input_size', nargs=2, type=int, default=[800, 800],
                       help='Input size (height width)')
    parser.add_argument('--output_dir', type=str, default='.',
                       help='Output directory for ONNX models')
    parser.add_argument('--model_name', type=str, default='SwinB',
                       help='Model variant (SwinB, Res2Net50, etc.)')
    
    args = parser.parse_args()
    
    converter = MixedPrecisionONNXConverter()
    result = converter.create_mixed_precision_model(
        input_size=tuple(args.input_size),
        output_dir=args.output_dir,
        model_name=args.model_name
    )
    
    if result:
        print(f"\n✅ Success! Mixed precision model saved to: {result}")
    else:
        print(f"\n❌ Failed to create mixed precision model")
        sys.exit(1)

if __name__ == "__main__":
    main()
