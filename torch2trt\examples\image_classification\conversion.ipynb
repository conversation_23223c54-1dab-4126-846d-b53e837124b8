{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["First, we create the pre-trained ImageNet model.  We'll use ``resnet18`` from the torchvision package.  Make sure to set the device to ``cuda``, since the inputs and parameter devices are inferred from model.  Also make sure to set ``eval()`` to fix batch norm statistics."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torchvision\n", "\n", "model = torchvision.models.resnet18(pretrained=True).cuda().half().eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we create some sample input that will be used to infer the shape and data types of our TensorRT engine"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "data = torch.randn((1, 3, 224, 224)).cuda().half()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, create the optimized TensorRT engine."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from torch2trt import torch2trt\n", "\n", "model_trt = torch2trt(model, [data], fp16_mode=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can execute the network like this"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["output_trt = model_trt(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And check against the original output"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([ 0.7231,  3.0195,  3.1016,  3.1152,  4.7539,  3.8301,  3.9180,  0.3086,\n", "        -0.8726, -0.2261], device='cuda:0', dtype=torch.float16,\n", "       grad_fn=<SliceBackward>)\n", "tensor([ 0.7202,  3.0234,  3.1074,  3.1133,  4.7539,  3.8340,  3.9141,  0.3081,\n", "        -0.8716, -0.2227], device='cuda:0', dtype=torch.float16)\n", "max error: 0.011719\n"]}], "source": ["output = model(data)\n", "\n", "print(output.flatten()[0:10])\n", "print(output_trt.flatten()[0:10])\n", "print('max error: %f' % float(torch.max(torch.abs(output - output_trt))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can save the model like this"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.save(model_trt.state_dict(), 'resnet18_trt.pth')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And load the model like this."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch2trt import TRTModule\n", "\n", "model_trt = TRTModule()\n", "\n", "model_trt.load_state_dict(torch.load('resnet18_trt.pth'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's it for this notebook!  Try out the live demo to see real-time classification on a video feed."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}