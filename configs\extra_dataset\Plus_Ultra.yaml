Model:
    name: "InSPyReNet_SwinB"
    depth: 64
    pretrained: True
    base_size: [1024, 1024]
    threshold: NULL

Train:
    Dataset:
        type: "RGB_Dataset"
        root: "data"
        sets: [
               'Train_Dataset/DUTS-TR', 
               'Train_Dataset/HRSOD-TR', 
               'Train_Dataset/UHRSD-TR',
               'Train_Dataset/DIS-TR',
               'Test_Dataset/DUTS-TE', 
               'Test_Dataset/DUT-OMRON', 
               'Test_Dataset/ECSSD', 
               'Test_Dataset/HKU-IS', 
               'Test_Dataset/PASCAL-S', 
               'Test_Dataset/DAVIS-S', 
               'Test_Dataset/HRSOD-TE',
               'Test_Dataset/UHRSD-TE', 
               'Test_Dataset/FSS-1000', 
               'Test_Dataset/MSRA-10K',
               'Test_Dataset/DIS-VD',
               'Test_Dataset/DIS-TE1',
               'Test_Dataset/DIS-TE2',
               'Test_Dataset/DIS-TE3',
               'Test_Dataset/DIS-TE4'
               ]
        transforms:
            static_resize: 
                size: [1024, 1024]
            random_scale_crop:
                range: [0.75, 1.25]
            random_flip:
                lr: True
                ud: False
            random_rotate:
                range: [-10, 10]
            random_image_enhance:
                methods: ['contrast', 'sharpness', 'brightness']
            tonumpy: NULL
            normalize: 
                mean: [0.485, 0.456, 0.406]
                std: [0.229, 0.224, 0.225]
            totensor: NULL
    Dataloader:
        batch_size: 1
        shuffle: True
        num_workers: 8
        pin_memory: False
    Optimizer:
        type: "Adam"
        lr: 1.0e-05
        weight_decay: 0.0
        mixed_precision: False
    Scheduler:
        type: "PolyLr"
        epoch: 60
        gamma: 0.9
        minimum_lr: 1.0e-07
        warmup_iteration: 12000
    Checkpoint:
        checkpoint_epoch: 1
        checkpoint_dir: "snapshots/Plus_Ultra"
    Debug:
        keys: ['saliency', 'laplacian']
    
Test:
    Dataset:
        type: "RGB_Dataset"
        root: "data/Test_Dataset"
        sets:  ['DUTS-TE', 'DUT-OMRON', 'ECSSD', 'HKU-IS', 'PASCAL-S', 'DAVIS-S', 'HRSOD-TE', 'UHRSD-TE']
        transforms:
            # static_resize: 
            #     size: [1024, 1024]
            dynamic_resize:
                L: 1280
            tonumpy: NULL
            normalize: 
                mean: [0.485, 0.456, 0.406]
                std: [0.229, 0.224, 0.225]
            totensor: NULL
    Dataloader:
        num_workers: 8
        pin_memory: True
    Checkpoint:
        checkpoint_dir: "snapshots/Plus_Ultra"

Eval:
    gt_root: "data/Test_Dataset"
    pred_root: "snapshots/Plus_Ultra"
    result_path: "results"
    datasets: ['DUTS-TE', 'DUT-OMRON', 'ECSSD', 'HKU-IS', 'PASCAL-S', 'DAVIS-S', 'HRSOD-TE', 'UHRSD-TE']
    metrics: ['Sm', 'mae', 'adpEm', 'maxEm', 'avgEm', 'adpFm', 'maxFm', 'avgFm', 'wFm', 'mBA']
