import os
import torch
import torch.nn as nn
import argparse
import onnx

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

def export_to_onnx(config_path, checkpoint_dir, onnx_path, batch_size=1, input_size=(1280, 1280), opset_version=17):
    """
    Export PyTorch model to ONNX format with dynamic input sizes
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        onnx_path: Path to save the ONNX model
        batch_size: Batch size for dummy input
        input_size: Input size (height, width)
        opset_version: ONNX opset version
    """
    try:
        # Load configuration
        opt = load_config(config_path)

        # Load model
        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        # Create a custom wrapper that handles padding
        class CustomWrapper(nn.Module):
            def __init__(self, base_model):
                super().__init__()
                self.model = base_model
                self.model.eval()
                if hasattr(self.model, 'forward_inference'):
                    self.model.forward = self.model.forward_inference

            def forward(self, x):
                # Get dimensions
                B, C, H, W = x.shape
                
                # Ensure dimensions are divisible by patch size (4)
                patch_size = 4
                new_H = ((H + patch_size - 1) // patch_size) * patch_size
                new_W = ((W + patch_size - 1) // patch_size) * patch_size
                
                # Pad if needed
                if new_H != H or new_W != W:
                    pad_H = new_H - H
                    pad_W = new_W - W
                    x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

                # Forward pass
                output = self.model({'image': x})
                
                # Return prediction
                return output['pred']

        # Wrap the model
        wrapper = CustomWrapper(model)

        # Create dummy input tensor
        dummy_input = torch.randn(batch_size, 3, *input_size)

        # Export to ONNX 
        torch.onnx.export(
            wrapper,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size', 2: 'height', 3: 'width'},  # Dynamic dimensions
                'output': {0: 'batch_size', 2: 'height', 3: 'width'}  # Dynamic dimensions
            }
        )

        print(f"Model has been successfully exported to {onnx_path}")

        # Check the model
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX model is valid and has been successfully loaded.")

    except Exception as e:
        print(f"Error exporting model: {e}")
        raise
    
def export_models_for_aspect_ratios(config_path, checkpoint_dir, opset_version=17):
    """
    Export ONNX models for different aspect ratios
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        opset_version: ONNX opset version
    """
    # Define model dimensions as (height, width)
    # Each model maintains ~1.6M pixels for comparable resolution
    model_dimensions = [
        (720, 1280),   # 16:9 landscape (width:height = 16:9)
        (960, 1280),   # 4:3 landscape (width:height = 4:3)
        (1280, 720),   # 9:16 portrait (width:height = 9:16)
        (1280, 960),   # 3:4 portrait (width:height = 3:4)
        (800, 800)     # 1:1 square (width:height = 1:1)
    ]
    
    # Export a model for each dimension
    for height, width in model_dimensions:
        # Always use width x height in the naming (standard convention)
        print(f"\n=== Creating model with dimensions {width}x{height} (width x height) ===")
        onnx_path = f"Plus_Ultra_{width}x{height}_LR.onnx"  # Width x height format
        
        export_to_onnx(
            config_path=config_path,
            checkpoint_dir=checkpoint_dir,
            onnx_path=onnx_path,
            batch_size=1,
            input_size=(height, width),  # Pass height, width tuple as input_size
            opset_version=opset_version
        )
        
        print(f"Model for {width}x{height} created successfully\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Export PyTorch model to ONNX format.')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra_LR.yaml", help='Path to the configuration file.')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra_LR", help='Directory containing model checkpoint.')
    parser.add_argument('--onnx-path', '-o', type=str, default="Plus_Ultra_LR.onnx", help='Path to save the ONNX model.')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for dummy input.')
    parser.add_argument('--input-size', type=int, nargs=2, default=[1280, 1280], help='Input size (height width).')
    parser.add_argument('--opset-version', type=int, default=17, help='ONNX opset version.')
    parser.add_argument('--multi-aspect', action='store_true', default=True, help='Export models for multiple aspect ratios.')
    
    args = parser.parse_args()
    
    if args.multi_aspect:
        # Export models for multiple aspect ratios
        export_models_for_aspect_ratios(
            config_path=args.config,
            checkpoint_dir=args.checkpoint_dir,
            opset_version=args.opset_version
        )
    else:
        # Export a single model with the specified parameters
        export_to_onnx(
            config_path=args.config,
            checkpoint_dir=args.checkpoint_dir,
            onnx_path=args.onnx_path,
            batch_size=args.batch_size,
            input_size=tuple(args.input_size),
            opset_version=args.opset_version
        )