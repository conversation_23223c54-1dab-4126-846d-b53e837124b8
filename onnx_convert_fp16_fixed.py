#!/usr/bin/env python3
"""
Fixed FP16 ONNX Model Converter

This script properly converts ONNX models to FP16 while avoiding numerical issues
that cause incorrect results in TensorRT FP16 inference.

Key fixes:
1. Uses FP16-safe epsilon values (1e-4 instead of 1e-8)
2. Keeps critical operations in FP32 for numerical stability
3. Handles min/max operations carefully to avoid underflow
4. Provides mixed precision approach for optimal performance

Usage:
    python onnx_convert_fp16_fixed.py
    python onnx_convert_fp16_fixed.py --input Plus_Ultra_800x800.onnx
    python onnx_convert_fp16_fixed.py --method mixed
"""

import os
import glob
import argparse
import onnx
import warnings
import numpy as np
from onnxconverter_common import float16
from onnx import numpy_helper

# Suppress truncation warnings
warnings.filterwarnings("ignore", message="the float32 number .* will be truncated to .*")

def convert_onnx_to_fp16_fixed(input_path, output_path=None, method='mixed'):
    """
    Convert an ONNX model from FP32 to FP16 with fixes for numerical stability
    
    Args:
        input_path: Path to input ONNX model (FP32)
        output_path: Path to save FP16 model (optional, auto-generated if None)
        method: 'mixed', 'conservative', or 'aggressive'
    """
    try:
        # Generate output path if not provided
        if output_path is None:
            base_name = input_path.replace('.onnx', '')
            output_path = f"{base_name}_fp16_fixed.onnx"
        
        print(f"Converting {input_path} to FP16 (method: {method})...")
        
        # Load the FP32 model
        model_fp32 = onnx.load(input_path)
        
        # Check model validity
        onnx.checker.check_model(model_fp32)
        
        # Get original model size
        original_size = os.path.getsize(input_path) / (1024 * 1024)  # MB
        
        if method == 'mixed':
            # Mixed precision: Keep critical operations in FP32
            model_fp16 = convert_mixed_precision(model_fp32)
        elif method == 'conservative':
            # Conservative: More operations stay in FP32
            model_fp16 = convert_conservative_fp16(model_fp32)
        elif method == 'aggressive':
            # Aggressive: Convert more to FP16 but with safe parameters
            model_fp16 = convert_aggressive_fp16(model_fp32)
        else:
            raise ValueError(f"Unknown method: {method}")
        
        # Save FP16 model
        onnx.save(model_fp16, output_path)
        
        # Get converted model size
        converted_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        size_reduction = (1 - converted_size / original_size) * 100
        
        print(f"✓ Successfully converted to {output_path}")
        print(f"  Original size: {original_size:.2f} MB")
        print(f"  FP16 size: {converted_size:.2f} MB")
        print(f"  Size reduction: {size_reduction:.1f}%")
        
        # Test the converted model
        if test_model_basic(output_path):
            print("✓ Basic model validation passed")
        else:
            print("⚠️ Model validation failed - may have issues")
        
        return output_path
        
    except Exception as e:
        print(f"✗ Error converting {input_path}: {e}")
        return None

def convert_mixed_precision(model):
    """
    Convert to mixed precision: critical operations stay in FP32
    This is the recommended approach for InSPyReNet models
    """
    # Operations that should stay in FP32 for numerical stability
    fp32_ops = [
        'Sigmoid',      # Critical for final activation
        'Exp', 'Log',   # Exponential operations
        'Div',          # Division operations (especially with small values)
        'Sqrt', 'Pow',  # Power operations
        'ReduceMean', 'ReduceSum', 'ReduceMin', 'ReduceMax',  # Reduction operations
        'Softmax',      # Softmax activation
        'LayerNormalization',  # Normalization layers
        'BatchNormalization',  # Batch normalization
        'InstanceNormalization',  # Instance normalization
        'Sub',          # Subtraction (used in normalization)
        'Add',          # Addition (when used with small epsilon values)
        'Mul',          # Multiplication (when used in normalization)
        'Cast',         # Type casting operations
        'Shape', 'Reshape', 'Transpose',  # Shape operations
        'Slice', 'Gather', 'Concat',  # Indexing operations
    ]
    
    print("  Using mixed precision conversion...")
    print(f"  Keeping {len(fp32_ops)} operation types in FP32")
    
    model_fp16 = float16.convert_float_to_float16(
        model,
        keep_io_types=True,  # Keep inputs/outputs as FP32
        disable_shape_infer=True,  # Avoid shape inference issues
        min_positive_val=1e-4,  # FP16-safe minimum value
        max_finite_val=1e4,     # Conservative maximum value
        op_block_list=fp32_ops  # Operations to keep in FP32
    )
    
    return model_fp16

def convert_conservative_fp16(model):
    """
    Conservative conversion: even more operations stay in FP32
    Use this if mixed precision still has issues
    """
    # Even more conservative list
    fp32_ops = [
        'Sigmoid', 'Tanh', 'Relu', 'LeakyRelu', 'Elu', 'Selu',  # All activations
        'Exp', 'Log', 'Sqrt', 'Pow', 'Abs',  # Math operations
        'Div', 'Sub', 'Add', 'Mul',  # All arithmetic operations
        'ReduceMean', 'ReduceSum', 'ReduceMin', 'ReduceMax',  # All reductions
        'Softmax', 'LogSoftmax',  # Softmax operations
        'LayerNormalization', 'BatchNormalization', 'InstanceNormalization',  # Normalizations
        'Cast', 'Shape', 'Reshape', 'Transpose', 'Squeeze', 'Unsqueeze',  # Shape ops
        'Slice', 'Gather', 'Concat', 'Split',  # Indexing operations
        'Interpolate', 'Resize', 'Upsample',  # Interpolation operations
        'Pad', 'Clip',  # Padding and clipping
    ]
    
    print("  Using conservative conversion...")
    print(f"  Keeping {len(fp32_ops)} operation types in FP32")
    
    model_fp16 = float16.convert_float_to_float16(
        model,
        keep_io_types=True,
        disable_shape_infer=True,
        min_positive_val=1e-3,  # Even more conservative
        max_finite_val=1e3,
        op_block_list=fp32_ops
    )
    
    return model_fp16

def convert_aggressive_fp16(model):
    """
    Aggressive conversion: convert more to FP16 but with safe parameters
    Use this only if you need maximum performance and can tolerate some precision loss
    """
    # Minimal list - only the most critical operations stay in FP32
    fp32_ops = [
        'Sigmoid',  # Final activation
        'Div',      # Division operations
        'ReduceMin', 'ReduceMax',  # Min/max operations
        'Sub',      # Subtraction in normalization
    ]
    
    print("  Using aggressive conversion...")
    print(f"  Keeping only {len(fp32_ops)} critical operation types in FP32")
    
    model_fp16 = float16.convert_float_to_float16(
        model,
        keep_io_types=True,
        disable_shape_infer=True,
        min_positive_val=1e-4,
        max_finite_val=1e4,
        op_block_list=fp32_ops
    )
    
    return model_fp16

def test_model_basic(model_path):
    """Basic test to see if model can be loaded"""
    try:
        import onnxruntime as ort
        
        # Try to create session
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        # Create dummy input
        if isinstance(input_shape[0], str) or input_shape[0] is None:
            input_shape = [1] + list(input_shape[1:])
        
        dummy_input = np.random.randn(*input_shape).astype(np.float32) * 0.5
        
        # Run inference
        outputs = session.run(None, {input_info.name: dummy_input})
        
        # Check for NaN/Inf in outputs
        for i, output in enumerate(outputs):
            if np.isnan(output).any() or np.isinf(output).any():
                print(f"  ⚠️ Output {i} contains NaN or Inf values")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Basic test failed: {e}")
        return False

def convert_all_models(input_dir=".", method='mixed'):
    """Convert all InSPyReNet models in the directory"""
    
    # Look for InSPyReNet models
    model_patterns = [
        "InsPyReNet_*.onnx",
        "Plus_Ultra_*.onnx",
        "*_800x800.onnx",
        "*_1280x720.onnx",
        "*_1280x960.onnx",
        "*_720x1280.onnx",
        "*_960x1280.onnx"
    ]
    
    found_models = []
    for pattern in model_patterns:
        found_models.extend(glob.glob(os.path.join(input_dir, pattern)))
    
    # Remove duplicates and FP16 models
    unique_models = []
    for model in found_models:
        if '_fp16' not in model and model not in unique_models:
            unique_models.append(model)
    
    if not unique_models:
        print("No suitable ONNX models found")
        return
    
    print(f"Found {len(unique_models)} models to convert:")
    for model in unique_models:
        print(f"  {os.path.basename(model)}")
    
    print(f"\nConverting with method: {method}")
    print("=" * 60)
    
    successful = 0
    failed = 0
    
    for model_path in unique_models:
        result = convert_onnx_to_fp16_fixed(model_path, method=method)
        if result:
            successful += 1
        else:
            failed += 1
        print("-" * 40)
    
    print(f"\nConversion Summary:")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Total: {len(unique_models)}")
    
    if successful > 0:
        print(f"\n✓ Fixed FP16 models created!")
        print(f"Now convert to TensorRT FP16 using:")
        print(f"  python convert_onnx_to_tensorrt.py --onnx <model>_fp16_fixed.onnx --fp16")

def main():
    parser = argparse.ArgumentParser(description='Convert ONNX models to FP16 with numerical stability fixes')
    parser.add_argument('--input', '-i', type=str, 
                       help='Input ONNX file (if not specified, converts all models in current directory)')
    parser.add_argument('--output', '-o', type=str, 
                       help='Output file path (optional, auto-generated if not provided)')
    parser.add_argument('--method', '-m', type=str, default='mixed',
                       choices=['mixed', 'conservative', 'aggressive'],
                       help='Conversion method (default: mixed)')
    parser.add_argument('--test-only', action='store_true',
                       help='Only test existing FP16 models, do not convert')
    
    args = parser.parse_args()
    
    print("Fixed FP16 ONNX Model Converter")
    print("=" * 40)
    print("This converter fixes numerical stability issues in FP16 models")
    print("that cause incorrect results in TensorRT FP16 inference.")
    print("=" * 40)
    
    if args.test_only:
        # Test existing FP16 models
        fp16_models = glob.glob("*_fp16*.onnx")
        if not fp16_models:
            print("No FP16 models found to test")
            return
        
        print(f"Testing {len(fp16_models)} FP16 models:")
        for model in fp16_models:
            print(f"\nTesting {model}:")
            if test_model_basic(model):
                print("✓ Model passed basic test")
            else:
                print("❌ Model failed basic test")
        return
    
    if args.input:
        # Convert single file
        if not os.path.exists(args.input):
            print(f"Error: Input file not found: {args.input}")
            return
        
        result = convert_onnx_to_fp16_fixed(args.input, args.output, args.method)
        if result:
            print(f"\n🎉 Success! Now you can convert to TensorRT FP16:")
            print(f"python convert_onnx_to_tensorrt.py --onnx {result} --fp16")
    else:
        # Convert all models in directory
        convert_all_models(method=args.method)

if __name__ == "__main__":
    main()