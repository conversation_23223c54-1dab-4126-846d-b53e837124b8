import os
import torch
import torch.nn as nn
import argparse
import onnx

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

def export_to_onnx(config_path, checkpoint_dir, onnx_path, input_size=(1280, 1280), opset_version=17):
    try:
        if os.path.exists(onnx_path):
            print(f"✓ ONNX model already exists: {onnx_path}")
            print(f"Model size: {os.path.getsize(onnx_path) / (1024 * 1024):.1f} MB")
            return

        opt = load_config(config_path)

        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        class CustomWrapper(nn.Module):
            def __init__(self, base_model):
                super().__init__()
                self.model = base_model
                self.model.eval()
                if hasattr(self.model, 'forward_inference'):
                    self.model.forward = self.model.forward_inference

            def forward(self, x):
                _, _, H, W = x.shape
                patch_size = 4
                new_H = ((H + patch_size - 1) // patch_size) * patch_size
                new_W = ((W + patch_size - 1) // patch_size) * patch_size

                if new_H != H or new_W != W:
                    pad_H = new_H - H
                    pad_W = new_W - W
                    x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

                output = self.model({'image': x})
                pred = output.get('pred', torch.sigmoid(output['saliency'][3]))

                pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-4)

                return torch.clamp(pred, 0.0, 1.0)

        wrapper = CustomWrapper(model)
        dummy_input = torch.randn(1, 3, *input_size)

        torch.onnx.export(
            wrapper,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output']
        )

        print(f"Model has been successfully exported to {onnx_path}")
        onnx.checker.check_model(onnx.load(onnx_path))
        print(f"ONNX model is valid. Input shape: [1, 3, {input_size[0]}, {input_size[1]}]")

    except Exception as e:
        print(f"Error exporting model: {e}")
        raise

def export_models_for_aspect_ratios(opset_version=17):
    # ATTENTION: Don't take the original model dimensions exactly because it does not work correctly.
    #            Also, with the LR mode, we cannot go smaller then the original model (384x284)
    model_dimensions = [
        (1280, 720), (1280, 960),
        (720, 1280), (960, 1280),
        (1280, 1280), (1056, 1056), (928, 928),
        (800, 800), (672, 672), (480, 480), (400, 400)
    ]

    for height, width in model_dimensions:
        print(f"\n=== Creating model with dimensions {width}x{height} (width x height) ===")

        if height < 500 and width < 500:
            config = "configs/extra_dataset/Plus_Ultra_LR.yaml"
            checkpoint = "snapshots/Plus_Ultra_LR"
        else:
            config = "configs/extra_dataset/Plus_Ultra.yaml"
            checkpoint = "snapshots/Plus_Ultra"

        print(f"Using config: {config}")
        print(f"Using checkpoint: {checkpoint}")

        onnx_path = f"InsPyReNet_{width}x{height}.onnx"
        export_to_onnx(
            config_path=config,
            checkpoint_dir=checkpoint,
            onnx_path=onnx_path,
            input_size=(height, width),
            opset_version=opset_version
        )

if __name__ == "__main__":

    print("ONNX Multi-Aspect Model Creator")
    print("=" * 40)
    export_models_for_aspect_ratios(opset_version=17)
