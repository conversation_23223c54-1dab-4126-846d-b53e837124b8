#!/usr/bin/env python3
"""
Debug script to investigate ONNX model output issues
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import torch.nn.functional as F
import onnxruntime as ort
import numpy as np
import cv2
import sys
from PIL import Image

# Add lib to path for imports
sys.path.append('lib')
from lib import *
from utils.misc import *

def debug_pytorch_vs_onnx():
    """Compare PyTorch model output with ONNX model output"""
    print("🔍 Debugging PyTorch vs ONNX output differences...")
    
    # Load PyTorch model
    print("📋 Loading PyTorch model...")
    from lib.InSPyReNet import InSPyReNet_SwinB

    # Try to load from checkpoint first
    checkpoint_paths = [
        "snapshots/Plus_Ultra/latest.pth",
        "snapshots/Plus_Ultra_LR/latest.pth",
        "snapshots/InSPyReNet_Res2Net50/latest.pth"
    ]

    model_loaded = False
    for checkpoint_path in checkpoint_paths:
        if os.path.exists(checkpoint_path):
            print(f"📋 Loading from checkpoint: {checkpoint_path}")
            try:
                pytorch_model = InSPyReNet_SwinB(depth=64, pretrained=False, base_size=[384, 384])
                checkpoint = torch.load(checkpoint_path, map_location='cpu')
                if 'model' in checkpoint:
                    pytorch_model.load_state_dict(checkpoint['model'])
                else:
                    pytorch_model.load_state_dict(checkpoint)
                print(f"✅ Successfully loaded checkpoint")
                model_loaded = True
                break
            except Exception as e:
                print(f"❌ Failed to load {checkpoint_path}: {e}")
                continue

    if not model_loaded:
        print("📋 Creating model with pretrained backbone weights")
        pytorch_model = InSPyReNet_SwinB(depth=64, pretrained=True, base_size=[384, 384])

    pytorch_model.eval()
    
    # Load input image
    print("🖼️ Loading and preprocessing input image...")
    image = Image.open('input.png').convert('RGB')
    original_size = image.size
    print(f"📐 Original image size: {original_size}")
    
    # Resize to model input size
    image_resized = image.resize((800, 800), Image.LANCZOS)
    
    # Convert to tensor and normalize
    image_array = np.array(image_resized).astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image_array).permute(2, 0, 1).unsqueeze(0)
    
    # Normalize with ImageNet stats
    mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
    image_tensor = (image_tensor - mean) / std
    
    print(f"✅ Input tensor shape: {image_tensor.shape}")
    print(f"📊 Input tensor stats: min={image_tensor.min():.3f}, max={image_tensor.max():.3f}, mean={image_tensor.mean():.3f}")
    
    # Test PyTorch model
    print("\n🔄 Testing PyTorch model...")
    with torch.no_grad():
        pytorch_output = pytorch_model.forward_inspyre(image_tensor)
        print(f"📋 PyTorch output type: {type(pytorch_output)}")
        print(f"📋 PyTorch output keys: {pytorch_output.keys()}")
        
        for key, value in pytorch_output.items():
            if isinstance(value, list):
                print(f"  📋 {key}: list with {len(value)} items")
                for i, item in enumerate(value):
                    if isinstance(item, torch.Tensor):
                        print(f"    📋 [{i}]: {item.shape}, min={item.min():.6f}, max={item.max():.6f}, mean={item.mean():.6f}")
            elif isinstance(value, torch.Tensor):
                print(f"  📋 {key}: {value.shape}, min={value.min():.6f}, max={value.max():.6f}, mean={value.mean():.6f}")
    
    # Test ONNX model
    print("\n🔄 Testing ONNX model...")
    ort_session = ort.InferenceSession('mixed_precision_models/InsPyReNet_800x800_fp32.onnx')
    
    # Get model info
    input_info = ort_session.get_inputs()[0]
    output_info = ort_session.get_outputs()[0]
    print(f"📋 ONNX input: {input_info.name} {input_info.shape}")
    print(f"📋 ONNX output: {output_info.name} {output_info.shape}")
    
    # Run ONNX inference
    onnx_input = {input_info.name: image_tensor.numpy()}
    onnx_output = ort_session.run(None, onnx_input)[0]
    
    print(f"📋 ONNX output shape: {onnx_output.shape}")
    print(f"📊 ONNX output stats: min={onnx_output.min():.6f}, max={onnx_output.max():.6f}, mean={onnx_output.mean():.6f}")
    
    # Compare outputs
    print("\n🔍 Comparing outputs...")
    
    # Get the final saliency output from PyTorch
    pytorch_final = pytorch_output['saliency'][-1]  # d0 is the final output
    print(f"📋 PyTorch final output: {pytorch_final.shape}, min={pytorch_final.min():.6f}, max={pytorch_final.max():.6f}")
    
    # Compare shapes
    if pytorch_final.shape == onnx_output.shape:
        print("✅ Shapes match!")
        
        # Calculate difference
        pytorch_np = pytorch_final.numpy()
        diff = np.abs(pytorch_np - onnx_output)
        print(f"📊 Absolute difference: min={diff.min():.6f}, max={diff.max():.6f}, mean={diff.mean():.6f}")
        
        if diff.max() < 1e-5:
            print("✅ Outputs are very similar!")
        else:
            print("❌ Outputs differ significantly!")
            
    else:
        print(f"❌ Shape mismatch: PyTorch {pytorch_final.shape} vs ONNX {onnx_output.shape}")
    
    # Test if the issue is in postprocessing
    print("\n🔍 Testing postprocessing...")
    
    def postprocess_output(output, target_size):
        """Test postprocessing function"""
        print(f"  📋 Input shape: {output.shape}")
        print(f"  📊 Input stats: min={output.min():.6f}, max={output.max():.6f}, mean={output.mean():.6f}")
        
        # Remove batch dimension
        if len(output.shape) == 4:
            output = output[0]
        if len(output.shape) == 3:
            output = output[0]
        
        print(f"  📋 After squeeze: {output.shape}")
        print(f"  📊 After squeeze stats: min={output.min():.6f}, max={output.max():.6f}, mean={output.mean():.6f}")
        
        # Apply sigmoid if not already applied
        if output.max() > 1.0 or output.min() < 0.0:
            print("  🔄 Applying sigmoid...")
            output = np.clip(output, -10, 10)
            output = 1.0 / (1.0 + np.exp(-output))
            print(f"  📊 After sigmoid stats: min={output.min():.6f}, max={output.max():.6f}, mean={output.mean():.6f}")
        
        # Resize to target size
        if output.shape != target_size:
            print(f"  🔄 Resizing from {output.shape} to {target_size}")
            output_resized = cv2.resize(output, target_size, interpolation=cv2.INTER_LINEAR)
            print(f"  📊 After resize stats: min={output_resized.min():.6f}, max={output_resized.max():.6f}, mean={output_resized.mean():.6f}")
        else:
            output_resized = output
        
        return output_resized
    
    # Test postprocessing on PyTorch output
    print("  🔄 Testing PyTorch postprocessing...")
    pytorch_processed = postprocess_output(pytorch_final.numpy(), original_size)
    
    # Test postprocessing on ONNX output  
    print("  🔄 Testing ONNX postprocessing...")
    onnx_processed = postprocess_output(onnx_output, original_size)
    
    # Save debug outputs
    print("\n💾 Saving debug outputs...")
    
    # Save raw outputs as numpy arrays for inspection
    np.save('debug_pytorch_raw.npy', pytorch_final.numpy())
    np.save('debug_onnx_raw.npy', onnx_output)
    np.save('debug_pytorch_processed.npy', pytorch_processed)
    np.save('debug_onnx_processed.npy', onnx_processed)
    
    # Save as images
    pytorch_img = (np.clip(pytorch_processed, 0, 1) * 255).astype(np.uint8)
    onnx_img = (np.clip(onnx_processed, 0, 1) * 255).astype(np.uint8)
    
    cv2.imwrite('debug_pytorch_output.png', pytorch_img)
    cv2.imwrite('debug_onnx_output.png', onnx_img)
    
    print("✅ Debug outputs saved:")
    print("  📁 debug_pytorch_raw.npy")
    print("  📁 debug_onnx_raw.npy") 
    print("  📁 debug_pytorch_processed.npy")
    print("  📁 debug_onnx_processed.npy")
    print("  📁 debug_pytorch_output.png")
    print("  📁 debug_onnx_output.png")

if __name__ == "__main__":
    print("🚀 Starting ONNX Debug Session")
    print("=" * 50)
    
    debug_pytorch_vs_onnx()
    
    print("\n✅ Debug session completed!")
