import numpy as np
from PIL import Image
import os
import time
import onnx
import onnxruntime as ort
import onnxoptimizer as optimizer

def optimize_onnx_model(input_model_path, output_model_path, input_shape=(1, 3, 1280, 1280)):
    """
    Optimize ONNX model using graph optimizations without type conversion
    Args:
        input_model_path: Path to original ONNX model
        output_model_path: Path to save optimized model
        input_shape: Fixed input shape (batch, channels, height, width)
    """
    # Load the model
    model = onnx.load(input_model_path)
    
    # Update model input shape if needed
    if input_shape is not None:
        model.graph.input[0].type.tensor_type.shape.dim[0].dim_value = input_shape[0]  # batch size
        model.graph.input[0].type.tensor_type.shape.dim[1].dim_value = input_shape[1]  # channels
        model.graph.input[0].type.tensor_type.shape.dim[2].dim_value = input_shape[2]  # height
        model.graph.input[0].type.tensor_type.shape.dim[3].dim_value = input_shape[3]  # width
    
    # Apply basic graph optimizations
    optimized_model = optimizer.optimize(model, 
                                         ['eliminate_identity',
                                          'eliminate_nop_transpose',
                                          'fuse_consecutive_transposes',
                                          'fuse_bn_into_conv',
                                          'fuse_pad_into_conv',
                                          'fuse_add_bias_into_conv'])
    
    # Save the optimized model
    onnx.save(optimized_model, output_model_path)
    print(f"Optimized model saved to {output_model_path}")
    
    return True

def create_onnx_session(model_path, enable_optimization=True, batch_size=1):
    """
    Create an optimized ONNX runtime session with CUDA-specific optimizations
    Args:
        model_path: Path to the ONNX model
        enable_optimization: Whether to enable graph optimization
        batch_size: Target batch size for optimization
    Returns:
        ONNX Runtime InferenceSession
    """
    # Set up session options
    sess_options = ort.SessionOptions()
    
    if enable_optimization:
        # Enable all graph optimizations
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Enable parallel execution
        sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
        
        # Use all available cores
        sess_options.intra_op_num_threads = os.cpu_count()
        
        # Enable CUDA-specific optimizations
        sess_options.enable_profiling = True
        sess_options.enable_mem_pattern = True
        sess_options.enable_cpu_mem_arena = True
        
        # Set CUDA-specific environment variables
        os.environ['ORT_CUDA_CUDNN_conv_workspace_limit_in_MB'] = '1024'
        os.environ['ORT_CUDA_CUDNN_conv_use_max_workspace'] = '1'
        
    # Check for GPU and use it if available
    providers = [
        (
            'CUDAExecutionProvider',
            {
                'device_id': 0,
                'arena_extend_strategy': 'kNextPowerOfTwo',
                'cudnn_conv_algo_search': 'EXHAUSTIVE',
                'do_copy_in_default_stream': True,
                'cudnn_conv_use_max_workspace': 1
            }
        ),
        'CPUExecutionProvider'
    ] if 'CUDAExecutionProvider' in ort.get_available_providers() \
    else ['CPUExecutionProvider']
    
    # Create ONNX Runtime session
    session = ort.InferenceSession(
        model_path,
        sess_options,
        providers=providers
    )
    
    # Log configuration
    print(f"ONNX Runtime using: {session.get_providers()}")
    print("CUDA Configuration:")
    print(f"  - Device ID: 0")
    print(f"  - Convolution Optimization: EXHAUSTIVE")
    
    return session

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    original_size = img.size  # (width, height)
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img, (original_size[1], original_size[0])  # Return (height, width)

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

def check_model_input_info(model_path):
    """
    Check and print information about model inputs and outputs
    Args:
        model_path: Path to the ONNX model
    """
    # Create a minimal session just to query input/output info
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Get input details
    inputs = session.get_inputs()
    print("\nModel Input Details:")
    for i, input_info in enumerate(inputs):
        print(f"Input {i}:")
        print(f"  Name: {input_info.name}")
        print(f"  Shape: {input_info.shape}")
        print(f"  Type: {input_info.type}")
    
    # Get output details
    outputs = session.get_outputs()
    print("\nModel Output Details:")
    for i, output_info in enumerate(outputs):
        print(f"Output {i}:")
        print(f"  Name: {output_info.name}")
        print(f"  Shape: {output_info.shape}")
        print(f"  Type: {output_info.type}")

def test_cuda_performance(session, batch_sizes=[1, 2, 4, 8], input_size=(1280, 1280)):
    """
    Test CUDA performance with different batch sizes
    Args:
        session: ONNX Runtime session
        batch_sizes: List of batch sizes to test
        input_size: Input image size (height, width)
    """
    # Get input and output names
    input_info = session.get_inputs()[0]
    output_info = session.get_outputs()[0]
    input_name = input_info.name
    output_name = output_info.name
    
    # Print model input/output details
    print("\nModel Input Details:")
    print(f"  Name: {input_name}")
    print(f"  Shape: {input_info.shape}")
    print(f"  Type: {input_info.type}")
    print("\nModel Output Details:")
    print(f"  Name: {output_name}")
    print(f"  Shape: {output_info.shape}")
    print(f"  Type: {output_info.type}")
    
    print("\nTesting CUDA performance with different batch sizes:")
    for batch_size in batch_sizes:
        try:
            # Create dummy input with the correct shape
            dummy_input = np.random.randn(batch_size, 3, *input_size).astype(np.float32)
            
            # Check if the input shape is compatible
            if len(input_info.shape) == 4 and input_info.shape[0] == 1:
                print(f"\nSkipping batch size {batch_size} because model expects batch size 1")
                continue
            
            # Warmup
            for _ in range(5):
                session.run([output_name], {input_name: dummy_input})
            
            # Measure performance
            start_time = time.time()
            for _ in range(5):  
                session.run([output_name], {input_name: dummy_input})
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 5  
            print(f"Batch size {batch_size}: Average inference time = {avg_time:.4f} seconds")
            
        except Exception as e:
            print(f"\nError with batch size {batch_size}:")
            print(f"Error: {e}")

if __name__ == "__main__":
    input_model = "model.onnx"
    optimized_model = "model_optimized.onnx"
    
    # Optimize the model
    optimize_onnx_model(input_model, optimized_model)
    
    # Create optimized session
    sess = create_onnx_session(optimized_model)
    
    # Test CUDA performance with different batch sizes
    test_cuda_performance(sess)
    
    # Verify model input info
    check_model_input_info(optimized_model)
    
    # Get input name from session metadata
    input_name = sess.get_inputs()[0].name
    
    # Process an image
    image_path = "input.png"
    img, original_size = preprocess_image(image_path)
    
    # Run inference
    for i in range(5):  # Runs the loop 5 times
        print(f"\nRunning inference {i+1}/5...")
        start_time = time.time()
        outputs = sess.run(None, {input_name: img})
        end_time = time.time()
        print(f"Inference {i+1} completed in {end_time - start_time:.4f} seconds")
    
    # Get alpha matte
    alpha = postprocess_output(outputs, original_size)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")
    
    print(f"Inference time: {end_time - start_time:.4f} seconds")
    print(f"Alpha matte saved to output_alpha.png")