#!/usr/bin/env python3
"""
Debug script to identify the source of the "too many indices for tensor of dimension 4" error
"""

import torch
import torch.nn as nn
import sys
import traceback

# Add lib to path for imports
sys.path.append('lib')
from lib import *
from utils.misc import *

def debug_forward_pass():
    """Debug the forward pass to find the indexing issue"""
    print("🔍 Debugging InSPyReNet forward pass...")
    
    try:
        # Load model
        print("📋 Loading model...")
        from lib.InSPyReNet import InSPyReNet_SwinB
        model = InSPyReNet_SwinB(depth=64, pretrained=False, base_size=[384, 384])
        model.eval()
        print("✅ Model loaded successfully")
        
        # Create test input
        print("📐 Creating test input...")
        test_input = torch.randn(1, 3, 800, 800)
        print(f"✅ Test input created: {test_input.shape}")
        
        # Test forward pass with detailed error tracking
        print("🔄 Testing forward pass...")
        with torch.no_grad():
            try:
                # Test the forward_inspyre method directly
                print("  🔍 Testing forward_inspyre...")
                output = model.forward_inspyre(test_input)
                print(f"  ✅ forward_inspyre successful: {type(output)}")
                
            except Exception as e:
                print(f"  ❌ forward_inspyre failed: {e}")
                print("  📋 Full traceback:")
                traceback.print_exc()
                
                # Try to isolate the issue by testing components
                print("\n🔍 Testing individual components...")
                
                try:
                    print("  🔍 Testing backbone...")
                    x1, x2, x3, x4, x5 = model.backbone(test_input)
                    print(f"  ✅ Backbone successful: {[x.shape for x in [x1, x2, x3, x4, x5]]}")
                    
                    print("  🔍 Testing context modules...")
                    x1_ctx = model.context1(x1)
                    print(f"  ✅ Context1 successful: {x1_ctx.shape}")
                    
                    x2_ctx = model.context2(x2)
                    print(f"  ✅ Context2 successful: {x2_ctx.shape}")
                    
                    x3_ctx = model.context3(x3)
                    print(f"  ✅ Context3 successful: {x3_ctx.shape}")
                    
                    x4_ctx = model.context4(x4)
                    print(f"  ✅ Context4 successful: {x4_ctx.shape}")
                    
                    x5_ctx = model.context5(x5)
                    print(f"  ✅ Context5 successful: {x5_ctx.shape}")
                    
                    print("  🔍 Testing decoder...")
                    f3, d3 = model.decoder([x3_ctx, x4_ctx, x5_ctx])
                    print(f"  ✅ Decoder successful: f3={f3.shape}, d3={d3.shape}")
                    
                except Exception as e2:
                    print(f"  ❌ Component test failed: {e2}")
                    print("  📋 Component traceback:")
                    traceback.print_exc()
                
                return False
        
        print("✅ Forward pass successful!")
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        print("📋 Full traceback:")
        traceback.print_exc()
        return False

def test_simple_inference():
    """Test simple inference format"""
    print("\n🔍 Testing simple inference format...")
    
    try:
        from lib.InSPyReNet import InSPyReNet_SwinB
        model = InSPyReNet_SwinB(depth=64, pretrained=False, base_size=[384, 384])
        model.eval()
        
        # Create sample in the format expected by forward_inference
        sample = {
            'image': torch.randn(1, 3, 800, 800)
        }
        
        print("🔄 Testing forward_inference...")
        with torch.no_grad():
            result = model.forward_inference(sample)
            print(f"✅ forward_inference successful: {result.keys()}")
            return True
            
    except Exception as e:
        print(f"❌ Simple inference failed: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        return False

def test_with_smaller_input():
    """Test with smaller input size"""
    print("\n🔍 Testing with smaller input size...")
    
    try:
        from lib.InSPyReNet import InSPyReNet_SwinB
        model = InSPyReNet_SwinB(depth=64, pretrained=False, base_size=[384, 384])
        model.eval()
        
        # Test with smaller input
        test_input = torch.randn(1, 3, 384, 384)
        
        print("🔄 Testing with 384x384 input...")
        with torch.no_grad():
            output = model.forward_inspyre(test_input)
            print(f"✅ Small input successful: {type(output)}")
            return True
            
    except Exception as e:
        print(f"❌ Small input failed: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting InSPyReNet Debug Session")
    print("=" * 50)
    
    # Test 1: Debug forward pass
    success1 = debug_forward_pass()
    
    # Test 2: Test simple inference
    success2 = test_simple_inference()
    
    # Test 3: Test with smaller input
    success3 = test_with_smaller_input()
    
    print("\n📊 Debug Summary:")
    print(f"  • Forward pass debug: {'✅' if success1 else '❌'}")
    print(f"  • Simple inference: {'✅' if success2 else '❌'}")
    print(f"  • Smaller input: {'✅' if success3 else '❌'}")
    
    if not any([success1, success2, success3]):
        print("\n❌ All tests failed - there's a fundamental issue with the model")
        sys.exit(1)
    else:
        print("\n✅ At least one test passed - issue may be input size related")
