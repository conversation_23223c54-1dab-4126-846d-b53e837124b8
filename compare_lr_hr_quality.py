#!/usr/bin/env python3
"""
Compare LR vs HR Quality for Different Image Sizes

This script tests the quality difference between LR and HR processing
for various image sizes, especially around the 800x800 threshold.

Usage:
    python compare_lr_hr_quality.py --source input.png
    python compare_lr_hr_quality.py --test-sizes
"""

import os
import sys
import argparse
import numpy as np
import onnxruntime as ort
from PIL import Image
import time

def create_test_image(size, pattern='gradient'):
    """Create a test image of specified size"""
    w, h = size
    
    if pattern == 'gradient':
        # Create a gradient pattern
        img = np.zeros((h, w, 3), dtype=np.uint8)
        for i in range(h):
            for j in range(w):
                img[i, j] = [
                    int(255 * i / h),           # Red gradient
                    int(255 * j / w),           # Green gradient
                    int(255 * (i + j) / (h + w))  # Blue gradient
                ]
    elif pattern == 'checkerboard':
        # Create checkerboard pattern
        img = np.zeros((h, w, 3), dtype=np.uint8)
        square_size = min(w, h) // 16
        for i in range(h):
            for j in range(w):
                if ((i // square_size) + (j // square_size)) % 2:
                    img[i, j] = [255, 255, 255]
                else:
                    img[i, j] = [0, 0, 0]
    
    return Image.fromarray(img)

def preprocess_image(img, target_size):
    """Preprocess image for ONNX inference"""
    # Resize to target size
    img = img.resize(target_size[::-1], Image.BILINEAR)  # target_size is (H,W), resize expects (W,H)
    
    # Convert to numpy and normalize
    img = np.array(img, dtype=np.float32) / 255.0
    img = (img - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
    
    # CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def postprocess_output(output):
    """Postprocess ONNX output"""
    alpha = output[0].squeeze()
    
    # Handle FP16 conversion
    if alpha.dtype == np.float16:
        alpha = alpha.astype(np.float32)
    
    # Check for NaN/Inf
    if np.isnan(alpha).any() or np.isinf(alpha).any():
        alpha = np.nan_to_num(alpha, nan=0.0, posinf=1.0, neginf=0.0)
    
    # Apply sigmoid if needed
    if np.min(alpha) < -1 or np.max(alpha) > 2:
        alpha = 1.0 / (1.0 + np.exp(-np.clip(alpha, -500, 500)))
    
    # Normalize
    alpha_min, alpha_max = np.min(alpha), np.max(alpha)
    if alpha_max > alpha_min:
        alpha = (alpha - alpha_min) / (alpha_max - alpha_min + 1e-8)
    
    alpha = np.clip(alpha, 0.0, 1.0)
    
    return alpha

def run_inference(session, img_tensor):
    """Run inference and measure time"""
    input_name = session.get_inputs()[0].name
    
    start_time = time.time()
    outputs = session.run(None, {input_name: img_tensor})
    inference_time = time.time() - start_time
    
    alpha = postprocess_output(outputs)
    
    return alpha, inference_time

def calculate_quality_metrics(alpha1, alpha2):
    """Calculate quality difference metrics between two alpha mattes"""
    # Resize to same size for comparison
    if alpha1.shape != alpha2.shape:
        h, w = min(alpha1.shape[0], alpha2.shape[0]), min(alpha1.shape[1], alpha2.shape[1])
        alpha1_resized = np.array(Image.fromarray((alpha1 * 255).astype(np.uint8)).resize((w, h))) / 255.0
        alpha2_resized = np.array(Image.fromarray((alpha2 * 255).astype(np.uint8)).resize((w, h))) / 255.0
    else:
        alpha1_resized, alpha2_resized = alpha1, alpha2
    
    # Calculate metrics
    mse = np.mean((alpha1_resized - alpha2_resized) ** 2)
    mae = np.mean(np.abs(alpha1_resized - alpha2_resized))
    
    # PSNR
    if mse > 0:
        psnr = 20 * np.log10(1.0 / np.sqrt(mse))
    else:
        psnr = float('inf')
    
    # Structural similarity (simplified)
    mean1, mean2 = np.mean(alpha1_resized), np.mean(alpha2_resized)
    var1, var2 = np.var(alpha1_resized), np.var(alpha2_resized)
    covar = np.mean((alpha1_resized - mean1) * (alpha2_resized - mean2))
    
    c1, c2 = 0.01**2, 0.03**2
    ssim = ((2 * mean1 * mean2 + c1) * (2 * covar + c2)) / ((mean1**2 + mean2**2 + c1) * (var1 + var2 + c2))
    
    return {
        'mse': mse,
        'mae': mae,
        'psnr': psnr,
        'ssim': ssim,
        'max_diff': np.max(np.abs(alpha1_resized - alpha2_resized))
    }

def compare_models(image_path_or_size, lr_model='Plus_Ultra_800x800.onnx', hr_model='Plus_Ultra_1280x720.onnx'):
    """Compare LR vs HR model quality"""
    
    # Load or create image
    if isinstance(image_path_or_size, str) and os.path.exists(image_path_or_size):
        img = Image.open(image_path_or_size).convert('RGB')
        image_source = f"file: {image_path_or_size}"
    elif isinstance(image_path_or_size, tuple):
        img = create_test_image(image_path_or_size, 'gradient')
        image_source = f"generated: {image_path_or_size[0]}x{image_path_or_size[1]}"
    else:
        print(f"Invalid image source: {image_path_or_size}")
        return None
    
    original_size = img.size
    print(f"\n📊 Testing image: {image_source}")
    print(f"Original size: {original_size}")
    
    # Check if models exist
    if not os.path.exists(lr_model):
        print(f"❌ LR model not found: {lr_model}")
        return None
    if not os.path.exists(hr_model):
        print(f"❌ HR model not found: {hr_model}")
        return None
    
    try:
        # Load models
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        lr_session = ort.InferenceSession(lr_model, providers=providers)
        hr_session = ort.InferenceSession(hr_model, providers=providers)
        
        print(f"✓ Models loaded")
        
        # Get model input sizes
        lr_input_shape = lr_session.get_inputs()[0].shape
        hr_input_shape = hr_session.get_inputs()[0].shape
        
        lr_size = (lr_input_shape[2], lr_input_shape[3])  # (H, W)
        hr_size = (hr_input_shape[2], hr_input_shape[3])  # (H, W)
        
        print(f"LR model input size: {lr_size}")
        print(f"HR model input size: {hr_size}")
        
        # Preprocess for both models
        lr_img = preprocess_image(img, lr_size)
        hr_img = preprocess_image(img, hr_size)
        
        # Run inference
        print(f"🚀 Running LR inference...")
        lr_alpha, lr_time = run_inference(lr_session, lr_img)
        
        print(f"🚀 Running HR inference...")
        hr_alpha, hr_time = run_inference(hr_session, hr_img)
        
        # Calculate quality metrics
        metrics = calculate_quality_metrics(lr_alpha, hr_alpha)
        
        # Results
        results = {
            'original_size': original_size,
            'lr_model': lr_model,
            'hr_model': hr_model,
            'lr_size': lr_size,
            'hr_size': hr_size,
            'lr_time': lr_time,
            'hr_time': hr_time,
            'speed_ratio': hr_time / lr_time,
            'metrics': metrics
        }
        
        return results
        
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return None

def print_results(results):
    """Print comparison results"""
    if not results:
        return
    
    print(f"\n📈 RESULTS")
    print(f"=" * 50)
    print(f"Original size: {results['original_size']}")
    print(f"LR model: {results['lr_model']} ({results['lr_size']})")
    print(f"HR model: {results['hr_model']} ({results['hr_size']})")
    
    print(f"\n⏱️ Performance:")
    print(f"LR time: {results['lr_time']:.4f}s")
    print(f"HR time: {results['hr_time']:.4f}s")
    print(f"Speed ratio: {results['speed_ratio']:.2f}x (HR is {results['speed_ratio']:.2f}x slower)")
    
    print(f"\n🎯 Quality Metrics:")
    metrics = results['metrics']
    print(f"MSE: {metrics['mse']:.6f}")
    print(f"MAE: {metrics['mae']:.6f}")
    print(f"PSNR: {metrics['psnr']:.2f} dB")
    print(f"SSIM: {metrics['ssim']:.4f}")
    print(f"Max difference: {metrics['max_diff']:.4f}")
    
    # Quality assessment
    if metrics['mae'] < 0.01:
        quality_assessment = "🟢 Virtually identical"
    elif metrics['mae'] < 0.05:
        quality_assessment = "🟡 Very similar"
    elif metrics['mae'] < 0.1:
        quality_assessment = "🟠 Noticeable difference"
    else:
        quality_assessment = "🔴 Significant difference"
    
    print(f"\n📊 Quality Assessment: {quality_assessment}")
    
    # Recommendation
    if results['original_size'][0] <= 800 and results['original_size'][1] <= 800:
        if metrics['mae'] < 0.02:
            recommendation = "✅ Use LR model - virtually no quality loss with much better speed"
        else:
            recommendation = "⚖️ LR model recommended for speed, HR for max quality"
    else:
        recommendation = "🎯 HR model recommended for large images"
    
    print(f"💡 Recommendation: {recommendation}")

def main():
    parser = argparse.ArgumentParser(description='Compare LR vs HR quality')
    parser.add_argument('--source', '-s', type=str, help='Input image path')
    parser.add_argument('--test-sizes', action='store_true', help='Test various image sizes')
    parser.add_argument('--lr-model', type=str, default='Plus_Ultra_800x800.onnx', help='LR model path')
    parser.add_argument('--hr-model', type=str, default='Plus_Ultra_1280x720.onnx', help='HR model path')
    
    args = parser.parse_args()
    
    print("LR vs HR Quality Comparison")
    print("=" * 40)
    
    if args.test_sizes:
        # Test various sizes around the threshold
        test_sizes = [
            (400, 400),   # Small
            (600, 600),   # Medium-small
            (800, 800),   # Threshold
            (1000, 1000), # Medium-large
            (1280, 1280), # Large
            (1600, 1200), # Very large
        ]
        
        print("Testing multiple image sizes...")
        
        for size in test_sizes:
            results = compare_models(size, args.lr_model, args.hr_model)
            if results:
                print_results(results)
                print("-" * 50)
    
    elif args.source:
        results = compare_models(args.source, args.lr_model, args.hr_model)
        if results:
            print_results(results)
    
    else:
        # Default: test 800x800
        print("Testing 800x800 image (default)...")
        results = compare_models((800, 800), args.lr_model, args.hr_model)
        if results:
            print_results(results)

if __name__ == "__main__":
    main()
