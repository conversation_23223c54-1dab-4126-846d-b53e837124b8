#!/usr/bin/env python3
"""
Simple TensorRT FP16 Test

This script directly tests the TensorRT FP16 engine to verify it's working correctly.

Usage:
    conda activate onnxgpu
    python test_tensorrt_fp16_simple.py
"""

import os
import sys
import numpy as np
from PIL import Image

def test_tensorrt_fp16_engine_direct():
    """
    Direct test of TensorRT FP16 engine using the TensorRT Python API
    """
    engine_path = "InsPyReNet_800x800_fp16.engine"
    test_image = "input.png"
    
    print(f"Testing TensorRT FP16 engine: {engine_path}")
    
    if not os.path.exists(engine_path):
        print(f"❌ Engine not found: {engine_path}")
        return False
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return False
    
    try:
        import tensorrt as trt
        import pycuda.driver as cuda
        import pycuda.autoinit
        
        print("✓ TensorRT and PyCUDA available")
        
        # Load engine
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        if engine is None:
            print("❌ Failed to deserialize engine")
            return False
        
        print("✓ Engine loaded successfully")
        
        # Create execution context
        context = engine.create_execution_context()
        
        # Get input/output info
        input_binding = 0
        output_binding = 1
        
        input_shape = engine.get_binding_shape(input_binding)
        output_shape = engine.get_binding_shape(output_binding)
        
        print(f"Input shape: {input_shape}")
        print(f"Output shape: {output_shape}")
        
        # Preprocess image
        print("Preprocessing image...")
        img = Image.open(test_image).convert('RGB')
        img = img.resize((input_shape[3], input_shape[2]), Image.BILINEAR)
        
        # Convert to numpy array as float32
        img = np.array(img, dtype=np.float32) / 255.0
        
        # Normalization
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img = (img - mean) / std
        
        # Transpose and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        img = img.astype(np.float32)
        
        print("✓ Image preprocessed")
        
        # Allocate GPU memory
        input_size = trt.volume(input_shape) * np.dtype(np.float32).itemsize
        output_size = trt.volume(output_shape) * np.dtype(np.float32).itemsize
        
        # Allocate device memory
        d_input = cuda.mem_alloc(input_size)
        d_output = cuda.mem_alloc(output_size)
        
        # Create stream
        stream = cuda.Stream()
        
        # Copy input to device
        cuda.memcpy_htod_async(d_input, img, stream)
        
        # Run inference
        print("Running TensorRT FP16 inference...")
        context.execute_async_v2(bindings=[int(d_input), int(d_output)], stream_handle=stream.handle)
        
        # Copy output back to host
        output = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh_async(output, d_output, stream)
        stream.synchronize()
        
        print("✓ TensorRT FP16 inference completed")
        
        # Analyze output
        output = output.squeeze()
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"\nTensorRT FP16 Output Analysis:")
        print(f"  Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"  Mean: {output_mean:.4f}, Std: {output_std:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        
        # Determine if model is working correctly
        if nan_count > 0 or inf_count > 0:
            print("❌ FAIL: Contains NaN or Inf values")
            return False
        elif white_percentage > 95:
            print("❌ FAIL: White alpha matte issue detected")
            return False
        elif output_std < 0.01:
            print("❌ FAIL: No contrast (flat output)")
            return False
        else:
            print("✅ PASS: TensorRT FP16 output looks good")
            
            # Save test output
            output_img = (output * 255).astype(np.uint8)
            test_output_path = "test_output_tensorrt_fp16_direct.png"
            Image.fromarray(output_img).save(test_output_path)
            print(f"✓ TensorRT FP16 output saved: {test_output_path}")
            
            return True
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("TensorRT Python API or PyCUDA not available")
        return False
    except Exception as e:
        print(f"❌ TensorRT test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_fp32_vs_tensorrt_fp16():
    """
    Compare FP32 reference with TensorRT FP16 output
    """
    fp32_output = "test_output_fp32_reference.png"
    fp16_output = "test_output_tensorrt_fp16_direct.png"
    
    print(f"\nComparing FP32 vs TensorRT FP16 outputs...")
    
    if not os.path.exists(fp32_output):
        print(f"❌ FP32 reference not found: {fp32_output}")
        return False
    
    if not os.path.exists(fp16_output):
        print(f"❌ TensorRT FP16 output not found: {fp16_output}")
        return False
    
    try:
        # Load both images
        img_fp32 = np.array(Image.open(fp32_output))
        img_fp16 = np.array(Image.open(fp16_output))
        
        print(f"FP32 output shape: {img_fp32.shape}")
        print(f"FP16 output shape: {img_fp16.shape}")
        
        # Calculate difference
        if img_fp32.shape == img_fp16.shape:
            diff = np.abs(img_fp32.astype(np.float32) - img_fp16.astype(np.float32))
            max_diff = np.max(diff)
            mean_diff = np.mean(diff)
            
            print(f"Difference statistics:")
            print(f"  Max difference: {max_diff:.2f}")
            print(f"  Mean difference: {mean_diff:.2f}")
            
            # Save difference image
            diff_img = (np.clip(diff * 5, 0, 255)).astype(np.uint8)  # Amplify differences
            Image.fromarray(diff_img).save("difference_fp32_vs_fp16.png")
            print(f"✓ Difference image saved: difference_fp32_vs_fp16.png")
            
            if max_diff < 10:  # Less than 10 pixel values difference
                print("✅ EXCELLENT: Outputs are very similar")
                return True
            elif max_diff < 30:
                print("✅ GOOD: Minor differences, acceptable for FP16")
                return True
            elif max_diff < 50:
                print("⚠️ CAUTION: Some differences detected but may be acceptable")
                return True
            else:
                print("❌ SIGNIFICANT: Large differences detected")
                return False
        else:
            print("❌ FAIL: Output shapes don't match")
            return False
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return False

def main():
    print("Simple TensorRT FP16 Test")
    print("=" * 40)
    
    # Test TensorRT FP16 engine directly
    print("Testing TensorRT FP16 engine...")
    
    if not test_tensorrt_fp16_engine_direct():
        print("\n❌ TensorRT FP16 test failed")
        print("\nTroubleshooting:")
        print("1. Make sure you have TensorRT Python API installed")
        print("2. Make sure you have PyCUDA installed")
        print("3. Make sure CUDA is available")
        return 1
    
    # Compare outputs
    print("\n" + "="*50)
    print("Comparing FP32 vs TensorRT FP16...")
    
    comparison_success = compare_fp32_vs_tensorrt_fp16()
    
    # Final result
    print(f"\n{'🎉'*20}")
    print("TENSORRT FP16 TEST COMPLETE")
    print('🎉'*20)
    
    if comparison_success:
        print("✅ SUCCESS: TensorRT FP16 is working correctly!")
        print("✅ Output quality is maintained")
    else:
        print("⚠️ TensorRT FP16 is working but with some differences")
        print("This may be acceptable depending on your use case")
    
    print(f"\nFiles created:")
    print(f"  ✅ InsPyReNet_800x800_fp16.engine (TensorRT FP16 engine)")
    print(f"  ✅ test_output_fp32_reference.png (FP32 reference)")
    print(f"  ✅ test_output_tensorrt_fp16_direct.png (TensorRT FP16 output)")
    print(f"  ✅ difference_fp32_vs_fp16.png (Difference visualization)")
    
    print(f"\nYour TensorRT FP16 engine is ready for production use!")
    print(f"Use it with: python InferenceTensorRT.py --source input.png --engine InsPyReNet_800x800_fp16.engine")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())