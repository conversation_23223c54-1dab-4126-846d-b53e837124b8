#!/usr/bin/env python3
"""
Memory-Efficient ONNX to TensorRT Conversion Script

This script converts ONNX models to TensorRT engines using trtexec,
which is much more memory-efficient than loading PyTorch models.

Usage:
    python convert_onnx_to_tensorrt.py
    python convert_onnx_to_tensorrt.py --onnx Plus_Ultra_800x800.onnx
    python convert_onnx_to_tensorrt.py --onnx Plus_Ultra_800x800.onnx --fp16
"""

import os
import sys
import argparse
import subprocess
import glob

def find_onnx_models():
    """Find available ONNX models in the current directory"""
    onnx_files = glob.glob("*.onnx")
    return sorted(onnx_files)

def find_trtexec():
    """Find trtexec executable"""
    # Common locations for trtexec
    possible_paths = [
        "trtexec",  # If in PATH
        "trtexec.exe",  # Windows
        "/usr/src/tensorrt/bin/trtexec",  # Docker
        "/opt/tensorrt/bin/trtexec",  # Linux install
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\TensorRT\\bin\\trtexec.exe",  # Windows install
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def convert_onnx_to_tensorrt(onnx_path, engine_path, use_fp16=True, max_workspace_size="4G"):
    """Convert ONNX model to TensorRT engine using trtexec"""
    
    trtexec_path = find_trtexec()
    if not trtexec_path:
        print("❌ trtexec not found!")
        print("Please install TensorRT and ensure trtexec is in your PATH")
        print("Or download TensorRT from: https://developer.nvidia.com/tensorrt")
        return False
    
    print(f"✓ Found trtexec: {trtexec_path}")
    
    # Build trtexec command with correct syntax for TensorRT 10.x
    cmd = [
        trtexec_path,
        f"--onnx={onnx_path}",
        f"--saveEngine={engine_path}",
        "--verbose"
    ]

    # Add workspace size with correct syntax for TensorRT 10.x
    if max_workspace_size.upper().endswith('G'):
        workspace_mb = int(max_workspace_size[:-1]) * 1024
    elif max_workspace_size.upper().endswith('M'):
        workspace_mb = int(max_workspace_size[:-1])
    else:
        workspace_mb = int(max_workspace_size) // (1024 * 1024)

    cmd.append(f"--memPoolSize=workspace:{workspace_mb}")

    if use_fp16:
        cmd.append("--fp16")
        print("Using FP16 precision for better performance and lower memory usage")

    # First, let's try to detect the actual input name from the ONNX model
    try:
        import onnx
        model = onnx.load(onnx_path)
        input_name = model.graph.input[0].name
        print(f"Detected input name: {input_name}")
    except:
        input_name = "input"  # fallback
        print(f"Using fallback input name: {input_name}")

    print(f"Converting {onnx_path} to {engine_path}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3000)
        
        if result.returncode == 0:
            print("✅ Conversion successful!")
            if os.path.exists(engine_path):
                size_mb = os.path.getsize(engine_path) / (1024 * 1024)
                print(f"Engine file created: {engine_path} ({size_mb:.1f} MB)")
                return True
            else:
                print("❌ Engine file was not created")
                return False
        else:
            print("❌ Conversion failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Conversion timed out (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False

def test_engine(engine_path):
    """Test the created engine"""
    try:
        import tensorrt as trt
        
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        if engine:
            print(f"✅ Engine test successful!")
            #print(f"Engine has {engine.num_bindings} bindings")
            return True
        else:
            print("❌ Engine test failed - could not deserialize")
            return False
            
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Convert ONNX models to TensorRT engines (memory-efficient)')
    parser.add_argument('--onnx', type=str, default="InsPyReNet_1280x720.onnx", help='ONNX model file to convert')
    parser.add_argument('--engine', type=str, default="InsPyReNet_1280x720.engine", help='Output TensorRT engine file')
    parser.add_argument('--fp16', action='store_true', default=False, help='Use FP16 precision (default: True)')
    parser.add_argument('--fp32', action='store_true', default=True, help='Use FP32 precision instead of FP16')
    parser.add_argument('--workspace', type=str, default="2G", help='Max workspace size (default: 2G)')
    parser.add_argument('--test', action='store_true', default=True, help='Test the engine after conversion')
    parser.add_argument('--list', action='store_true', help='List available ONNX models')
    
    args = parser.parse_args()
    
    print("ONNX to TensorRT Converter (Memory-Efficient)")
    print("=" * 50)
    
    # List available ONNX models
    onnx_models = find_onnx_models()
    if args.list or not args.onnx:
        print("Available ONNX models:")
        if onnx_models:
            for i, model in enumerate(onnx_models, 1):
                size_mb = os.path.getsize(model) / (1024 * 1024)
                print(f"  {i}. {model} ({size_mb:.1f} MB)")
        else:
            print("  No ONNX models found in current directory")
        
        if args.list:
            return 0
        
        if not args.onnx and onnx_models:
            print(f"\nUsing first available model: {onnx_models[0]}")
            args.onnx = onnx_models[0]
        elif not args.onnx:
            print("No ONNX model specified and none found!")
            return 1
    
    print(os.getcwd())

    # Validate ONNX file
    if not os.path.exists(args.onnx):
        print(f"❌ ONNX file not found: {args.onnx}")
        return 1
    
    # Set output engine path
    if not args.engine:
        base_name = os.path.splitext(args.onnx)[0]
        args.engine = f"{base_name}.plan"
    
    # Handle FP32 override
    use_fp16 = args.fp16 and not args.fp32
    
    print(f"Input ONNX: {args.onnx}")
    print(f"Output Engine: {args.engine}")
    print(f"Precision: {'FP16' if use_fp16 else 'FP32'}")
    print(f"Workspace: {args.workspace}")
    
    # Convert
    success = convert_onnx_to_tensorrt(
        args.onnx, 
        args.engine, 
        use_fp16=use_fp16,
        max_workspace_size=args.workspace
    )
    
    if success and args.test:
        print("\nTesting engine...")
        test_engine(args.engine)
    
    if success:
        print(f"\n🎉 Success! You can now use:")
        print(f"python run/InferenceTensorRT.py --source input.png --engine {args.engine}")
        return 0
    else:
        print(f"\n❌ Conversion failed. You can still use:")
        print(f"python run/Inference.py --source input.png")
        return 1

if __name__ == "__main__":
    sys.exit(main())
