#!/usr/bin/env python3
"""
Regenerate FP16-Safe ONNX Models

This script regenerates ONNX models with FP16-safe modifications to avoid
numerical issues when converting to TensorRT FP16.

Key improvements:
1. Uses FP16-safe epsilon values (1e-4 instead of 1e-8)
2. Avoids problematic min/max normalization in FP16
3. Applies safer clamping and normalization strategies
4. Outputs models ready for stable FP16 TensorRT conversion

Usage:
    python regenerate_fp16_safe_models.py
    python regenerate_fp16_safe_models.py --config configs/extra_dataset/Plus_Ultra.yaml
"""

import os
import torch
import torch.nn as nn
import argparse
import onnx

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

def export_fp16_safe_onnx(config_path, checkpoint_dir, onnx_path, input_size=(1280, 1280), opset_version=17):
    """
    Export PyTorch model to FP16-safe ONNX format
    """
    try:
        # Check if ONNX file already exists
        if os.path.exists(onnx_path):
            print(f"✓ ONNX model already exists: {onnx_path}")
            print(f"Model size: {os.path.getsize(onnx_path) / (1024 * 1024):.1f} MB")
            return

        # Load configuration
        opt = load_config(config_path)

        # Load model
        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        # Create FP16-safe wrapper
        class FP16SafeWrapper(nn.Module):
            def __init__(self, base_model):
                super().__init__()
                self.model = base_model
                self.model.eval()
                if hasattr(self.model, 'forward_inference'):
                    self.model.forward = self.model.forward_inference

            def forward(self, x):
                # Get dimensions
                _, _, H, W = x.shape

                # Ensure dimensions are divisible by patch size (4)
                patch_size = 4
                new_H = ((H + patch_size - 1) // patch_size) * patch_size
                new_W = ((W + patch_size - 1) // patch_size) * patch_size
                
                # Pad if needed
                if new_H != H or new_W != W:
                    pad_H = new_H - H
                    pad_W = new_W - W
                    x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

                # Forward pass - let the model do its own processing
                output = self.model({'image': x})
                
                # The model already returns 'pred' which should be the processed output
                # Let's use that directly instead of trying to reprocess the raw saliency
                if 'pred' in output:
                    pred = output['pred']
                else:
                    # Fallback: get raw saliency and apply minimal processing
                    pred_raw = output['saliency'][3]  # d0 is the final output
                    pred = torch.sigmoid(pred_raw)
                
                # The issue might be that we're over-normalizing
                # Let's try a much simpler approach that preserves the original values better
                
                # Just ensure values are in [0,1] range without aggressive normalization
                pred = torch.clamp(pred, 0.0, 1.0)
                
                # Optional: Apply a very gentle contrast enhancement only if needed
                # Check if the prediction has reasonable contrast
                pred_std = torch.std(pred)
                
                # Only apply normalization if the standard deviation is very low (flat image)
                if pred_std < 0.1:  # Very flat prediction, needs enhancement
                    pred_min = torch.min(pred)
                    pred_max = torch.max(pred)
                    pred_range = pred_max - pred_min
                    
                    # Use a larger epsilon for FP16 safety
                    epsilon = 1e-3
                    
                    if pred_range > epsilon:
                        # Gentle normalization
                        pred = (pred - pred_min) / (pred_range + epsilon)
                    
                    # Final clamp
                    pred = torch.clamp(pred, 0.0, 1.0)
                
                return pred

        # Wrap the model
        wrapper = FP16SafeWrapper(model)

        # Create dummy input tensor
        dummy_input = torch.randn(1, 3, *input_size)

        # Export to ONNX with FP16-safe settings
        torch.onnx.export(
            wrapper,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            # Use dynamic axes to be more flexible
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            } if opset_version >= 11 else None
        )

        print(f"Model has been successfully exported to {onnx_path}")

        # Check the model
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX model is valid and has been successfully loaded.")
        
        # Print model info
        print(f"Model size: {os.path.getsize(onnx_path) / (1024 * 1024):.1f} MB")
        print(f"Input shape: [1, 3, {input_size[0]}, {input_size[1]}]")
        print("✓ Model is FP16-safe and ready for TensorRT FP16 conversion")

    except Exception as e:
        print(f"Error exporting model: {e}")
        raise

def export_fp16_safe_models(config_path, checkpoint_dir, opset_version=17):
    """
    Export FP16-safe ONNX models for different aspect ratios
    """
    # Define model dimensions as (height, width)
    model_dimensions = [
        (720, 1280),   # 16:9 landscape
        (960, 1280),   # 4:3 landscape
        (1280, 720),   # 9:16 portrait
        (1280, 960),   # 3:4 portrait
        (800, 800)     # 1:1 square
    ]
    
    # Export a model for each dimension
    for height, width in model_dimensions:
        print(f"\n=== Creating FP16-safe model {width}x{height} ===")
        onnx_path = f"InsPyReNet_{width}x{height}_fp16safe.onnx"
        
        export_fp16_safe_onnx(
            config_path=config_path,
            checkpoint_dir=checkpoint_dir,
            onnx_path=onnx_path,
            input_size=(height, width),
            opset_version=opset_version
        )
        
        print(f"✓ FP16-safe model for {width}x{height} created successfully\n")

def test_fp16_safe_model(model_path):
    """Test the FP16-safe model with dummy input"""
    try:
        import onnxruntime as ort
        import numpy as np
        
        print(f"Testing FP16-safe model: {model_path}")
        
        # Create session
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        # Handle dynamic shapes
        if isinstance(input_shape[0], str) or input_shape[0] is None:
            input_shape = [1] + list(input_shape[1:])
        
        # Create test input
        test_input = np.random.randn(*input_shape).astype(np.float32) * 0.5
        
        # Run inference
        outputs = session.run(None, {input_info.name: test_input})
        
        # Check output
        output = outputs[0]
        print(f"  Output shape: {output.shape}")
        print(f"  Output dtype: {output.dtype}")
        print(f"  Output range: [{np.min(output):.6f}, {np.max(output):.6f}]")
        print(f"  Output mean: {np.mean(output):.6f}")
        
        # Check for numerical issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        
        if nan_count > 0:
            print(f"  ❌ Found {nan_count} NaN values!")
            return False
        if inf_count > 0:
            print(f"  ❌ Found {inf_count} Inf values!")
            return False
        
        # Check if values are in reasonable range
        if np.min(output) < -0.1 or np.max(output) > 1.1:
            print(f"  ⚠️ Output values outside expected [0,1] range")
        
        print("  ✓ Model test passed - ready for FP16 conversion")
        return True
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Generate FP16-safe ONNX models for InSPyReNet')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml",
                       help='Path to the configuration file.')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra",
                       help='Directory containing model checkpoint.')
    parser.add_argument('--opset-version', type=int, default=17,
                       help='ONNX opset version.')
    parser.add_argument('--test-models', action='store_true', default=True,
                       help='Test the generated models after creation')
    parser.add_argument('--convert-to-fp16', action='store_true',
                       help='Also convert the FP16-safe models to actual FP16')

    args = parser.parse_args()

    print("FP16-Safe ONNX Model Generator")
    print("=" * 50)
    print("This script creates ONNX models that are safe for FP16 TensorRT conversion")
    print("by avoiding numerical instabilities in the normalization operations.")
    print("=" * 50)
    print(f"Configuration: {args.config}")
    print(f"Checkpoint dir: {args.checkpoint_dir}")
    print(f"ONNX opset version: {args.opset_version}")
    print("=" * 50)

    # Generate FP16-safe models
    export_fp16_safe_models(
        config_path=args.config,
        checkpoint_dir=args.checkpoint_dir,
        opset_version=args.opset_version
    )

    # Test the generated models
    if args.test_models:
        print("\n" + "=" * 50)
        print("TESTING GENERATED MODELS")
        print("=" * 50)
        
        fp16safe_models = [
            "InsPyReNet_1280x720_fp16safe.onnx",
            "InsPyReNet_1280x960_fp16safe.onnx", 
            "InsPyReNet_720x1280_fp16safe.onnx",
            "InsPyReNet_960x1280_fp16safe.onnx",
            "InsPyReNet_800x800_fp16safe.onnx"
        ]
        
        all_passed = True
        for model_path in fp16safe_models:
            if os.path.exists(model_path):
                if not test_fp16_safe_model(model_path):
                    all_passed = False
                print("-" * 30)
        
        if all_passed:
            print("✅ All models passed testing!")
        else:
            print("⚠️ Some models failed testing")

    # Convert to FP16 if requested
    if args.convert_to_fp16:
        print("\n" + "=" * 50)
        print("CONVERTING TO FP16")
        print("=" * 50)
        
        try:
            import subprocess
            result = subprocess.run([
                'python', 'onnx_convert_fp16_fixed.py', '--method', 'mixed'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ FP16 conversion completed successfully")
                print(result.stdout)
            else:
                print("❌ FP16 conversion failed")
                print(result.stderr)
        except Exception as e:
            print(f"❌ Could not run FP16 conversion: {e}")
            print("Please run manually: python onnx_convert_fp16_fixed.py --method mixed")

    print("\n" + "🎉" + " " * 48 + "🎉")
    print("SUCCESS! Your FP16-safe models are ready.")
    print("=" * 50)
    print("Next steps:")
    print("1. Convert FP16-safe models to actual FP16:")
    print("   python onnx_convert_fp16_fixed.py --method mixed")
    print("")
    print("2. Convert FP16 models to TensorRT:")
    print("   python convert_onnx_to_tensorrt.py --onnx <model>_fp16_fixed.onnx --fp16")
    print("")
    print("3. Test TensorRT FP16 inference:")
    print("   python InferenceTensorRT.py --source input.png --engine <model>.engine")
    print("🎉" + " " * 48 + "🎉")

if __name__ == "__main__":
    main()