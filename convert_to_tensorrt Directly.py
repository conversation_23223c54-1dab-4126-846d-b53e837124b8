#!/usr/bin/env python
# Script to convert InSPyReNet model to TensorRT

import torch
import torch_tensorrt
import tensorrt as trt
import os
import numpy as np
from PIL import Image
import sys
import argparse
import logging

# Add parent directory to path if needed
parent_dir = os.path.dirname(os.getcwd())
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Verify we're in the right directory
# inspyrenet_dir = '/content/drive/MyDrive/Colab/InSPyReNet'
inspyrenet_dir = 'F:/Catechese/EditeurAudioVideo/Archives/InSPyReNet'
if not os.path.exists(os.path.join(inspyrenet_dir, 'lib')):
    print(f"Error: Could not find InSPyReNet directories. Make sure you're in the correct directory.")
    sys.exit(1)

# Make sure we can import from the correct directories
os.chdir(inspyrenet_dir)
sys.path.append(inspyrenet_dir)

try:
    from lib import *
    from utils.misc import *
    from data.dataloader import *
    from data.custom_transforms import *
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure all required files exist in the correct directories")
    sys.exit(1)

torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

def create_engine(config_path, checkpoint_dir, engine_path):
    """
    Create TensorRT engine from PyTorch model
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        engine_path: Path to save the TensorRT engine
    """
    # Verify checkpoint file exists
    checkpoint_file = os.path.join(checkpoint_dir, 'latest.pth')
    if not os.path.exists(checkpoint_file):
        print(f"Error: Checkpoint file not found at {checkpoint_file}")
        print("Please make sure you have the model checkpoint in the specified directory")
        return False
    
    # Verify config file exists
    if not os.path.exists(config_path):
        print(f"Error: Config file not found at {config_path}")
        return False
    
    print(f"Loading configuration from {config_path}")
    print(f"Loading checkpoint from {checkpoint_file}")
    
    # Load configuration
    opt = load_config(config_path)
    
    # Load model
    print("Loading PyTorch model...")
    try:
        model = eval(opt.Model.name)(**opt.Model)
        model.load_state_dict(torch.load(checkpoint_file, map_location=torch.device('cpu')), strict=True)
        model = model.cuda()
        model.eval()
        print("Model loaded successfully")
    except Exception as e:
        print(f"Error loading model: {e}")
        return False
    
    # Create wrapper for inference with more explicit handling
    class SimpleInferenceWrapper(torch.nn.Module):
        def __init__(self, model):
            super().__init__()
            self.model = model
            self.model.eval()
            self.model.forward = self.model.forward_inference

        def forward(self, x):
            # More explicit handling of input
            batch, channels, height, width = x.shape
            # Ensure input is float32 to avoid precision issues during tracing
            x = x.to(dtype=torch.float32)
            output = self.model({'image': x})
            pred = output['pred']
            return pred

    # Create the wrapper
    wrapper = SimpleInferenceWrapper(model)
    input_shape = (1, 3, 1280, 1280)
    
    # Prepare input
    print("Preparing input tensor...")
    input_tensor = torch.randn(input_shape).cuda()
    
    # Convert PyTorch model to TensorRT
    print("Converting PyTorch model to TensorRT...")
    try:
        with torch.no_grad():
            # Try scripting instead of tracing
            print("Scripting model instead of tracing...")
            try:
                traced_model = torch.jit.script(wrapper)
            except Exception as e:
                print(f"Scripting failed, falling back to trace: {e}")
                traced_model = torch.jit.trace(wrapper, input_tensor)
            
            # Convert to TensorRT
            print("Compiling to TensorRT...")
            # Set logging level directly
            original_level = torch_tensorrt._LOGGER.level
            # Use numeric value 40 for ERROR level if needed
            torch_tensorrt._LOGGER.setLevel(logging.ERROR)  # ERROR level is 40
            
            try:
                trt_model = torch_tensorrt.compile(
                    traced_model,
                    inputs=[
                        torch_tensorrt.Input(
                            shape=input_shape,
                            dtype=torch.float32,
                            # Enable dynamic shapes to help with shape analysis
                            min_shape=input_shape,
                            opt_shape=input_shape,
                            max_shape=input_shape
                        )
                    ],
                    enabled_precisions={torch.float16},
                    workspace_size=2 << 30,
                    truncate_long_and_double=True,
                    debug=True,  # Enable debug mode
                    min_block_size=3  # Increase minimum partition size
                )
            except RuntimeError as e:
                print(f"TensorRT compilation failed with error: {e}")
                print("Trying alternative approach with direct TorchScript export...")
                
                # Save the TorchScript model directly if TensorRT compilation fails
                torch.jit.save(traced_model, engine_path + ".torchscript")
                print(f"Saved TorchScript model to {engine_path}.torchscript")
                print("You can use this model directly with torch.jit.load()")
                return False
            finally:
                # Restore original logging level
                torch_tensorrt._LOGGER.setLevel(original_level)
        
        # Save the model
        print(f"Saving TensorRT engine to {engine_path}")
        torch.jit.save(trt_model, engine_path)
        print("Conversion completed successfully!")
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("Starting InSPyReNet to TensorRT conversion...")
    
    parser = argparse.ArgumentParser(description='Convert model to TensorRT')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to configuration file')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint')
    parser.add_argument('--engine-path', '-e', type=str, default="engine.trt", help='Path to save TensorRT engine')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workspace-size', type=int, default=3<<30, help='Workspace size in bytes')
    
    args = parser.parse_args()
    
    # Make sure paths are correct
    config_path = os.path.join(inspyrenet_dir, args.config)
    checkpoint_dir = os.path.join(inspyrenet_dir, args.checkpoint_dir)
    engine_path = os.path.join(inspyrenet_dir, args.engine_path)
    
    print(f"Configuration file: {config_path}")
    print(f"Checkpoint directory: {checkpoint_dir}")
    print(f"Output engine path: {engine_path}")
    
    success = create_engine(config_path, checkpoint_dir, engine_path)
    
    if success:
        print("Conversion completed successfully!")
    else:
        print("Conversion failed!")