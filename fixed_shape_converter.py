#!/usr/bin/env python3
"""
Fixed Shape TensorRT Converter

This script converts ONNX models to TensorRT using fixed input shapes
to avoid dynamic reshape issues with Swin Transformer models.

Usage:
    python fixed_shape_converter.py Plus_Ultra_800x800.onnx
"""

import os
import sys
import argparse
import subprocess
import glob

def get_onnx_shape(onnx_path):
    """Get the exact input shape from ONNX model"""
    try:
        import onnx
        model = onnx.load(onnx_path)
        input_info = model.graph.input[0]
        input_name = input_info.name
        
        # Extract exact shape
        shape = []
        for dim in input_info.type.tensor_type.shape.dim:
            if dim.dim_value:
                shape.append(dim.dim_value)
            else:
                # For dynamic dimensions, infer from filename
                if "800x800" in onnx_path:
                    shape.extend([1, 3, 800, 800])
                elif "1280x720" in onnx_path:
                    shape.extend([1, 3, 720, 1280])
                elif "1280x960" in onnx_path:
                    shape.extend([1, 3, 960, 1280])
                elif "720x1280" in onnx_path:
                    shape.extend([1, 3, 1280, 720])
                elif "960x1280" in onnx_path:
                    shape.extend([1, 3, 1280, 960])
                else:
                    shape.extend([1, 3, 1280, 1280])
                break
        
        return input_name, shape
    except Exception as e:
        print(f"Could not parse ONNX model: {e}")
        # Fallback: infer from filename
        if "800x800" in onnx_path:
            return "input", [1, 3, 800, 800]
        elif "1280x720" in onnx_path:
            return "input", [1, 3, 720, 1280]
        elif "1280x960" in onnx_path:
            return "input", [1, 3, 960, 1280]
        else:
            return "input", [1, 3, 1280, 1280]

def find_trtexec():
    """Find trtexec executable"""
    possible_paths = [
        "trtexec",
        "trtexec.exe",
        "/usr/src/tensorrt/bin/trtexec",
        "/opt/tensorrt/bin/trtexec",
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\TensorRT\\bin\\trtexec.exe",
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def convert_with_fixed_shapes(onnx_path, engine_path, use_fp16=True):
    """Convert ONNX to TensorRT using fixed input shapes"""
    
    trtexec_path = find_trtexec()
    if not trtexec_path:
        print("❌ trtexec not found!")
        return False
    
    input_name, shape = get_onnx_shape(onnx_path)
    shape_str = "x".join(map(str, shape))
    
    print(f"Converting {onnx_path}")
    print(f"Input: {input_name}")
    print(f"Fixed shape: {shape}")
    
    # Build command with fixed shapes (no dynamic shapes)
    cmd = [
        trtexec_path,
        f"--onnx={onnx_path}",
        f"--saveEngine={engine_path}",
        f"--shapes={input_name}:{shape_str}",  # Fixed shape only
        "--memPoolSize=workspace:1024",  # 1GB workspace for RTX 3050 Ti
        "--verbose"
    ]
    
    if use_fp16:
        cmd.append("--fp16")
        print("Using FP16 precision")
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Conversion successful!")
            if os.path.exists(engine_path):
                size_mb = os.path.getsize(engine_path) / (1024 * 1024)
                print(f"Engine created: {engine_path} ({size_mb:.1f} MB)")
                return True
        else:
            print("❌ Conversion failed!")
            print("STDERR:", result.stderr[-1000:])  # Last 1000 chars
            
            # Try fallback without workspace specification
            print("\nTrying without workspace specification...")
            cmd_fallback = [
                trtexec_path,
                f"--onnx={onnx_path}",
                f"--saveEngine={engine_path}",
                f"--shapes={input_name}:{shape_str}",
            ]
            if use_fp16:
                cmd_fallback.append("--fp16")
            
            result2 = subprocess.run(cmd_fallback, capture_output=True, text=True, timeout=600)
            if result2.returncode == 0:
                print("✅ Fallback conversion successful!")
                return True
            else:
                print("❌ Fallback also failed!")
                print("STDERR:", result2.stderr[-500:])
        
        return False
        
    except subprocess.TimeoutExpired:
        print("❌ Conversion timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Fixed shape TensorRT converter')
    parser.add_argument('onnx_file', nargs='?', help='ONNX model file')
    parser.add_argument('--engine', type=str, help='Output engine file')
    parser.add_argument('--fp16', action='store_true', default=True, help='Use FP16')
    parser.add_argument('--fp32', action='store_true', help='Use FP32')
    parser.add_argument('--list', action='store_true', help='List ONNX models')
    
    args = parser.parse_args()
    
    print("Fixed Shape TensorRT Converter")
    print("=" * 40)
    
    onnx_models = sorted(glob.glob("*.onnx"))
    
    if args.list or not args.onnx_file:
        print("Available ONNX models:")
        for model in onnx_models:
            input_name, shape = get_onnx_shape(model)
            size_mb = os.path.getsize(model) / (1024 * 1024)
            print(f"  {model} - Shape: {shape} ({size_mb:.1f} MB)")
        
        if args.list:
            return 0
        
        if onnx_models:
            args.onnx_file = onnx_models[0]
            print(f"\nUsing: {args.onnx_file}")
        else:
            print("No ONNX models found!")
            return 1
    
    if not os.path.exists(args.onnx_file):
        print(f"❌ File not found: {args.onnx_file}")
        return 1
    
    if not args.engine:
        base_name = os.path.splitext(args.onnx_file)[0]
        args.engine = f"{base_name}_fixed.plan"
    
    use_fp16 = args.fp16 and not args.fp32
    
    success = convert_with_fixed_shapes(args.onnx_file, args.engine, use_fp16)
    
    if success:
        print(f"\n🎉 Success! Test with:")
        print(f"python run/InferenceTensorRT.py --source input.png --engine {args.engine}")
        return 0
    else:
        print(f"\n❌ Failed. Try ONNX Runtime instead:")
        print(f"python run/InferenceONNXTensorRT.py --onnx {args.onnx_file}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
