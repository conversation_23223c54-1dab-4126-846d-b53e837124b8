name: tensorrt_env
channels:
  - bioconda
  - conda-forge
  - defaults
dependencies:
  - _libavif_api=1.3.0=h57928b3_0
  - _openmp_mutex=4.5=2_gnu
  - _python_abi3_support=1.0=hd8ed1ab_2
  - adwaita-icon-theme=48.1=win_0
  - aom=3.9.1=he0c23c2_0
  - appdirs=1.4.4=pyhd8ed1ab_1
  - atk-1.0=2.38.0=hb44c4ce_2
  - brotli-python=1.1.0=py310h9e98ed7_3
  - bzip2=1.0.8=h2466b09_7
  - c-compiler=1.9.0=hcfcfb64_0
  - ca-certificates=2025.6.15=h4c7d964_0
  - cairo=1.18.4=h5782bbf_0
  - certifi=2025.6.15=pyhd8ed1ab_0
  - cffi=1.17.1=py310ha8f682b_0
  - charset-normalizer=3.4.2=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - cpython=3.10.18=py310hd8ed1ab_0
  - cuda-cccl_win-64=12.9.27=h57928b3_0
  - cuda-command-line-tools=12.9.1=h57928b3_0
  - cuda-compiler=12.9.1=h63438b5_0
  - cuda-crt-dev_win-64=12.9.86=h57928b3_1
  - cuda-crt-tools=12.9.86=h57928b3_1
  - cuda-cudart=12.9.79=he0c23c2_0
  - cuda-cudart-dev=12.9.79=he0c23c2_0
  - cuda-cudart-dev_win-64=12.9.79=he0c23c2_0
  - cuda-cudart-static=12.9.79=he0c23c2_0
  - cuda-cudart-static_win-64=12.9.79=he0c23c2_0
  - cuda-cudart_win-64=12.9.79=he0c23c2_0
  - cuda-cuobjdump=12.9.82=he0c23c2_0
  - cuda-cupti=12.9.79=he0c23c2_0
  - cuda-cupti-dev=12.9.79=he0c23c2_0
  - cuda-cuxxfilt=12.9.82=he0c23c2_0
  - cuda-libraries=12.9.1=h57928b3_0
  - cuda-libraries-dev=12.9.1=h57928b3_0
  - cuda-nvcc=12.9.86=h8f04d04_1
  - cuda-nvcc-dev_win-64=12.9.86=h36c15f3_1
  - cuda-nvcc-impl=12.9.86=h53cbb54_1
  - cuda-nvcc-tools=12.9.86=he0c23c2_1
  - cuda-nvcc_win-64=12.9.86=hd70436c_1
  - cuda-nvdisasm=12.9.88=he0c23c2_0
  - cuda-nvml-dev=12.9.79=he0c23c2_0
  - cuda-nvprof=12.9.79=he0c23c2_0
  - cuda-nvprune=12.9.82=he0c23c2_0
  - cuda-nvrtc=12.9.86=he0c23c2_0
  - cuda-nvrtc-dev=12.9.86=he0c23c2_0
  - cuda-nvvm-dev_win-64=12.9.86=h57928b3_1
  - cuda-nvvm-impl=12.9.86=he0c23c2_1
  - cuda-nvvm-tools=12.9.86=he0c23c2_1
  - cuda-nvvp=12.9.79=he0c23c2_0
  - cuda-opencl=12.9.19=he0c23c2_0
  - cuda-opencl-dev=12.9.19=he0c23c2_0
  - cuda-profiler-api=12.9.79=h57928b3_0
  - cuda-sanitizer-api=12.9.79=he0c23c2_0
  - cuda-toolkit=12.9.1=h7428d3b_0
  - cuda-tools=12.9.1=h57928b3_0
  - cuda-version=12.9=h4f385c5_3
  - cuda-visual-tools=12.9.1=h57928b3_0
  - cudnn=9.10.1.4=h1361d0a_0
  - cxx-compiler=1.9.0=h91493d7_0
  - dav1d=1.2.1=hcfcfb64_0
  - double-conversion=3.3.1=he0c23c2_0
  - easydict=1.13=pyhff2d567_1
  - epoxy=1.5.10=h8d14728_1
  - ffmpeg=7.1.1=gpl_h37769ee_906
  - filelock=3.18.0=pyhd8ed1ab_0
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.15.0=h765892d_1
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freeglut=3.2.2=he0c23c2_3
  - freetype=2.13.3=h57928b3_1
  - fribidi=1.0.10=h8d14728_0
  - fsspec=2025.5.1=pyhd8ed1ab_0
  - gdk-pixbuf=2.42.12=hed59a49_0
  - giflib=5.2.2=h64bf75a_0
  - glib=2.84.2=he8f994d_0
  - glib-tools=2.84.2=h4394cf3_0
  - graphite2=1.3.14=he0c23c2_0
  - gst-plugins-base=1.24.11=h3fe0a9e_0
  - gstreamer=1.24.11=h233a61a_0
  - gtk3=3.24.43=h4ab12e8_5
  - h2=4.2.0=pyhd8ed1ab_0
  - harfbuzz=11.2.1=h8796e6f_0
  - hdf5=1.14.6=nompi_hd5d9e70_101
  - hf-xet=1.1.4=py39hcaf6e8a_0
  - hicolor-icon-theme=0.17=h57928b3_2
  - hpack=4.1.0=pyhd8ed1ab_0
  - huggingface_hub=0.33.0=pyhd8ed1ab_0
  - hyperframe=6.1.0=pyhd8ed1ab_0
  - icu=75.1=he0c23c2_0
  - idna=3.10=pyhd8ed1ab_1
  - imath=3.1.12=hbb528cf_0
  - importlib-metadata=8.7.0=pyhe01879c_1
  - intel-openmp=2024.2.1=h57928b3_1083
  - jasper=4.2.5=h99a1cce_0
  - jinja2=3.1.6=pyhd8ed1ab_0
  - khronos-opencl-icd-loader=2024.10.24=h2466b09_1
  - kornia=0.8.1=pyhd8ed1ab_0
  - kornia-rs=0.1.9=py310he921d4d_2
  - krb5=1.21.3=hdf4eb48_0
  - lame=3.100=hcfcfb64_1003
  - lcms2=2.17=hbcf6048_0
  - lerc=4.0.0=h6470a55_1
  - libabseil=20250127.1=cxx17_h4eb7d71_0
  - libaec=1.1.4=h20038f6_0
  - libasprintf=0.22.5=h5728263_3
  - libavif16=1.3.0=hf2698fe_0
  - libblas=3.9.0=32_h641d27c_mkl
  - libboost=1.86.0=hb0986bb_3
  - libboost-python=1.86.0=py310h3e8ed56_3
  - libcblas=3.9.0=32_h5e41251_mkl
  - libclang13=20.1.7=default_h6e92b77_0
  - libcublas=12.9.1.4=he0c23c2_0
  - libcublas-dev=12.9.1.4=he0c23c2_0
  - libcudnn=9.10.1.4=hffc9a7f_0
  - libcudnn-dev=9.10.1.4=hffc9a7f_0
  - libcudss=0.6.0.5=hffc9a7f_0
  - libcufft=11.4.1.4=he0c23c2_0
  - libcufft-dev=11.4.1.4=he0c23c2_0
  - libcurand=10.3.10.19=he0c23c2_0
  - libcurand-dev=10.3.10.19=he0c23c2_0
  - libcurl=8.14.1=h88aaa65_0
  - libcusolver=11.7.5.82=he0c23c2_0
  - libcusolver-dev=11.7.5.82=he0c23c2_0
  - libcusparse=12.5.10.65=he0c23c2_0
  - libcusparse-dev=12.5.10.65=he0c23c2_0
  - libdeflate=1.24=h76ddb4d_0
  - libexpat=2.7.0=he0c23c2_0
  - libffi=3.4.6=h537db12_1
  - libfreetype=2.13.3=h57928b3_1
  - libfreetype6=2.13.3=h0b5ce68_1
  - libgcc=15.1.0=h1383e82_2
  - libgettextpo=0.22.5=h5728263_3
  - libglib=2.84.2=hbc94333_0
  - libgomp=15.1.0=h1383e82_2
  - libhwloc=2.11.2=default_ha69328c_1001
  - libiconv=1.18=h135ad9c_1
  - libintl=0.22.5=h5728263_3
  - libintl-devel=0.22.5=h5728263_3
  - libjpeg-turbo=3.1.0=h2466b09_0
  - liblapack=3.9.0=32_h1aa476e_mkl
  - liblapacke=3.9.0=32_h845c4fa_mkl
  - liblzma=5.8.1=h2466b09_2
  - libmagma=2.9.0=he50f1ff_0
  - libnpp=12.4.1.87=he0c23c2_0
  - libnpp-dev=12.4.1.87=he0c23c2_0
  - libnvfatbin=12.9.82=he0c23c2_0
  - libnvfatbin-dev=12.9.82=he0c23c2_0
  - libnvjitlink=12.9.86=he0c23c2_0
  - libnvjitlink-dev=12.9.86=he0c23c2_0
  - libnvjpeg=12.4.0.76=he0c23c2_0
  - libnvjpeg-dev=12.4.0.76=h57928b3_0
  - libogg=1.3.5=h2466b09_1
  - libopencv=4.11.0=qt6_py310h323afe6_608
  - libopenvino=2025.0.0=hb1d9b14_3
  - libopenvino-auto-batch-plugin=2025.0.0=h04f32e0_3
  - libopenvino-auto-plugin=2025.0.0=h04f32e0_3
  - libopenvino-hetero-plugin=2025.0.0=hb61b842_3
  - libopenvino-intel-cpu-plugin=2025.0.0=hb1d9b14_3
  - libopenvino-intel-gpu-plugin=2025.0.0=hb1d9b14_3
  - libopenvino-ir-frontend=2025.0.0=hb61b842_3
  - libopenvino-onnx-frontend=2025.0.0=hf9c6bd6_3
  - libopenvino-paddle-frontend=2025.0.0=hf9c6bd6_3
  - libopenvino-pytorch-frontend=2025.0.0=he0c23c2_3
  - libopenvino-tensorflow-frontend=2025.0.0=hd51e7bd_3
  - libopenvino-tensorflow-lite-frontend=2025.0.0=he0c23c2_3
  - libopus=1.5.2=h2466b09_0
  - libpng=1.6.49=h7a4582a_0
  - libprotobuf=5.29.3=he9d8c4a_1
  - librsvg=2.58.4=h5ce5fed_3
  - libsqlite=3.50.1=h67fdade_1
  - libssh2=1.11.1=h9aa295b_0
  - libtiff=4.7.0=h05922d8_5
  - libtorch=2.7.1=cuda126_mkl_h4be6f90_300
  - libusb=1.0.29=h1839187_0
  - libuv=1.51.0=h2466b09_0
  - libvorbis=1.3.7=h0e60522_0
  - libwebp-base=1.5.0=h3b0e114_0
  - libwinpthread=12.0.0.r4.gg4f2fc60ca=h57928b3_9
  - libxcb=1.17.0=h0e4246c_0
  - libxml2=2.13.8=h442d1da_0
  - libzlib=1.3.1=h2466b09_2
  - mako=1.3.10=pyhd8ed1ab_0
  - markupsafe=3.0.2=py310h38315fa_1
  - mkl=2024.2.2=h66d3029_15
  - mpmath=1.3.0=pyhd8ed1ab_1
  - networkx=3.4.2=pyh267e887_2
  - nsight-compute=2025.2.1.3=h5173278_0
  - numpy=2.2.6=py310h4987827_0
  - onnx=1.18.0=np20py310hc8f73cc_0
  - opencl-headers=2025.06.13=he0c23c2_0
  - opencv=4.11.0=qt6_py310h2237cdb_608
  - openexr=3.3.4=h9fcfcc6_0
  - openh264=2.6.0=hb17fa0b_0
  - openjpeg=2.5.3=h4d64b90_0
  - openssl=3.5.0=ha4e3fda_1
  - optree=0.16.0=py310hc19bc0b_0
  - packaging=25.0=pyh29332c3_1
  - pango=1.56.3=h0c53d3b_1
  - pcre2=10.45=h99c9b8b_0
  - pillow=11.2.1=py310h9595edc_0
  - pip=25.1.1=pyh8b19718_0
  - pixman=0.46.2=had0cd8c_0
  - platformdirs=4.3.8=pyhe01879c_0
  - protobuf=5.29.3=py310h9e98ed7_0
  - pthread-stubs=0.4=h0e40799_1002
  - pugixml=1.15=h372dad0_0
  - py-opencv=4.11.0=qt6_py310h384fcbd_608
  - pybind11=2.13.6=pyhc790b64_3
  - pybind11-global=2.13.6=pyh6a1d191_3
  - pycparser=2.22=pyh29332c3_1
  - pycuda=2025.1.1=py310hdc6fa3b_0
  - pysocks=1.7.1=pyh09c184e_7
  - python=3.10.18=h8c5b53a_0_cpython
  - python-gil=3.10.18=hd8ed1ab_0
  - python_abi=3.10=7_cp310
  - pytools=2025.1.6=pyhd8ed1ab_0
  - pytorch=2.7.1=cuda126_mkl_py310_hfcc198c_300
  - pyyaml=6.0.2=py310h38315fa_2
  - qt6-main=6.9.1=h02ddd7d_0
  - rav1e=0.7.1=ha073cba_3
  - requests=2.32.4=pyhd8ed1ab_0
  - safetensors=0.5.3=py310hc226416_0
  - sdl2=2.32.54=he0c23c2_0
  - sdl3=3.2.16=ha4196fd_0
  - setuptools=80.9.0=pyhff2d567_0
  - siphash24=1.7=py310ha8f682b_1
  - sleef=3.8=h7e360cc_0
  - snappy=1.2.1=h500f7fa_1
  - svt-av1=3.0.2=he0c23c2_0
  - sympy=1.14.0=pyh04b8f61_5
  - tbb=2021.13.0=h62715c5_1
  - timm=1.0.15=pyhd8ed1ab_0
  - tk=8.6.13=h2c6b04d_2
  - torchvision=0.22.0=cuda126_py310_h5d02e3b_0
  - tqdm=4.67.1=pyhd8ed1ab_1
  - typing-extensions=4.14.0=h32cad80_0
  - typing_extensions=4.14.0=pyhe01879c_0
  - tzdata=2025b=h78e105d_0
  - ucrt=10.0.22621.0=h57928b3_1
  - urllib3=2.5.0=pyhd8ed1ab_0
  - vc=14.3=h41ae7f8_26
  - vc14_runtime=14.44.35208=h818238b_26
  - vs2015_runtime=14.44.35208=h38c0c73_26
  - vs2019_win-64=19.29.30139=h7dcff83_26
  - vswhere=3.1.7=h40126e0_1
  - wheel=0.45.1=pyhd8ed1ab_1
  - win_inet_pton=1.1.0=pyh7428d3b_8
  - x264=1!164.3095=h8ffe710_2
  - x265=3.5=h2d74725_3
  - xorg-libxau=1.0.12=h0e40799_0
  - xorg-libxdmcp=1.1.5=h0e40799_0
  - yaml=0.2.5=h8ffe710_2
  - zipp=3.23.0=pyhd8ed1ab_0
  - zstandard=0.23.0=py310ha8f682b_2
  - zstd=1.5.7=hbeecb71_2
  - pip:
      - nvidia-cuda-runtime-cu12==12.9.79
      - tensorrt==**********
      - tensorrt-cu12==**********
      - tensorrt-cu12-bindings==**********
      - tensorrt-cu12-libs==**********
prefix: C:\Users\<USER>\miniconda3\envs\tensorrt_env
