#!/usr/bin/env python3
"""
Quick FP16 Fix

This script provides a simple solution to the LayerNormalization type mismatch
by creating a properly working FP16 model.

Usage:
    conda activate onnxgpu
    python quick_fp16_fix.py
"""

import os
import sys

def main():
    print("Quick FP16 Fix")
    print("=" * 30)
    
    # Check if original model exists
    original_model = "InsPyReNet_800x800.onnx"
    if not os.path.exists(original_model):
        print(f"❌ Original model not found: {original_model}")
        return 1
    
    print(f"✅ Found original model: {original_model}")
    
    # Step 1: Fix the type mismatch
    print("\nStep 1: Fixing LayerNormalization type mismatch...")
    
    try:
        import subprocess
        
        # Run the fix script
        cmd = f"python fix_fp16_type_error.py --input {original_model} --method keep_fp32"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Type mismatch fix completed")
            print(result.stdout)
        else:
            print("❌ Type mismatch fix failed")
            print(result.stderr)
            return 1
            
    except Exception as e:
        print(f"❌ Error running fix: {e}")
        return 1
    
    # Step 2: Test the fixed model
    print("\nStep 2: Testing the fixed model...")
    
    fixed_model = "InsPyReNet_800x800_fp16_typefixed.onnx"
    if os.path.exists(fixed_model):
        try:
            cmd = f"python run_fp16_test_fixed.py"
            # Just run the test, don't capture output so user can see it
            result = subprocess.run(cmd, shell=True)
            
            if result.returncode == 0:
                print("✅ All tests passed!")
                return 0
            else:
                print("❌ Tests failed")
                return 1
                
        except Exception as e:
            print(f"❌ Error running tests: {e}")
            return 1
    else:
        print(f"❌ Fixed model not found: {fixed_model}")
        return 1

if __name__ == "__main__":
    sys.exit(main())