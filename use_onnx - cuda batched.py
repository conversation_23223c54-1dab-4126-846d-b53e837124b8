import onnxruntime as ort
import numpy as np
from PIL import Image
import torch
import time

# Set global ONNX Runtime configuration for CUDA
def get_onnx_session_options():
    """
    Configure ONNX Runtime session options for CUDA usage
    """
    sess_options = ort.SessionOptions()
    sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
    sess_options.intra_op_num_threads = 1
    return sess_options

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def create_batch(image_path, batch_size=5, target_size=(1280, 1280)):
    """
    Create a batch of images for ONNX inference
    Args:
        image_path: Path to input image
        batch_size: Number of images in the batch
        target_size: Target size for the images
    Returns:
        Batch of preprocessed image tensors
    """
    # Process single image first
    single_img = preprocess_image(image_path, target_size)
    
    # Create batch by repeating the same image
    batch = np.repeat(single_img, batch_size, axis=0)
    
    # Ensure the batch is contiguous in memory for better GPU performance
    batch = np.ascontiguousarray(batch)
    
    return batch

def postprocess_output(output, batch_index=0, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        batch_index: Index in the batch to process
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0][batch_index]  # Extract specific batch item
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

# Example usage
if __name__ == "__main__":
    # Check if CUDA is available
    cuda_available = ort.get_device() == "GPU"
    if cuda_available:
        print("CUDA is available for ONNX Runtime")
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    else:
        print("CUDA is not available, falling back to CPU")
        providers = ['CPUExecutionProvider']
    
    # Load ONNX model with CUDA if available
    sess_options = get_onnx_session_options()
    sess = ort.InferenceSession("temp_model.onnx", sess_options=sess_options, providers=providers)
    
    # Process an image and create batch
    image_path = "input.png"
    batch_size = 1
    img_batch = create_batch(image_path, batch_size=batch_size)
    
    # Warm-up run (important for GPU performance measurement)
    print("Performing warm-up run...")
    _ = sess.run(None, {"image": img_batch})
    
    # Run inference 5 times with a batch of 5
    num_runs = 5
    for i in range(num_runs):
        # Start timing
        start_time = time.time()
        
        # Run inference with batch
        outputs = sess.run(None, {"image": img_batch})
        
        # End timing
        end_time = time.time()
        inference_time = end_time - start_time
        
        print(f"Inference run {i+1} with batch_size={batch_size}: {inference_time:.6f} seconds")
    
    # Get alpha matte from the last run (first image in batch)
    alpha = postprocess_output(outputs, batch_index=0)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")