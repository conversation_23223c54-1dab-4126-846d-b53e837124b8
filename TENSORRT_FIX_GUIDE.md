# TensorRT Fix Guide for InSPyReNet

## 🔍 Problem Analysis

The InSPyReNet model has several operations that cause issues with TensorRT, especially in FP16 mode:

### Root Causes:

1. **FP16 Precision Issues:**
   - ImagePyramid operations use small epsilon values (1e-4) that underflow in FP16
   - Morphological operations (dilation/erosion) in Transition layers
   - Min/max normalization with very small ranges

2. **TensorRT Version Differences:**
   - TensorRT 10.10: FP32 works, FP16 produces zeros
   - TensorRT 10.11: Both FP32 and FP16 can fail without proper settings

## 🛠️ Implemented Fixes

### Fix 1: FP16-Safe ONNX Model Creation
**File:** `GOOD ONE TO USE - onnx_create plus ultra.py`

**Changes:**
- Replaced `1e-4` epsilon with `1e-3` (FP16-safe)
- Added robust min/max normalization
- Enhanced clamping to prevent overflow/underflow

```python
# Old (FP16-unsafe):
pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-4)

# New (FP16-safe):
safe_epsilon = 1e-3
safe_range = torch.clamp(pred_range, min=safe_epsilon)
pred = (pred - pred_min) / safe_range
```

### Fix 2: Version-Specific TensorRT Optimizations
**File:** `GOOD ONE - direct_tensorrt_fp16.py`

**Changes:**
- Added TensorRT version detection
- Version-specific optimization flags
- Enhanced FP16 stability settings

**TensorRT 10.11+ Optimizations:**
```bash
--builderOptimizationLevel=3    # Conservative optimization
--avgTiming=8                   # More timing iterations
--noTF32                        # Disable TF32 for consistency
--stronglyTyped                 # Strict type checking (FP16)
--sparsity=disable              # Disable problematic optimizations
```

### Fix 3: Enhanced Error Detection and Warnings
**File:** `GOOD ONE - direct_tensorrt_fp16.py`

**Changes:**
- Detailed validation failure reporting
- Specific warnings for FP16 all-zeros output
- Performance comparison with fallback detection

### Fix 4: Diagnostic Tool
**File:** `tensorrt_diagnostic.py`

**Features:**
- Tests multiple TensorRT configurations
- Version-specific recommendations
- Automatic best-configuration detection

## 📋 Usage Instructions

### Step 1: Create Fixed ONNX Models
```bash
python "GOOD ONE TO USE - onnx_create plus ultra.py"
```
This creates ONNX models with FP16-safe operations.

### Step 2: Run Diagnostic Test (Recommended)
```bash
python tensorrt_diagnostic.py
```
This will test different TensorRT configurations and recommend the best settings.

### Step 3: Test TensorRT Engines
```bash
python "GOOD ONE - direct_tensorrt_fp16.py"
```
This creates and tests both FP32 and FP16 engines with enhanced error reporting.

## 🎯 Recommendations by TensorRT Version

### TensorRT **********
- ✅ **FP32**: Works reliably
- ⚠️ **FP16**: Produces all zeros - **avoid FP16**
- **Recommendation**: Use FP32 engines only

### TensorRT **********
- ✅ **FP32**: Works with conservative settings
- ⚠️ **FP16**: May work with ultra-conservative settings
- **Recommendation**: 
  1. Try FP32 with conservative optimization
  2. If FP16 needed, use strictest settings
  3. Always validate output before deployment

## 🔧 Manual TensorRT Commands

### For TensorRT 10.10 (FP32 only):
```bash
trtexec --onnx=InsPyReNet_1024x1024.onnx \
        --saveEngine=InsPyReNet_1024x1024_fp32.engine \
        --memPoolSize=workspace:2048 \
        --verbose
```

### For TensorRT 10.11+ (Conservative FP32):
```bash
trtexec --onnx=InsPyReNet_1024x1024.onnx \
        --saveEngine=InsPyReNet_1024x1024_fp32.engine \
        --memPoolSize=workspace:2048 \
        --builderOptimizationLevel=3 \
        --noTF32 \
        --verbose
```

### For TensorRT 10.11+ (Ultra-Conservative FP16):
```bash
trtexec --onnx=InsPyReNet_1024x1024.onnx \
        --saveEngine=InsPyReNet_1024x1024_fp16.engine \
        --fp16 \
        --memPoolSize=workspace:2048 \
        --builderOptimizationLevel=1 \
        --noTF32 \
        --stronglyTyped \
        --sparsity=disable \
        --verbose
```

## ⚠️ Important Notes

1. **Always validate output**: Check that the engine produces reasonable saliency maps
2. **FP16 is risky**: This model architecture has inherent FP16 precision issues
3. **Version matters**: TensorRT 10.11+ requires more conservative settings
4. **Test thoroughly**: Use the diagnostic tool before production deployment

## 🚀 Performance Expectations

- **FP32**: ~1100ms inference time, reliable output
- **FP16**: ~10x slower if broken, potentially faster if working correctly
- **Recommendation**: Prioritize reliability over speed for this model

## 🔍 Troubleshooting

### If FP16 produces all zeros:
1. Use FP32 instead
2. Check TensorRT version compatibility
3. Verify ONNX model was created with fixes

### If FP32 fails on TensorRT 10.11+:
1. Use conservative optimization level (1-3)
2. Add `--noTF32` flag
3. Increase timing iterations with `--avgTiming=8`

### If conversion times out:
1. Increase workspace memory
2. Reduce optimization level
3. Use simpler precision settings
