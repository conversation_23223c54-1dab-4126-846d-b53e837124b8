FP16 Layer Analysis Report for InSPyReNet
==================================================

PyTorch Model Analysis:
-------------------------
Found 401 problematic layers:
  • InSPyReNet (InSPyReNet): Contains mathematical operations sensitive to FP16
  • backbone.patch_embed.norm (LayerNorm): Normalization layer with division operations
  • backbone.layers.0.blocks.0.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.0.blocks.0.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.0.blocks.0.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.0.blocks.0.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.0.blocks.0.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.0.blocks.1.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.0.blocks.1.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.0.blocks.1.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.0.blocks.1.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.0.blocks.1.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.0.downsample.norm (LayerNorm): Normalization layer with division operations
  • backbone.layers.1.blocks.0.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.1.blocks.0.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.1.blocks.0.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.1.blocks.0.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.1.blocks.0.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.1.blocks.1.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.1.blocks.1.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.1.blocks.1.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.1.blocks.1.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.1.blocks.1.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.1.downsample.norm (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.0.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.0.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.0.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.0.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.0.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.1.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.1.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.1.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.1.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.1.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.2.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.2.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.2.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.2.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.2.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.3.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.3.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.3.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.3.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.3.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.4.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.4.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.4.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.4.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.4.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.5.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.5.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.5.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.5.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.5.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.6.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.6.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.6.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.6.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.6.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.7.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.7.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.7.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.7.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.7.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.8.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.8.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.8.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.8.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.8.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.9.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.9.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.9.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.9.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.9.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.10.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.10.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.10.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.10.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.10.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.11.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.11.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.11.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.11.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.11.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.12.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.12.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.12.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.12.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.12.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.13.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.13.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.13.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.13.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.13.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.14.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.14.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.14.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.14.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.14.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.15.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.15.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.15.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.15.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.15.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.16.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.16.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.16.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.16.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.16.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.blocks.17.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.17.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.2.blocks.17.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.2.blocks.17.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.2.blocks.17.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.2.downsample.norm (LayerNorm): Normalization layer with division operations
  • backbone.layers.3.blocks.0.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.3.blocks.0.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.3.blocks.0.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.3.blocks.0.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.3.blocks.0.mlp.act (GELU): Activation function using exponential operations
  • backbone.layers.3.blocks.1.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.layers.3.blocks.1.attn (WindowAttention): Contains mathematical operations sensitive to FP16
  • backbone.layers.3.blocks.1.attn.softmax (Softmax): Activation function prone to FP16 precision issues
  • backbone.layers.3.blocks.1.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.layers.3.blocks.1.mlp.act (GELU): Activation function using exponential operations
  • backbone.norm0 (LayerNorm): Normalization layer with division operations
  • backbone.norm1 (LayerNorm): Normalization layer with division operations
  • backbone.norm2 (LayerNorm): Normalization layer with division operations
  • backbone.norm3 (LayerNorm): Normalization layer with division operations
  • context1.branch0.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch1.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.branch1.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch1.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch1.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.branch2.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch2.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.branch2.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch2.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch2.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.branch3.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch3.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.branch3.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context1.branch3.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context1.branch3.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context1.conv_cat.bn (BatchNorm2d): Normalization layer with division operations
  • context1.conv_res.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch0.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch1.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.branch1.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch1.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch1.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.branch2.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch2.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.branch2.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch2.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch2.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.branch3.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch3.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.branch3.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context2.branch3.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context2.branch3.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context2.conv_cat.bn (BatchNorm2d): Normalization layer with division operations
  • context2.conv_res.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch0.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch1.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.branch1.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch1.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch1.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.branch2.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch2.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.branch2.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch2.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch2.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.branch3.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch3.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.branch3.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context3.branch3.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context3.branch3.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context3.conv_cat.bn (BatchNorm2d): Normalization layer with division operations
  • context3.conv_res.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch0.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch1.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.branch1.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch1.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch1.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.branch2.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch2.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.branch2.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch2.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch2.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.branch3.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch3.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.branch3.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context4.branch3.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context4.branch3.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context4.conv_cat.bn (BatchNorm2d): Normalization layer with division operations
  • context4.conv_res.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch0.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch1.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.branch1.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch1.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch1.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.branch2.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch2.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.branch2.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch2.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch2.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.branch3.conv0.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch3.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.branch3.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • context5.branch3.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • context5.branch3.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • context5.conv_cat.bn (BatchNorm2d): Normalization layer with division operations
  • context5.conv_res.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.conv1.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.conv2.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.conv3.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.conv4.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Hattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • decoder.Hattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Hattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Hattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Hattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • decoder.Wattn (SelfAttention): Contains mathematical operations sensitive to FP16
  • decoder.Wattn.query_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Wattn.key_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Wattn.value_conv.bn (BatchNorm2d): Normalization layer with division operations
  • decoder.Wattn.softmax (Softmax): Activation function prone to FP16 precision issues
  • attention0 (SICA): Contains mathematical operations sensitive to FP16
  • attention0.conv_query.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_query.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_key.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_key.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_value.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_value.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_out1.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_out2.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_out3.bn (BatchNorm2d): Normalization layer with division operations
  • attention0.conv_out4.bn (BatchNorm2d): Normalization layer with division operations
  • attention1 (SICA): Contains mathematical operations sensitive to FP16
  • attention1.conv_query.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_query.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_key.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_key.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_value.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_value.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_out1.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_out2.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_out3.bn (BatchNorm2d): Normalization layer with division operations
  • attention1.conv_out4.bn (BatchNorm2d): Normalization layer with division operations
  • attention2 (SICA): Contains mathematical operations sensitive to FP16
  • attention2.conv_query.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_query.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_key.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_key.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_value.0.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_value.1.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_out1.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_out2.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_out3.bn (BatchNorm2d): Normalization layer with division operations
  • attention2.conv_out4.bn (BatchNorm2d): Normalization layer with division operations

ONNX Model Analysis:
--------------------
Found 404 problematic nodes:
  • /model/backbone/patch_embed/norm/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.0/blocks.0/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.0/blocks.0/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.0/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.0/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.0/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.0/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.0/blocks.0/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.0/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.0/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.0/blocks.0/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.0/blocks.1/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.1/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.0/blocks.1/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.1/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.0/blocks.1/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.0/blocks.1/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.0/blocks.1/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.0/downsample/norm/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/norm0/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.1/blocks.0/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.1/blocks.0/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.0/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.0/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.0/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.0/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.1/blocks.0/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.0/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.0/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.1/blocks.0/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.1/blocks.1/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.1/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.1/blocks.1/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.1/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.1/blocks.1/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.1/blocks.1/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.1/blocks.1/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.1/downsample/norm/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.0/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.0/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.0/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.0/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.0/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.0/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.0/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.0/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.0/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.0/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.1/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.1/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.1/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.1/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.1/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.1/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.1/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.2/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.2/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.2/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.2/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.2/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.2/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.2/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.2/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.2/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.2/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.3/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.3/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.3/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.3/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.3/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.3/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.3/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.4/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.4/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.4/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.4/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.4/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.4/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.4/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.4/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.4/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.4/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.5/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.5/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.5/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.5/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.5/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.5/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.5/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.6/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.6/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.6/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.6/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.6/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.6/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.6/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.6/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.6/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.6/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.7/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.7/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.7/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.7/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.7/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.7/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.7/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.8/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.8/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.8/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.8/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.8/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.8/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.8/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.8/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.8/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.8/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.9/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.9/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.9/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.9/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.9/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.9/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.9/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.10/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.10/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.10/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.10/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.10/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.10/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.10/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.10/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.10/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.10/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.11/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.11/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.11/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.11/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.11/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.11/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.11/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.12/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.12/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.12/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.12/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.12/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.12/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.12/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.12/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.12/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.12/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.13/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.13/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.13/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.13/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.13/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.13/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.13/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.14/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.14/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.14/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.14/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.14/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.14/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.14/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.14/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.14/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.14/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.15/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.15/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.15/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.15/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.15/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.15/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.15/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.16/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.16/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.16/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.16/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.16/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.16/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.16/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.16/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.16/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.16/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.17/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.17/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.2/blocks.17/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.17/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.2/blocks.17/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.2/blocks.17/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.2/blocks.17/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.2/downsample/norm/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.3/blocks.0/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.3/blocks.0/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.0/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.0/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.0/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.0/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.3/blocks.0/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.0/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.0/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.3/blocks.0/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/norm1/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.3/blocks.1/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/attn/Div (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/attn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.1/attn/Div_1 (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/attn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/backbone/layers.3/blocks.1/attn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.1/attn/proj/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/backbone/layers.3/blocks.1/Div_2 (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/Div_3 (Div): Division - can amplify small errors
  • /model/backbone/layers.3/blocks.1/norm2/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/backbone/layers.3/blocks.1/mlp/act/Div (Div): Division - can amplify small errors
  • /model/backbone/norm3/LayerNormalization (LayerNormalization): Layer normalization - contains division operations
  • /model/context1/branch1/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch1/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch1/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch1/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch1/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch1/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch2/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch2/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch2/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch2/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch2/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch2/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch3/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch3/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch3/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch3/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context1/branch3/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context1/branch3/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch1/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch1/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch1/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch1/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch1/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch1/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch2/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch2/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch2/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch2/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch2/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch2/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch3/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch3/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch3/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch3/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context2/branch3/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context2/branch3/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch1/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch1/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch1/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch1/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch1/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch1/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch2/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch2/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch2/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch2/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch2/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch2/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch3/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch3/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch3/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch3/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context3/branch3/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context3/branch3/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch1/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch1/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch1/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch1/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch1/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch1/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch2/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch2/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch2/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch2/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch2/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch2/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch3/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch3/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch3/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch3/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context4/branch3/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context4/branch3/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch1/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch1/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch1/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch1/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch1/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch1/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch2/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch2/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch2/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch2/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch2/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch2/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch3/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch3/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch3/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch3/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/context5/branch3/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/context5/branch3/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/decoder/Hattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/decoder/Hattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/decoder/Hattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/decoder/Wattn/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/decoder/Wattn/softmax/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/decoder/Wattn/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention2/Sigmoid (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/attention2/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/attention2/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention2/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/attention2/MatMul_2 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention1/Sigmoid (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/attention1/Sigmoid_1 (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/attention1/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/attention1/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention1/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/attention1/MatMul_2 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention0/Sigmoid (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/attention0/Sigmoid_1 (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/attention0/MatMul (MatMul): Matrix multiplication in attention mechanism
  • /model/attention0/MatMul_1 (MatMul): Matrix multiplication in attention mechanism
  • /model/attention0/Softmax (Softmax): Softmax - numerical instability with large values
  • /model/attention0/MatMul_2 (MatMul): Matrix multiplication in attention mechanism
  • /model/Sigmoid (Sigmoid): Sigmoid activation - prone to saturation in FP16
  • /model/ReduceMin (ReduceMin): Reduction operations - accumulation/comparison errors
  • /model/ReduceMax (ReduceMax): Reduction operations - accumulation/comparison errors
  • /model/Div (Div): Division - can amplify small errors
  • /ReduceMin (ReduceMin): Reduction operations - accumulation/comparison errors
  • /ReduceMax (ReduceMax): Reduction operations - accumulation/comparison errors
  • /Div (Div): Division - can amplify small errors

Recommended Precision Mapping:
--------------------------------
FP32 Operations (keep high precision):
  • Div
  • LayerNormalization
  • MatMul
  • ReduceMax
  • ReduceMin
  • Sigmoid
  • Softmax

FP16 Operations (can use lower precision):
  • Abs
  • Add
  • Cast
  • Clip
  • Concat
  • Constant
  • ConstantOfShape
  • Conv
  • DepthToSpace
  • Equal
  • Erf
  • Expand
  • Gather
  • Mod
  • Mul
  • Neg
  • Not
  • Pad
  • Relu
  • Reshape
  • Resize
  • ScatterND
  • Shape
  • Slice
  • Sub
  • Transpose
  • Unsqueeze
  • Where

Recommendations:
---------------
1. 🔧 Keep these 7 operation types in FP32: Div, LayerNormalization, MatMul, ReduceMax, ReduceMin, Sigmoid, Softmax
2. ⚡ These 28 operation types can safely use FP16: Abs, Add, Cast, Clip, Concat, Constant, ConstantOfShape, Conv, DepthToSpace, Equal, Erf, Expand, Gather, Mod, Mul, Neg, Not, Pad, Relu, Reshape, Resize, ScatterND, Shape, Slice, Sub, Transpose, Unsqueeze, Where
3. ⚠️  Sigmoid operations found - these are particularly sensitive to FP16 precision. Consider applying sigmoid post-inference in FP32 for better stability.
4. ⚠️  Softmax operations found - use temperature scaling or apply in FP32 to avoid numerical instability.
5. ⚠️  LayerNormalization found - keep these in FP32 as they involve division operations that can amplify errors.
6. ⚠️  Reduction operations found - keep in FP32 to avoid accumulation errors.
7. 📊 Estimated 80.0% of operations can use FP16, providing significant memory savings while maintaining accuracy.

Implementation Guidance:
-----------------------
1. Use mixed precision ONNX conversion
2. Apply FP32 precision to sensitive operations
3. Use FP16 for memory-intensive but stable operations
4. Test thoroughly with representative data
5. Monitor for numerical instabilities
