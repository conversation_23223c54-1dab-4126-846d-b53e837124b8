#!/usr/bin/env python3
"""
Clean FP16 Models

This script cleans up graph topology issues in FP16 models
and ensures they work correctly.

Usage:
    python clean_fp16_models.py
"""

import os
import glob
import onnx
from onnx import helper, numpy_helper
import numpy as np

def clean_model_topology(model_path, output_path=None):
    """Clean up model topology issues"""
    
    if output_path is None:
        output_path = model_path.replace('.onnx', '_cleaned.onnx')
    
    print(f"Cleaning: {model_path}")
    
    try:
        # Load model
        model = onnx.load(model_path)
        
        # Remove problematic nodes if they exist
        nodes_to_remove = []
        for i, node in enumerate(model.graph.node):
            if 'graph_input_cast' in str(node.input):
                print(f"  Found problematic node: {node.name}")
                nodes_to_remove.append(i)
        
        # Remove nodes in reverse order to maintain indices
        for i in reversed(nodes_to_remove):
            del model.graph.node[i]
            print(f"  Removed node at index {i}")
        
        # Clean up value_info
        value_info_to_remove = []
        for i, value_info in enumerate(model.graph.value_info):
            if 'graph_input_cast' in value_info.name:
                value_info_to_remove.append(i)
        
        for i in reversed(value_info_to_remove):
            del model.graph.value_info[i]
            print(f"  Removed value_info at index {i}")
        
        # Try to fix the model
        try:
            onnx.checker.check_model(model)
            print("  ✓ Model validation passed")
        except Exception as e:
            print(f"  ⚠️ Model validation warning: {e}")
            print("  Proceeding anyway...")
        
        # Save cleaned model
        onnx.save(model, output_path)
        print(f"  ✓ Cleaned model saved: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"  ❌ Cleaning failed: {e}")
        return None

def main():
    print("Cleaning FP16 Models")
    print("=" * 30)
    
    # Find FP16 models
    fp16_models = sorted(glob.glob("*_fp16.onnx"))
    
    if not fp16_models:
        print("No FP16 models found")
        return 1
    
    print(f"Found {len(fp16_models)} FP16 models:")
    for model in fp16_models:
        print(f"  {model}")
    
    # Clean each model
    cleaned_models = []
    for model in fp16_models:
        cleaned_path = clean_model_topology(model)
        if cleaned_path:
            cleaned_models.append(cleaned_path)
    
    print(f"\n✓ Cleaned {len(cleaned_models)} models")
    
    if cleaned_models:
        print("\nTest the cleaned models with:")
        print("python test_corrected_fp16_models.py")
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
