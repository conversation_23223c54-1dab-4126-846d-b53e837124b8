import onnxruntime as ort
import numpy as np
from PIL import Image
import torch
import time

def preprocess_image(image_path, target_size=(800, 800)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

# Example usage
if __name__ == "__main__":
    # Load ONNX model
    #sess = ort.InferenceSession("latest.opset17.onnx")
    sess = ort.InferenceSession("Plus_Ultra_800x800.onnx")
    
    # Process an image
    image_path = "head.png"
    img = preprocess_image(image_path)
    
    # Run inference 5 times and record the time for each run
    num_runs = 5
    for i in range(num_runs):
        # Start timing
        start_time = time.time()
        
        # Run inference
        outputs = sess.run(None, {"input": img})
        
        # End timing
        end_time = time.time()
        inference_time = end_time - start_time
        
        print(f"Inference run {i+1}: {inference_time:.6f} seconds")
    
    # Get alpha matte from the last run
    alpha = postprocess_output(outputs)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")