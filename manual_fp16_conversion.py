#!/usr/bin/env python3
"""
Manual FP16 Conversion

This script manually converts only the model weights to FP16 while keeping
all operations in FP32. This avoids the LayerNormalization type mismatch issue.

Usage:
    conda activate onnxgpu
    python manual_fp16_conversion.py
"""

import os
import sys
import onnx
import numpy as np
from onnx import numpy_helper

def manual_fp16_conversion(input_model_path, output_model_path):
    """
    Manually convert only weights to FP16, keep all operations in FP32
    This avoids type mismatch issues while still reducing model size
    """
    print(f"Manual FP16 conversion: {input_model_path}")
    print(f"Output: {output_model_path}")
    
    try:
        # Load the original model
        print("Loading FP32 model...")
        model = onnx.load(input_model_path)
        
        # Check model
        onnx.checker.check_model(model)
        print("✓ FP32 model is valid")
        
        # Create a copy
        model_copy = onnx.ModelProto()
        model_copy.CopyFrom(model)
        
        print("Converting weights to FP16...")
        
        # Convert only the initializers (weights) to FP16
        converted_count = 0
        total_count = len(model_copy.graph.initializer)
        
        for initializer in model_copy.graph.initializer:
            if initializer.data_type == onnx.TensorProto.FLOAT:
                # Get the numpy array
                weights = numpy_helper.to_array(initializer)
                
                # Convert to FP16
                weights_fp16 = weights.astype(np.float16)
                
                # Create new initializer
                new_initializer = numpy_helper.from_array(weights_fp16, initializer.name)
                
                # Replace the old initializer
                initializer.CopyFrom(new_initializer)
                converted_count += 1
        
        print(f"✓ Converted {converted_count}/{total_count} weight tensors to FP16")
        
        # Keep ALL value_info, inputs, and outputs as FP32
        # This ensures operations stay in FP32 even with FP16 weights
        print("Keeping all operations in FP32...")
        
        # Inputs stay FP32
        for input_info in model_copy.graph.input:
            if input_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
                input_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
        
        # Outputs stay FP32
        for output_info in model_copy.graph.output:
            if output_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
                output_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
        
        # Value info stays FP32 (intermediate tensors)
        for value_info in model_copy.graph.value_info:
            if value_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
                value_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
        
        print("✓ All operations kept in FP32")
        
        # Save the model
        onnx.save(model_copy, output_model_path)
        print(f"✓ Model saved: {output_model_path}")
        
        # Get file sizes
        original_size = os.path.getsize(input_model_path) / (1024 * 1024)
        fp16_size = os.path.getsize(output_model_path) / (1024 * 1024)
        reduction = (1 - fp16_size / original_size) * 100
        
        print(f"Size reduction: {original_size:.1f}MB → {fp16_size:.1f}MB ({reduction:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Manual conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_fp16_model(model_path, test_image="input.png"):
    """
    Test the manually converted FP16 model
    """
    print(f"\nTesting manual FP16 model: {model_path}")
    
    try:
        import onnxruntime as ort
        from PIL import Image
        
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return False
            
        if not os.path.exists(test_image):
            print(f"❌ Test image not found: {test_image}")
            return False
        
        # Load model
        print("Loading model...")
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        print("✓ Model loaded successfully - no type mismatch!")
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        print(f"Input type: {input_info.type}")
        print(f"Input shape: {input_shape}")
        
        # Determine target size
        if len(input_shape) >= 4:
            target_size = (input_shape[2], input_shape[3])
        else:
            target_size = (800, 800)
        
        # Preprocess image
        print("Preprocessing image...")
        img = Image.open(test_image).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy array as float32
        img = np.array(img, dtype=np.float32) / 255.0
        
        # Normalization with explicit float32 arrays
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img = (img - mean) / std
        
        # Transpose and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        img = img.astype(np.float32)
        
        print("✓ Image preprocessed")
        
        # Run inference
        print("Running inference...")
        outputs = session.run(None, {input_info.name: img})
        output = outputs[0].squeeze()
        
        print("✓ Inference completed successfully")
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"\nOutput Analysis:")
        print(f"  Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"  Mean: {output_mean:.4f}, Std: {output_std:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        
        # Determine if model is working correctly
        if nan_count > 0 or inf_count > 0:
            print("❌ FAIL: Contains NaN or Inf values")
            return False
        elif white_percentage > 95:
            print("❌ FAIL: White alpha matte issue detected")
            return False
        elif output_std < 0.01:
            print("❌ FAIL: No contrast (flat output)")
            return False
        else:
            print("✅ PASS: Model output looks good")
            
            # Save test output
            output_img = (output * 255).astype(np.uint8)
            test_output_path = f"test_output_{os.path.basename(model_path).replace('.onnx', '')}.png"
            Image.fromarray(output_img).save(test_output_path)
            print(f"✓ Test output saved: {test_output_path}")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def convert_to_tensorrt(onnx_model_path):
    """
    Convert the working FP16 ONNX model to TensorRT
    """
    print(f"\nConverting to TensorRT: {onnx_model_path}")
    
    engine_path = onnx_model_path.replace('.onnx', '.engine')
    
    if os.path.exists(engine_path):
        print(f"✅ TensorRT engine already exists: {engine_path}")
        return True
    
    try:
        import subprocess
        
        # Use the TensorRT conversion script
        cmd = f"python convert_onnx_to_tensorrt.py --onnx {onnx_model_path} --fp16"
        print(f"Running: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("✅ TensorRT conversion successful!")
            if os.path.exists(engine_path):
                engine_size = os.path.getsize(engine_path) / (1024 * 1024)
                print(f"✓ Engine created: {engine_path} ({engine_size:.1f}MB)")
                return True
            else:
                print("⚠️ Command succeeded but engine file not found")
                return False
        else:
            print("❌ TensorRT conversion failed")
            if result.stderr:
                print("Error:", result.stderr[:500])  # Limit error output
            return False
            
    except Exception as e:
        print(f"❌ TensorRT conversion error: {e}")
        return False

def main():
    print("Manual FP16 Conversion")
    print("=" * 40)
    print("This approach converts only weights to FP16 while keeping")
    print("all operations in FP32 to avoid type mismatch issues.")
    print("=" * 40)
    
    # Input and output paths
    input_model = "InsPyReNet_800x800.onnx"
    output_model = "InsPyReNet_800x800_manual_fp16.onnx"
    
    # Check if input exists
    if not os.path.exists(input_model):
        print(f"❌ Input model not found: {input_model}")
        print("Please make sure you have the FP32 ONNX model")
        return 1
    
    print(f"✅ Found input model: {input_model}")
    
    # Step 1: Manual FP16 conversion
    print(f"\n{'='*50}")
    print("STEP 1: Manual FP16 Conversion")
    print('='*50)
    
    if os.path.exists(output_model):
        print(f"✅ Manual FP16 model already exists: {output_model}")
    else:
        if not manual_fp16_conversion(input_model, output_model):
            print("❌ Failed to create manual FP16 model")
            return 1
    
    # Step 2: Test the manual FP16 model
    print(f"\n{'='*50}")
    print("STEP 2: Testing Manual FP16 Model")
    print('='*50)
    
    if not test_manual_fp16_model(output_model):
        print("❌ Manual FP16 model test failed")
        return 1
    
    # Step 3: Convert to TensorRT
    print(f"\n{'='*50}")
    print("STEP 3: TensorRT Conversion")
    print('='*50)
    
    tensorrt_success = convert_to_tensorrt(output_model)
    
    # Success!
    print(f"\n{'🎉'*20}")
    print("SUCCESS! MANUAL FP16 WORKING")
    print('🎉'*20)
    print(f"\nFiles created:")
    print(f"  ✅ {output_model}")
    
    engine_file = output_model.replace('.onnx', '.engine')
    if os.path.exists(engine_file):
        print(f"  ✅ {engine_file}")
    
    print(f"\nKey advantages of this approach:")
    print(f"  • No type mismatch errors")
    print(f"  • Reduced model size (~50% smaller)")
    print(f"  • Compatible with TensorRT FP16")
    print(f"  • Maintains numerical stability")
    
    print(f"\nNext steps:")
    if os.path.exists(engine_file):
        print(f"1. Test TensorRT inference:")
        print(f"   python InferenceTensorRT.py --source input.png --engine {engine_file}")
    else:
        print(f"1. Convert to TensorRT manually:")
        print(f"   python convert_onnx_to_tensorrt.py --onnx {output_model} --fp16")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())