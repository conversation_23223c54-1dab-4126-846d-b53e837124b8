#!/usr/bin/env python3
"""
FP16 Layer Analyzer for InSPyReNet

This module analyzes the InSPyReNet model to identify layers that are problematic
when converted to FP16 precision. It provides detailed analysis of operations
that should remain in FP32 for numerical stability.

Key problematic operations for FP16:
1. Sigmoid - Prone to saturation and gradient vanishing
2. Softmax - Numerical instability with large values
3. LayerNorm - Division operations can cause precision loss
4. Division operations - Can amplify small errors
5. Exponential operations - Can overflow/underflow easily
6. Reduction operations (mean, min, max) - Accumulation errors
7. Attention mechanisms - Combine multiple sensitive operations

Usage:
    python fp16_layer_analyzer.py
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import onnx
import onnxruntime as ort
import numpy as np
from collections import defaultdict
import os
import sys
from typing import Dict, List, Set, Tuple

# Add lib to path for imports
sys.path.append('lib')
from lib import *
from utils.misc import *

class FP16LayerAnalyzer:
    """Analyzes PyTorch and ONNX models to identify FP16-problematic layers"""
    
    def __init__(self):
        self.problematic_ops = {
            # Mathematical operations prone to precision issues
            'sigmoid': 'Sigmoid activation - prone to saturation in FP16',
            'softmax': 'Softmax - numerical instability with large values',
            'exp': 'Exponential - can overflow/underflow in FP16',
            'log': 'Logarithm - precision loss near zero',
            'div': 'Division - can amplify small errors',
            'sqrt': 'Square root - precision loss near zero',
            'pow': 'Power operations - can cause overflow',
            
            # Normalization operations
            'layernorm': 'Layer normalization - division operations',
            'batchnorm': 'Batch normalization - division operations',
            'instancenorm': 'Instance normalization - division operations',
            'groupnorm': 'Group normalization - division operations',
            
            # Reduction operations
            'reducemean': 'Mean reduction - accumulation errors',
            'reducesum': 'Sum reduction - accumulation errors',
            'reducemin': 'Min reduction - comparison precision',
            'reducemax': 'Max reduction - comparison precision',
            'reduceprod': 'Product reduction - can overflow',
            
            # Attention and transformer operations
            'matmul': 'Matrix multiplication in attention - accumulation errors',
            'bmm': 'Batch matrix multiplication - accumulation errors',
            'attention': 'Attention mechanism - combines multiple sensitive ops',
            
            # Activation functions
            'gelu': 'GELU activation - uses exponential operations',
            'swish': 'Swish activation - uses sigmoid',
            'mish': 'Mish activation - uses softplus and tanh',
            
            # Loss functions
            'crossentropy': 'Cross entropy loss - uses log and softmax',
            'bce': 'Binary cross entropy - uses log',
            'mse': 'Mean squared error - squaring can amplify errors'
        }
        
        self.pytorch_to_onnx_mapping = {
            'torch.sigmoid': 'Sigmoid',
            'torch.nn.functional.sigmoid': 'Sigmoid',
            'torch.softmax': 'Softmax',
            'torch.nn.functional.softmax': 'Softmax',
            'torch.exp': 'Exp',
            'torch.log': 'Log',
            'torch.div': 'Div',
            'torch.sqrt': 'Sqrt',
            'torch.pow': 'Pow',
            'torch.nn.LayerNorm': 'LayerNormalization',
            'torch.nn.BatchNorm2d': 'BatchNormalization',
            'torch.nn.functional.layer_norm': 'LayerNormalization',
            'torch.mean': 'ReduceMean',
            'torch.sum': 'ReduceSum',
            'torch.min': 'ReduceMin',
            'torch.max': 'ReduceMax',
            'torch.matmul': 'MatMul',
            'torch.bmm': 'MatMul',
            'torch.nn.functional.gelu': 'Gelu',
            'torch.nn.GELU': 'Gelu'
        }
        
        self.analysis_results = {
            'pytorch_problematic_layers': [],
            'onnx_problematic_nodes': [],
            'recommendations': [],
            'fp32_required_ops': set(),
            'fp16_safe_ops': set()
        }

    def analyze_pytorch_model(self, model: nn.Module) -> Dict:
        """Analyze PyTorch model for FP16 problematic layers"""
        print("🔍 Analyzing PyTorch model for FP16 problematic layers...")
        
        problematic_layers = []
        
        def analyze_module(module, name="", depth=0):
            indent = "  " * depth
            module_type = type(module).__name__
            
            # Check if this module type is problematic
            is_problematic = False
            reason = ""
            
            if isinstance(module, (nn.Sigmoid, nn.Softmax)):
                is_problematic = True
                reason = "Activation function prone to FP16 precision issues"
            elif isinstance(module, (nn.LayerNorm, nn.BatchNorm2d, nn.GroupNorm, nn.InstanceNorm2d)):
                is_problematic = True
                reason = "Normalization layer with division operations"
            elif isinstance(module, (nn.GELU, nn.SiLU)):
                is_problematic = True
                reason = "Activation function using exponential operations"
            elif hasattr(module, 'forward'):
                # Check forward method for problematic operations
                forward_code = str(module.forward.__code__.co_names)
                if any(op in forward_code.lower() for op in ['sigmoid', 'softmax', 'exp', 'log', 'div']):
                    is_problematic = True
                    reason = "Contains mathematical operations sensitive to FP16"
            
            if is_problematic:
                layer_info = {
                    'name': name or module_type,
                    'type': module_type,
                    'reason': reason,
                    'depth': depth,
                    'module': module
                }
                problematic_layers.append(layer_info)
                print(f"{indent}❌ {name}: {module_type} - {reason}")
            else:
                print(f"{indent}✅ {name}: {module_type}")
            
            # Recursively analyze child modules
            for child_name, child_module in module.named_children():
                full_name = f"{name}.{child_name}" if name else child_name
                analyze_module(child_module, full_name, depth + 1)
        
        analyze_module(model)
        
        self.analysis_results['pytorch_problematic_layers'] = problematic_layers
        return problematic_layers

    def analyze_onnx_model(self, onnx_path: str) -> Dict:
        """Analyze ONNX model for FP16 problematic nodes"""
        print(f"🔍 Analyzing ONNX model: {onnx_path}")
        
        if not os.path.exists(onnx_path):
            print(f"❌ ONNX model not found: {onnx_path}")
            return {}
        
        try:
            model = onnx.load(onnx_path)
            graph = model.graph
            
            problematic_nodes = []
            node_counts = defaultdict(int)
            
            for node in graph.node:
                node_counts[node.op_type] += 1
                
                is_problematic = False
                reason = ""
                
                # Check if this node type is problematic for FP16
                op_type_lower = node.op_type.lower()
                
                if node.op_type in ['Sigmoid']:
                    is_problematic = True
                    reason = "Sigmoid activation - prone to saturation in FP16"
                elif node.op_type in ['Softmax']:
                    is_problematic = True
                    reason = "Softmax - numerical instability with large values"
                elif node.op_type in ['Exp']:
                    is_problematic = True
                    reason = "Exponential - can overflow/underflow in FP16"
                elif node.op_type in ['Log']:
                    is_problematic = True
                    reason = "Logarithm - precision loss near zero"
                elif node.op_type in ['Div']:
                    is_problematic = True
                    reason = "Division - can amplify small errors"
                elif node.op_type in ['LayerNormalization']:
                    is_problematic = True
                    reason = "Layer normalization - contains division operations"
                elif node.op_type in ['BatchNormalization']:
                    is_problematic = True
                    reason = "Batch normalization - contains division operations"
                elif node.op_type in ['ReduceMean', 'ReduceSum', 'ReduceMin', 'ReduceMax']:
                    is_problematic = True
                    reason = "Reduction operations - accumulation/comparison errors"
                elif node.op_type in ['MatMul'] and len(node.input) >= 2:
                    # Check if this is likely an attention mechanism
                    if any('attention' in inp.lower() or 'attn' in inp.lower() for inp in node.input):
                        is_problematic = True
                        reason = "Matrix multiplication in attention mechanism"
                elif node.op_type in ['Gelu']:
                    is_problematic = True
                    reason = "GELU activation - uses exponential operations"
                
                if is_problematic:
                    node_info = {
                        'name': node.name or f"{node.op_type}_{len(problematic_nodes)}",
                        'op_type': node.op_type,
                        'reason': reason,
                        'inputs': list(node.input),
                        'outputs': list(node.output)
                    }
                    problematic_nodes.append(node_info)
                    self.analysis_results['fp32_required_ops'].add(node.op_type)
                else:
                    self.analysis_results['fp16_safe_ops'].add(node.op_type)
            
            # Print summary
            print(f"\n📊 ONNX Model Analysis Summary:")
            print(f"Total nodes: {len(graph.node)}")
            print(f"Problematic nodes: {len(problematic_nodes)}")
            print(f"Node types found: {len(node_counts)}")
            
            print(f"\n❌ Problematic operations (should stay FP32):")
            for node in problematic_nodes:
                print(f"  • {node['op_type']}: {node['reason']}")
            
            print(f"\n✅ Safe operations (can use FP16):")
            safe_ops = self.analysis_results['fp16_safe_ops'] - self.analysis_results['fp32_required_ops']
            for op in sorted(safe_ops):
                print(f"  • {op}")
            
            self.analysis_results['onnx_problematic_nodes'] = problematic_nodes
            return problematic_nodes
            
        except Exception as e:
            print(f"❌ Error analyzing ONNX model: {e}")
            return {}

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis results"""
        recommendations = []

        fp32_ops = self.analysis_results['fp32_required_ops']
        fp16_ops = self.analysis_results['fp16_safe_ops'] - fp32_ops

        if fp32_ops:
            recommendations.append(
                f"🔧 Keep these {len(fp32_ops)} operation types in FP32: {', '.join(sorted(fp32_ops))}"
            )

        if fp16_ops:
            recommendations.append(
                f"⚡ These {len(fp16_ops)} operation types can safely use FP16: {', '.join(sorted(fp16_ops))}"
            )

        # Specific recommendations based on found operations
        if 'Sigmoid' in fp32_ops:
            recommendations.append(
                "⚠️  Sigmoid operations found - these are particularly sensitive to FP16 precision. "
                "Consider applying sigmoid post-inference in FP32 for better stability."
            )

        if 'Softmax' in fp32_ops:
            recommendations.append(
                "⚠️  Softmax operations found - use temperature scaling or apply in FP32 to avoid numerical instability."
            )

        if 'LayerNormalization' in fp32_ops:
            recommendations.append(
                "⚠️  LayerNormalization found - keep these in FP32 as they involve division operations that can amplify errors."
            )

        if any(op in fp32_ops for op in ['ReduceMean', 'ReduceSum', 'ReduceMin', 'ReduceMax']):
            recommendations.append(
                "⚠️  Reduction operations found - keep in FP32 to avoid accumulation errors."
            )

        # Memory and performance estimates
        total_ops = len(fp32_ops) + len(fp16_ops)
        if total_ops > 0:
            fp16_percentage = len(fp16_ops) / total_ops * 100
            recommendations.append(
                f"📊 Estimated {fp16_percentage:.1f}% of operations can use FP16, "
                f"providing significant memory savings while maintaining accuracy."
            )

        self.analysis_results['recommendations'] = recommendations
        return recommendations

    def create_precision_mapping(self) -> Dict[str, str]:
        """Create a mapping of operation types to their recommended precision"""
        precision_mapping = {}

        # FP32 operations
        for op in self.analysis_results['fp32_required_ops']:
            precision_mapping[op] = 'fp32'

        # FP16 operations
        for op in self.analysis_results['fp16_safe_ops']:
            if op not in self.analysis_results['fp32_required_ops']:
                precision_mapping[op] = 'fp16'

        return precision_mapping

    def save_analysis_report(self, output_path: str = "fp16_analysis_report.txt"):
        """Save detailed analysis report to file"""
        try:
            with open(output_path, 'w') as f:
                f.write("FP16 Layer Analysis Report for InSPyReNet\n")
                f.write("=" * 50 + "\n\n")

                # PyTorch analysis
                f.write("PyTorch Model Analysis:\n")
                f.write("-" * 25 + "\n")
                pytorch_layers = self.analysis_results['pytorch_problematic_layers']
                if pytorch_layers:
                    f.write(f"Found {len(pytorch_layers)} problematic layers:\n")
                    for layer in pytorch_layers:
                        f.write(f"  • {layer['name']} ({layer['type']}): {layer['reason']}\n")
                else:
                    f.write("No problematic layers found in PyTorch model.\n")
                f.write("\n")

                # ONNX analysis
                f.write("ONNX Model Analysis:\n")
                f.write("-" * 20 + "\n")
                onnx_nodes = self.analysis_results['onnx_problematic_nodes']
                if onnx_nodes:
                    f.write(f"Found {len(onnx_nodes)} problematic nodes:\n")
                    for node in onnx_nodes:
                        f.write(f"  • {node['name']} ({node['op_type']}): {node['reason']}\n")
                else:
                    f.write("No problematic nodes found in ONNX model.\n")
                f.write("\n")

                # Precision mapping
                f.write("Recommended Precision Mapping:\n")
                f.write("-" * 32 + "\n")
                precision_mapping = self.create_precision_mapping()

                f.write("FP32 Operations (keep high precision):\n")
                fp32_ops = [op for op, prec in precision_mapping.items() if prec == 'fp32']
                for op in sorted(fp32_ops):
                    f.write(f"  • {op}\n")
                f.write("\n")

                f.write("FP16 Operations (can use lower precision):\n")
                fp16_ops = [op for op, prec in precision_mapping.items() if prec == 'fp16']
                for op in sorted(fp16_ops):
                    f.write(f"  • {op}\n")
                f.write("\n")

                # Recommendations
                f.write("Recommendations:\n")
                f.write("-" * 15 + "\n")
                recommendations = self.generate_recommendations()
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
                f.write("\n")

                # Implementation guidance
                f.write("Implementation Guidance:\n")
                f.write("-" * 23 + "\n")
                f.write("1. Use mixed precision ONNX conversion\n")
                f.write("2. Apply FP32 precision to sensitive operations\n")
                f.write("3. Use FP16 for memory-intensive but stable operations\n")
                f.write("4. Test thoroughly with representative data\n")
                f.write("5. Monitor for numerical instabilities\n")

            print(f"✅ Analysis report saved to: {output_path}")
            return True

        except Exception as e:
            print(f"❌ Error saving analysis report: {e}")
            return False

def analyze_inspyrenet_model():
    """Analyze the InSPyReNet model for FP16 compatibility"""
    print("🚀 Starting FP16 Layer Analysis for InSPyReNet")
    print("=" * 50)

    analyzer = FP16LayerAnalyzer()

    # Step 1: Analyze PyTorch model
    print("\n📋 Step 1: Loading and analyzing PyTorch model...")
    try:
        # Load the model configuration
        config_path = "configs/InSPyReNet_SwinB.yaml"
        if not os.path.exists(config_path):
            print(f"⚠️  Config not found: {config_path}, using default SwinB model")
            from lib.InSPyReNet import InSPyReNet_SwinB
            model = InSPyReNet_SwinB(depth=64, pretrained=False, base_size=[384, 384])
        else:
            opt = load_config(config_path)
            model = eval(opt.Model.name)(**opt.Model)

        model.eval()
        pytorch_results = analyzer.analyze_pytorch_model(model)
        print(f"✅ PyTorch analysis complete: {len(pytorch_results)} problematic layers found")

    except Exception as e:
        print(f"❌ Error analyzing PyTorch model: {e}")
        pytorch_results = []

    # Step 2: Analyze ONNX models
    print("\n📋 Step 2: Analyzing ONNX models...")
    onnx_models = [
        "InsPyReNet_800x800.onnx",
        "InsPyReNet_1280x1280.onnx",
        "Plus_Ultra.onnx"
    ]

    for onnx_path in onnx_models:
        if os.path.exists(onnx_path):
            print(f"\n🔍 Analyzing {onnx_path}...")
            onnx_results = analyzer.analyze_onnx_model(onnx_path)
            print(f"✅ ONNX analysis complete: {len(onnx_results)} problematic nodes found")
            break
    else:
        print("⚠️  No ONNX models found for analysis")

    # Step 3: Generate recommendations
    print("\n📋 Step 3: Generating recommendations...")
    recommendations = analyzer.generate_recommendations()

    print(f"\n🎯 Analysis Results Summary:")
    print("-" * 30)
    for rec in recommendations:
        print(rec)

    # Step 4: Save detailed report
    print("\n📋 Step 4: Saving analysis report...")
    analyzer.save_analysis_report()

    print(f"\n🎉 FP16 Layer Analysis Complete!")
    print("Next steps:")
    print("1. Review the analysis report: fp16_analysis_report.txt")
    print("2. Use the mixed precision ONNX converter with the identified problematic operations")
    print("3. Test the resulting model for accuracy and performance")

    return analyzer

if __name__ == "__main__":
    analyzer = analyze_inspyrenet_model()
