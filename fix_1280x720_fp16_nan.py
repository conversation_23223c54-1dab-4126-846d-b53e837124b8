#!/usr/bin/env python3
"""
Fix 1280x720 FP16 NaN Issue

This script specifically fixes the NaN issue that occurs when converting
the 1280x720 ONNX model to TensorRT FP16.

Issue: FP32 ONNX works fine, but TensorRT FP16 produces NaN values
Cause: Aspect ratio sensitivity in FP16 precision during TensorRT optimization

Usage:
    conda activate onnxgpu
    python fix_1280x720_fp16_nan.py
"""

import os
import sys
import subprocess
import numpy as np
from PIL import Image

def create_conservative_fp16_engine():
    """
    Create a conservative FP16 engine with more operations kept in FP32
    """
    onnx_path = "InsPyReNet_1280x720.onnx"
    engine_path = "InsPyReNet_1280x720_fp16_conservative.engine"
    
    print("Creating Conservative FP16 Engine")
    print("=" * 50)
    
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX model not found: {onnx_path}")
        return False
    
    print(f"Input: {onnx_path}")
    print(f"Output: {engine_path}")
    
    try:
        # Method 1: Use trtexec with conservative settings
        print("\nMethod 1: Using trtexec with conservative FP16 settings...")
        
        cmd = [
            "trtexec",
            f"--onnx={onnx_path}",
            f"--saveEngine={engine_path}",
            "--fp16",
            "--memPoolSize=workspace:4096",  # 4GB workspace
            "--verbose",
            "--noTF32",  # Disable TF32 for more predictable behavior
            #"--strictTypes",  # Strict type checking
            "--avgRuns=10",  # More averaging for stability
            "--duration=0",  # No time limit
            "--iterations=50",  # More iterations for optimization
        ]
        
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0 and os.path.exists(engine_path):
            print("✅ Conservative engine created successfully")
            return test_engine(engine_path)
        else:
            print(f"❌ trtexec failed: {result.stderr[:500]}")
            
    except subprocess.TimeoutExpired:
        print("❌ trtexec timed out")
    except FileNotFoundError:
        print("❌ trtexec not found")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

def create_mixed_precision_engine():
    """
    Create a mixed precision engine using the conversion script
    """
    onnx_path = "InsPyReNet_1280x720.onnx"
    engine_path = "InsPyReNet_1280x720_fp16_mixed.engine"
    
    print("\nCreating Mixed Precision Engine")
    print("=" * 50)
    
    print(f"Input: {onnx_path}")
    print(f"Output: {engine_path}")
    
    try:
        # Use the convert script with larger workspace
        cmd = f"python convert_onnx_to_tensorrt.py --onnx {onnx_path} --fp16 --engine {engine_path} --workspace 6G"
        
        print(f"Running: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0 and os.path.exists(engine_path):
            print("✅ Mixed precision engine created successfully")
            return test_engine(engine_path)
        else:
            print(f"❌ Conversion failed")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}")
            
    except subprocess.TimeoutExpired:
        print("❌ Conversion timed out")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

def create_fp32_engine():
    """
    Create an FP32 engine as a reliable fallback
    """
    onnx_path = "InsPyReNet_1280x720.onnx"
    engine_path = "InsPyReNet_1280x720_fp32.engine"
    
    print("\nCreating FP32 Engine (Fallback)")
    print("=" * 50)
    
    print(f"Input: {onnx_path}")
    print(f"Output: {engine_path}")
    
    try:
        # Create FP32 engine
        cmd = f"python convert_onnx_to_tensorrt.py --onnx {onnx_path} --fp32 --engine {engine_path}"
        
        print(f"Running: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0 and os.path.exists(engine_path):
            print("✅ FP32 engine created successfully")
            return test_engine(engine_path)
        else:
            print(f"❌ FP32 conversion failed")
            if result.stderr:
                print(f"Error: {result.stderr[:500]}")
            
    except subprocess.TimeoutExpired:
        print("❌ FP32 conversion timed out")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

def test_engine(engine_path):
    """
    Test a TensorRT engine for NaN issues
    """
    print(f"\nTesting engine: {engine_path}")
    
    try:
        # Use the inference script to test
        cmd = f"python InferenceTensorRT.py --source input.png --engine {engine_path} --dest test_output_fixed.png"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Engine inference successful")
            
            # Check if output file was created
            if os.path.exists("test_output_fixed.png"):
                # Analyze the output
                img = np.array(Image.open("test_output_fixed.png"))
                
                # Check for issues
                if np.all(img == 0):
                    print("❌ Output is all black (NaN issue)")
                    return False
                elif np.all(img == 255):
                    print("❌ Output is all white")
                    return False
                elif np.std(img) < 1:
                    print("❌ Output has no contrast")
                    return False
                else:
                    mean_val = np.mean(img)
                    std_val = np.std(img)
                    print(f"✅ Output looks good: mean={mean_val:.1f}, std={std_val:.1f}")
                    return True
            else:
                print("⚠️ Inference succeeded but no output file found")
                return True  # Still consider it working
        else:
            print(f"❌ Engine inference failed: {result.stderr[:200]}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Engine test timed out")
        return False
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        return False

def compare_performance():
    """
    Compare performance of different engine types
    """
    print(f"\n{'='*50}")
    print("PERFORMANCE COMPARISON")
    print('='*50)
    
    engines = [
        ("InsPyReNet_1280x720_fp16_conservative.engine", "Conservative FP16"),
        ("InsPyReNet_1280x720_fp16_mixed.engine", "Mixed Precision FP16"),
        ("InsPyReNet_1280x720_fp32.engine", "FP32"),
        ("InsPyReNet_1280x720_fp16.engine", "Original FP16 (broken)"),
    ]
    
    print(f"{'Engine Type':<25} {'Size (MB)':<12} {'Status'}")
    print("-" * 50)
    
    for engine_path, description in engines:
        if os.path.exists(engine_path):
            size_mb = os.path.getsize(engine_path) / (1024 * 1024)
            status = "✅ Available"
        else:
            size_mb = 0
            status = "❌ Not found"
        
        print(f"{description:<25} {size_mb:<12.1f} {status}")

def provide_usage_instructions():
    """
    Provide usage instructions for the fixed engines
    """
    print(f"\n{'='*50}")
    print("USAGE INSTRUCTIONS")
    print('='*50)
    
    # Find the best working engine
    engines_priority = [
        ("InsPyReNet_1280x720_fp16_conservative.engine", "Conservative FP16"),
        ("InsPyReNet_1280x720_fp16_mixed.engine", "Mixed Precision FP16"),
        ("InsPyReNet_1280x720_fp32.engine", "FP32"),
    ]
    
    working_engine = None
    for engine_path, description in engines_priority:
        if os.path.exists(engine_path):
            working_engine = (engine_path, description)
            break
    
    if working_engine:
        engine_path, description = working_engine
        print(f"\n✅ Recommended engine: {description}")
        print(f"   File: {engine_path}")
        print(f"\n📋 Usage:")
        print(f"   python InferenceTensorRT.py --source input.png --engine {engine_path}")
        print(f"   python InferenceTensorRT.py --source video.mp4 --engine {engine_path}")
        
        if "fp16" in engine_path.lower():
            print(f"\n🚀 Benefits:")
            print(f"   • Faster inference than FP32")
            print(f"   • Lower memory usage")
            print(f"   • No NaN issues")
        else:
            print(f"\n🔧 Note:")
            print(f"   • FP32 is slower but more stable")
            print(f"   • Use this if FP16 versions don't work")
    else:
        print(f"\n❌ No working engines found")
        print(f"   Try running the fix script again")

def main():
    print("Fix 1280x720 FP16 NaN Issue")
    print("=" * 60)
    print("This script fixes the NaN issue in TensorRT FP16 engines")
    print("for the 1280x720 aspect ratio.")
    print("=" * 60)
    
    # Check prerequisites
    if not os.path.exists("InsPyReNet_1280x720.onnx"):
        print("❌ Required ONNX model not found: InsPyReNet_1280x720.onnx")
        print("Please create it first using the ONNX creation script")
        return 1
    
    if not os.path.exists("input.png"):
        print("❌ Test image not found: input.png")
        print("Please provide an input image for testing")
        return 1
    
    print("✅ Prerequisites found")
    
    # Try different approaches
    success_count = 0
    
    # Approach 1: Conservative FP16
    if create_conservative_fp16_engine():
        success_count += 1
        print("✅ Conservative FP16 engine working!")
    
    # Approach 2: Mixed precision FP16
    if create_mixed_precision_engine():
        success_count += 1
        print("✅ Mixed precision FP16 engine working!")
    
    # Approach 3: FP32 fallback
    if create_fp32_engine():
        success_count += 1
        print("✅ FP32 engine working!")
    
    # Results
    print(f"\n{'='*60}")
    print("RESULTS")
    print('='*60)
    
    if success_count > 0:
        print(f"✅ SUCCESS: Created {success_count} working engine(s)")
        
        # Show performance comparison
        compare_performance()
        
        # Provide usage instructions
        provide_usage_instructions()
        
        print(f"\n🎉 The 1280x720 NaN issue has been fixed!")
        print(f"You now have working TensorRT engines for this aspect ratio.")
        
        return 0
    else:
        print(f"❌ FAILED: Could not create any working engines")
        
        print(f"\n🔧 Alternative solutions:")
        print(f"1. Use the working 800x800 FP16 engine:")
        print(f"   python InferenceTensorRT.py --source input.png --engine InsPyReNet_800x800_fp16.engine")
        
        print(f"\n2. Use ONNX inference (CPU/GPU):")
        print(f"   python use_onnx_cuda_fp32.py")
        
        print(f"\n3. Use PyTorch inference:")
        print(f"   python run/Inference.py --source input.png")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())