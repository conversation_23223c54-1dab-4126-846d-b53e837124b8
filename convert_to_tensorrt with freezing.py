#!/usr/bin/env python
# Script to convert InSPyReNet model to TensorRT with model freezing

import torch
import torch_tensorrt
import tensorrt as trt
import os
import numpy as np
from PIL import Image
import sys
import argparse
import logging
import io
import copy

# Add parent directory to path if needed
parent_dir = os.path.dirname(os.getcwd())
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Verify we're in the right directory
# inspyrenet_dir = '/content/drive/MyDrive/Colab/InSPyReNet'
inspyrenet_dir = 'F:/Catechese/EditeurAudioVideo/Archives/InSPyReNet'
if not os.path.exists(os.path.join(inspyrenet_dir, 'lib')):
    print(f"Error: Could not find InSPyReNet directories. Make sure you're in the correct directory.")
    sys.exit(1)

# Make sure we can import from the correct directories
os.chdir(inspyrenet_dir)
sys.path.append(inspyrenet_dir)

try:
    from lib import *
    from utils.misc import *
    from data.dataloader import *
    from data.custom_transforms import *
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure all required files exist in the correct directories")
    sys.exit(1)

torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

def freeze_model(model):
    """
    Freeze model parameters and optimize for inference
    Args:
        model: PyTorch model to freeze
    Returns:
        Frozen model
    """
    print("Freezing model...")
    
    # Make a deep copy of the model to avoid modifying the original
    frozen_model = copy.deepcopy(model)
    
    # Set the entire model to evaluation mode
    frozen_model.eval()
    
    # Force all modules to be in eval mode
    def set_eval_mode(module):
        module.train(False)  # This is equivalent to module.eval()
        for child in module.children():
            set_eval_mode(child)
    
    # Apply eval mode recursively to all submodules
    set_eval_mode(frozen_model)
    
    # Freeze parameters by setting requires_grad to False
    for param in frozen_model.parameters():
        param.requires_grad = False
    
    # Convert batch norm to frozen batch norm if present
    def freeze_bn(module):
        if isinstance(module, torch.nn.BatchNorm2d):
            module.eval()
            # Disable parameters
            module.weight.requires_grad = False
            module.bias.requires_grad = False
            # Disable running stats updates
            module.track_running_stats = False
    
    # Apply freezing function to all submodules
    frozen_model.apply(freeze_bn)
    
    # Optimize dropout for inference
    def disable_dropout(module):
        if isinstance(module, torch.nn.Dropout) or isinstance(module, torch.nn.Dropout2d):
            module.eval()
            module.p = 0.0  # Force dropout probability to zero
    
    # Apply dropout disabling
    frozen_model.apply(disable_dropout)
    
    # Force all operations to be in inference mode
    with torch.no_grad():
        # Run a dummy forward pass to ensure all lazy modules are initialized
        dummy_input = torch.randn(1, 3, 1280, 1280).cuda()
        _ = frozen_model.forward_inference({'image': dummy_input})
    
    print("Model frozen successfully")
    return frozen_model

def create_engine(config_path, checkpoint_dir, engine_path):
    """
    Create TensorRT engine from PyTorch model using multiple approaches
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        engine_path: Path to save the TensorRT engine
    """
    # Verify checkpoint file exists
    checkpoint_file = os.path.join(checkpoint_dir, 'latest.pth')
    if not os.path.exists(checkpoint_file):
        print(f"Error: Checkpoint file not found at {checkpoint_file}")
        print("Please make sure you have the model checkpoint in the specified directory")
        return False
    
    # Verify config file exists
    if not os.path.exists(config_path):
        print(f"Error: Config file not found at {config_path}")
        return False
    
    print(f"Loading configuration from {config_path}")
    print(f"Loading checkpoint from {checkpoint_file}")
    
    # Load configuration
    opt = load_config(config_path)
    
    # Load model
    print("Loading PyTorch model...")
    try:
        model = eval(opt.Model.name)(**opt.Model)
        model.load_state_dict(torch.load(checkpoint_file, map_location=torch.device('cpu')), strict=True)
        model = model.cuda()
        model.eval()
        print("Model loaded successfully")
    except Exception as e:
        print(f"Error loading model: {e}")
        return False
    
    # Freeze the model before conversion
    frozen_model = freeze_model(model)
    
    # Approach 1: Try direct TorchScript export first
    # print("\n--- Approach 1: Direct TorchScript Export with Frozen Model ---")
    # try_torchscript_export(frozen_model, engine_path)
    
    # Approach 2: Try ONNX export
    # print("\n--- Approach 2: ONNX Export with Frozen Model ---")
    # success = try_onnx_export(frozen_model, engine_path)
    # if success:
    #     return True
    
    # Approach 3: Try TensorRT direct compilation with more options
    print("\n--- Approach 3: TensorRT Direct Compilation with Frozen Model ---")
    success = try_tensorrt_compilation(frozen_model, engine_path)
    
    return success

def try_torchscript_export(model, engine_path):
    """
    Attempt to export model using TorchScript
    """
    try:
        # Create a simple wrapper for the model
        class SimpleWrapper(torch.nn.Module):
            def __init__(self, model):
                super().__init__()
                self.model = model
                self.model.eval()
            
            def forward(self, x):
                with torch.no_grad():
                    return self.model.forward_inference({'image': x})['pred']
        
        wrapper = SimpleWrapper(model)
        input_shape = (1, 3, 1280, 1280)
        input_tensor = torch.randn(input_shape).cuda()
        
        # Try to use tracing (more reliable than scripting for complex models)
        print("Tracing model...")
        with torch.no_grad():
            traced_model = torch.jit.trace(wrapper, input_tensor)
        
        # Ensure the traced model is in eval mode
        traced_model.eval()
        
        # Further optimize the traced model
        print("Optimizing traced model...")
        try:
            traced_model = torch.jit.freeze(traced_model)
        except Exception as e:
            print(f"Warning: Could not freeze traced model: {e}")
        
        try:
            traced_model = torch.jit.optimize_for_inference(traced_model)
        except Exception as e:
            print(f"Warning: Could not optimize traced model for inference: {e}")
        
        # Save the traced model
        torchscript_path = f"{engine_path}.torchscript"
        print(f"Saving TorchScript model to {torchscript_path}")
        torch.jit.save(traced_model, torchscript_path)
        print("TorchScript export successful. You can use this model with torch.jit.load()")
        
        # Verify the model by loading and running inference
        print("Verifying TorchScript model...")
        loaded_model = torch.jit.load(torchscript_path)
        with torch.no_grad():
            test_output = loaded_model(input_tensor)
        print(f"Verification successful. Output shape: {test_output.shape}")
        
        return True
    except Exception as e:
        print(f"TorchScript export failed: {e}")
        return False

def try_onnx_export(model, engine_path):
    """
    Attempt to export model to ONNX and then to TensorRT
    """
    try:
        print("Preparing ONNX export...")
        
        # Create a simple wrapper for the model
        class ONNXWrapper(torch.nn.Module):
            def __init__(self, model):
                super().__init__()
                self.model = model
                self.model.eval()
            
            def forward(self, x):
                with torch.no_grad():
                    return self.model.forward_inference({'image': x})['pred']
        
        wrapper = ONNXWrapper(model)
        input_shape = (1, 3, 1280, 1280)
        input_tensor = torch.randn(input_shape).cuda()
        
        # Export to ONNX
        onnx_path = f"{engine_path}.onnx"
        print(f"Exporting to ONNX: {onnx_path}")
        
        with torch.no_grad():
            torch.onnx.export(
                wrapper,
                input_tensor,
                onnx_path,
                input_names=["input"],
                output_names=["output"],
                opset_version=13,
                do_constant_folding=True,
                dynamic_axes={
                    "input": {0: "batch_size"},
                    "output": {0: "batch_size"}
                },
                verbose=False
            )
        
        print("ONNX export successful")
        
        # Optimize the ONNX model
        try:
            import onnx
            import onnxoptimizer
            
            print("Optimizing ONNX model...")
            onnx_model = onnx.load(onnx_path)
            
            # Basic validation
            onnx.checker.check_model(onnx_model)
            
            # Apply optimizations
            passes = [
                'eliminate_identity',
                'eliminate_deadend',
                'eliminate_nop_transpose',
                'eliminate_nop_pad',
                'extract_constant_to_initializer',
                'fuse_bn_into_conv',
                'fuse_pad_into_conv',
                'fuse_matmul_add_bias_into_gemm',
                'fuse_transpose_into_gemm'
            ]
            
            optimized_model = onnxoptimizer.optimize(onnx_model, passes)
            
            # Save the optimized model
            optimized_onnx_path = f"{engine_path}_optimized.onnx"
            onnx.save(optimized_model, optimized_onnx_path)
            print(f"Saved optimized ONNX model to {optimized_onnx_path}")
            
            # Use the optimized model for TensorRT conversion
            onnx_path = optimized_onnx_path
            
        except ImportError:
            print("ONNX optimizer not available, continuing with the original ONNX model")
        except Exception as e:
            print(f"ONNX optimization failed: {e}, continuing with the original ONNX model")
        
        # Try to convert ONNX to TensorRT using the TensorRT API
        try:
            print("Converting ONNX to TensorRT...")
            import tensorrt as trt
            
            logger = trt.Logger(trt.Logger.ERROR)
            builder = trt.Builder(logger)
            network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            parser = trt.OnnxParser(network, logger)
            
            with open(onnx_path, 'rb') as model_file:
                if not parser.parse(model_file.read()):
                    for error in range(parser.num_errors):
                        print(f"ONNX parse error: {parser.get_error(error)}")
                    return False
            
            config = builder.create_builder_config()
            config.max_workspace_size = 2 << 30  # 2GB
            config.set_flag(trt.BuilderFlag.FP16)
            
            # Optimize for specific GPU architecture if available
            try:
                cuda_capability = torch.cuda.get_device_capability(0)
                if cuda_capability[0] >= 7:  # For Volta (7.x) or newer
                    capability = cuda_capability[0] * 10 + cuda_capability[1]
                    profile = builder.create_optimization_profile()
                    profile.set_shape("input", (1, 3, 1280, 1280), (1, 3, 1280, 1280), (1, 3, 1280, 1280))
                    config.add_optimization_profile(profile)
                    config.set_tactic_sources(1 << int(trt.TacticSource.CUBLAS) | 1 << int(trt.TacticSource.CUDNN))
                    print(f"Optimizing for CUDA capability {capability}")
            except Exception as e:
                print(f"Could not set specific optimizations: {e}")
                
            engine = builder.build_engine(network, config)
            
            if engine is None:
                print("Failed to create TensorRT engine")
                return False
            
            # Save the engine
            with open(engine_path, "wb") as f:
                f.write(engine.serialize())
            
            print(f"Successfully created and saved TensorRT engine to {engine_path}")
            return True
            
        except Exception as e:
            print(f"Failed to convert ONNX to TensorRT: {e}")
            print("You can still use the exported ONNX model with onnxruntime or another framework")
            return False
            
    except Exception as e:
        print(f"ONNX export failed: {e}")
        return False

def try_tensorrt_compilation(model, engine_path):
    """
    Try direct TensorRT compilation with more options
    """
    try:
        print("Attempting direct TensorRT compilation...")
        
        class TRTWrapper(torch.nn.Module):
            def __init__(self, model):
                super().__init__()
                self.model = model
                self.model.eval()
            
            def forward(self, x):
                # Simplify input/output for better TensorRT compatibility
                with torch.no_grad():
                    output = self.model.forward_inference({'image': x})
                    return output['pred']
        
        wrapper = TRTWrapper(model)
        
        # Define dynamic shape ranges for TensorRT
        min_shape = (1, 3, 320, 320)  # Minimum supported shape
        opt_shape = (1, 3, 1280, 1280)  # Optimal shape (your target)
        max_shape = (1, 3, 2560, 2560)  # Maximum supported shape
        
        # Create input specifications with dynamic shapes
        input_tensor = torch.randn(opt_shape).cuda()
        
        print("Tracing model...")
        with torch.no_grad():
            traced_model = torch.jit.trace(wrapper, input_tensor)
            
            # Further optimize the traced model
            print("Optimizing traced model for TensorRT...")
            traced_model.eval()
            
            try:
                traced_model = torch.jit.freeze(traced_model)
            except Exception as e:
                print(f"Warning: Could not freeze traced model: {e}")
                
            try:
                traced_model = torch.jit.optimize_for_inference(traced_model)
            except Exception as e:
                print(f"Warning: Could not optimize traced model for inference: {e}")
            
            # Set logging level directly
            original_level = torch_tensorrt._LOGGER.level
            torch_tensorrt._LOGGER.setLevel(40)  # ERROR level is 40
            
            try:
                print("Compiling with torch_tensorrt...")
                # Use dynamic shapes with correct API parameters
                trt_model = torch_tensorrt.compile(
                    traced_model,
                    inputs=[
                        torch_tensorrt.Input(
                            min_shape=min_shape,
                            opt_shape=opt_shape,
                            max_shape=max_shape,
                            dtype=torch.float32
                        )
                    ],
                    enabled_precisions={torch.float16, torch.float32},
                    workspace_size=3 << 30,  # 3GB
                    truncate_long_and_double=True,
                    require_full_compilation=False,
                    min_block_size=1,
                    debug=True
                )
                
                # Save the model
                print(f"Saving TensorRT engine to {engine_path}")
                torch.jit.save(trt_model, engine_path)
                print("TensorRT compilation completed successfully!")
                return True
                
            except Exception as e:
                print(f"TensorRT compilation failed: {e}")
                
                # Try with even fewer parameters if the previous attempt failed
                try:
                    print("Retrying with minimal parameters...")
                    trt_model = torch_tensorrt.compile(
                        traced_model,
                        inputs=[
                            torch_tensorrt.Input(
                                shape=opt_shape,
                                dtype=torch.float32
                            )
                        ],
                        enabled_precisions={torch.float16, torch.float32}
                    )
                    
                    # Save the model
                    print(f"Saving TensorRT engine to {engine_path}")
                    torch.jit.save(trt_model, engine_path)
                    print("TensorRT compilation completed successfully with minimal parameters!")
                    return True
                    
                except Exception as e2:
                    print(f"Minimal parameter TensorRT compilation also failed: {e2}")
                    return False
                
            finally:
                # Restore original logging level
                torch_tensorrt._LOGGER.setLevel(original_level)
                
    except Exception as e:
        print(f"TensorRT compilation approach failed: {e}")
        return False

def verify_engine(engine_path, model):
    """
    Verify the TensorRT engine against the original model
    """
    try:
        print("\nVerifying TensorRT engine...")
        
        # Load the serialized engine
        if not os.path.exists(engine_path):
            print(f"Error: Engine file not found at {engine_path}")
            return False
        
        # Check if we have a TorchScript model or a TensorRT engine
        if engine_path.endswith(".torchscript"):
            try:
                loaded_engine = torch.jit.load(engine_path)
                # Create test input
                test_input = torch.randn(1, 3, 1280, 1280).cuda()
                
                # Get output from original model
                with torch.no_grad():
                    original_output = model.forward_inference({'image': test_input})['pred']
                
                # Get output from engine
                with torch.no_grad():
                    engine_output = loaded_engine(test_input)
                
                # Compare outputs
                max_diff = torch.max(torch.abs(original_output - engine_output))
                print(f"Maximum difference between original and optimized model outputs: {max_diff.item()}")
                
                if max_diff.item() < 0.01:  # Threshold for acceptable difference
                    print("Verification successful: Outputs match within tolerance")
                    return True
                else:
                    print("Warning: Significant difference between original and optimized model outputs")
                    return False
                
            except Exception as e:
                print(f"Failed to verify TorchScript model: {e}")
                return False
        else:
            print("Verification requires loading the engine with TensorRT runtime.")
            print("Please verify manually using the inference script.")
            return True
        
    except Exception as e:
        print(f"Engine verification failed: {e}")
        return False

if __name__ == "__main__":
    print("Starting InSPyReNet to TensorRT conversion with model freezing...")
    
    parser = argparse.ArgumentParser(description='Convert model to TensorRT')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to configuration file')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint')
    parser.add_argument('--engine-path', '-e', type=str, default="engine.trt", help='Path to save TensorRT engine')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workspace-size', type=int, default=3<<30, help='Workspace size in bytes')
    parser.add_argument('--verify', action='store_true', help='Verify the engine after creation')
    
    args = parser.parse_args()
    
    # Make sure paths are correct
    config_path = os.path.join(inspyrenet_dir, args.config)
    checkpoint_dir = os.path.join(inspyrenet_dir, args.checkpoint_dir)
    engine_path = os.path.join(inspyrenet_dir, args.engine_path)
    
    print(f"Configuration file: {config_path}")
    print(f"Checkpoint directory: {checkpoint_dir}")
    print(f"Output engine path: {engine_path}")
    
    success = create_engine(config_path, checkpoint_dir, engine_path)
    
    if success:
        print("Conversion completed successfully!")
        
        # Load the original model for verification if requested
        if args.verify:
            opt = load_config(config_path)
            checkpoint_file = os.path.join(checkpoint_dir, 'latest.pth')
            
            try:
                model = eval(opt.Model.name)(**opt.Model)
                model.load_state_dict(torch.load(checkpoint_file, map_location=torch.device('cpu')), strict=True)
                model = model.cuda()
                model.eval()
                
                verify_engine(engine_path, model)
            except Exception as e:
                print(f"Could not load model for verification: {e}")
    else:
        print("All conversion attempts failed, but you may have partial outputs that can be used.")
        print("Check for .torchscript or .onnx files that were generated during the process.")