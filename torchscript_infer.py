import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import time
import argparse
from torchvision import transforms
import os

# Image preprocessing function
def preprocess_image(image_path, target_size=None):
    """
    Preprocess an image for TorchScript inference
    Args:
        image_path: Path to input image
        target_size: Optional target size for resizing (height, width)
    Returns:
        Preprocessed image tensor, original size
    """
    try:
        # Load image
        img = Image.open(image_path).convert('RGB')
        original_size = img.size  # (width, height)
        
        # Resize if target size is specified
        if target_size is not None:
            img = img.resize(target_size[::-1], Image.BILINEAR)  # Resize to target size
        
        # Convert to numpy array
        img = np.array(img, dtype=np.float32)
        
        # Normalize using the values from the config
        img = img / 255.0
        img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
        
        # Transpose to CHW format and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        # Convert to tensor
        img = torch.from_numpy(img)
        
        return img, (original_size[1], original_size[0])  # Return (height, width)
    except Exception as e:
        print(f"Error in preprocessing: {e}")
        return None, None

def save_alpha_matte(alpha, output_path, original_size):
    """
    Save alpha matte as a PNG image
    Args:
        alpha: Alpha matte tensor
        output_path: Path to save the output
        original_size: Original image size (height, width)
    """
    try:
        # Convert to numpy and scale to 0-255
        alpha = alpha.squeeze().cpu().numpy()
        alpha = (alpha * 255).astype(np.uint8)
        
        # Create PIL image
        alpha_img = Image.fromarray(alpha)
        
        # Resize back to original size
        alpha_img = alpha_img.resize((original_size[1], original_size[0]), Image.BILINEAR)
        
        # Save as PNG
        alpha_img.save(output_path)
        print(f"Saved alpha matte to {output_path}")
    except Exception as e:
        print(f"Error saving alpha matte: {e}")

def main():
    parser = argparse.ArgumentParser(description='Run inference with TorchScript model')
    parser.add_argument('--image', '-i', type=str, default='input full.png', help='Path to input image')
    parser.add_argument('--model', '-m', type=str, default='Plus_Ultra.pt', help='Path to TorchScript model')
    parser.add_argument('--output', '-o', type=str, default='output full.png', help='Path to save alpha matte')
    parser.add_argument('--runs', type=int, default=5, help='Number of inference runs')
    
    args = parser.parse_args()

    try:
        # Load the model
        print(f"Loading model from {args.model}...")
        model = torch.jit.load(args.model)
        model = model.cuda()
        model.eval()

        # Preprocess image
        print(f"Processing image {args.image}...")
        input_tensor, original_size = preprocess_image(args.image)
        input_tensor = input_tensor.cuda()

        # Run inference multiple times and measure timing
        inference_times = []
        print(f"\nRunning inference {args.runs} times:")
        for i in range(args.runs):
            start_time = time.time()
            
            with torch.no_grad():
                output = model(input_tensor)
                
            end_time = time.time()
            inference_time = end_time - start_time
            inference_times.append(inference_time)
            print(f"Run {i+1}: {inference_time:.3f} seconds")

        # Print summary statistics
        avg_time = sum(inference_times) / len(inference_times)
        print(f"\nAverage inference time: {avg_time:.3f} seconds")
        print(f"Min time: {min(inference_times):.3f} seconds")
        print(f"Max time: {max(inference_times):.3f} seconds")

        # Save the alpha matte
        save_alpha_matte(output, args.output, original_size)

    except Exception as e:
        print(f"Error during inference: {e}")

if __name__ == "__main__":
    main()