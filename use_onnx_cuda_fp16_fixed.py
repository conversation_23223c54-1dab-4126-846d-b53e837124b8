import onnxruntime as ort
import numpy as np
from PIL import Image
import torch
import time

def preprocess_image(image_path, target_size=(800, 800)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def postprocess_output_fixed(output, original_size=None, debug=True):
    """
    Fixed postprocess function that handles FP16 models properly
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
        debug: Print debug information
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    if debug:
        print(f"Raw output shape: {alpha.shape}")
        print(f"Raw output dtype: {alpha.dtype}")
        print(f"Raw output range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"Contains NaN: {np.isnan(alpha).any()}")
        print(f"Contains Inf: {np.isinf(alpha).any()}")
    
    # Convert to float32 if it's float16 to avoid precision issues
    if alpha.dtype == np.float16:
        alpha = alpha.astype(np.float32)
        if debug:
            print("Converted from FP16 to FP32")
    
    # Remove batch dimension
    alpha = alpha.squeeze()
    
    # Check for NaN/Inf values and handle them
    if np.isnan(alpha).any() or np.isinf(alpha).any():
        print("⚠️ Warning: Found NaN or Inf values in output!")
        print(f"NaN count: {np.isnan(alpha).sum()}")
        print(f"Inf count: {np.isinf(alpha).sum()}")
        
        # Replace NaN and Inf with safe values
        alpha = np.nan_to_num(alpha, nan=0.0, posinf=1.0, neginf=0.0)
        print("Replaced NaN/Inf values with safe defaults")
    
    # Apply sigmoid if values are not in [0,1] range (raw logits)
    if np.min(alpha) < -1 or np.max(alpha) > 2:
        if debug:
            print("Applying sigmoid activation (detected raw logits)")
        # Use stable sigmoid to avoid overflow
        alpha = stable_sigmoid(alpha)
    
    # Apply normalization like the original model
    alpha_min = np.min(alpha)
    alpha_max = np.max(alpha)
    
    if alpha_max > alpha_min:  # Avoid division by zero
        alpha = (alpha - alpha_min) / (alpha_max - alpha_min + 1e-8)
    else:
        # If all values are the same, set to 0.5
        alpha = np.full_like(alpha, 0.5)
    
    # Final clipping to ensure [0,1] range
    alpha = np.clip(alpha, 0.0, 1.0)
    
    if debug:
        print(f"Final alpha range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"Final alpha mean: {np.mean(alpha):.6f}")
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha_img = alpha_img.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha_img) / 255.0
    
    return alpha

def stable_sigmoid(x):
    """
    Numerically stable sigmoid function
    """
    # Clip extreme values to prevent overflow
    x = np.clip(x, -500, 500)
    
    # Use stable sigmoid computation
    positive_mask = x >= 0
    negative_mask = ~positive_mask
    
    result = np.zeros_like(x)
    
    # For positive values: sigmoid(x) = 1 / (1 + exp(-x))
    result[positive_mask] = 1.0 / (1.0 + np.exp(-x[positive_mask]))
    
    # For negative values: sigmoid(x) = exp(x) / (1 + exp(x))
    exp_x = np.exp(x[negative_mask])
    result[negative_mask] = exp_x / (1.0 + exp_x)
    
    return result

def check_model_outputs(session):
    """Check model output information"""
    print("Model output information:")
    for i, output in enumerate(session.get_outputs()):
        print(f"  Output {i}: {output.name}, shape: {output.shape}, type: {output.type}")

# Example usage
if __name__ == "__main__":
    # Check if CUDA is available
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Set up ONNX Runtime providers with FP16 optimizations
    providers = []
    if cuda_available:
        # Add CUDA provider with FP16 support
        providers.append(('CUDAExecutionProvider', {
            'device_id': 0,
            'arena_extend_strategy': 'kNextPowerOfTwo',
            'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB limit for RTX 3050 Ti
            'cudnn_conv_algo_search': 'EXHAUSTIVE',
            'do_copy_in_default_stream': True,
            'cudnn_conv_use_max_workspace': '1',
            'cudnn_conv1d_pad_to_nc1d': '1'
        }))
    
    # Add CPU provider as fallback
    providers.append('CPUExecutionProvider')
    
    print(f"Using providers: {[p[0] if isinstance(p, tuple) else p for p in providers]}")
    
    # Load ONNX model with CUDA support
    model_path = "Plus_Ultra_800x800_fp16.onnx"
    
    try:
        sess = ort.InferenceSession(model_path, providers=providers)
        print(f"✓ Successfully loaded model: {model_path}")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        # Try with CPU only
        sess = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
        print("Fallback to CPU execution")
    
    # Verify which provider is being used
    print(f"Active providers: {sess.get_providers()}")
    
    # Check model outputs
    check_model_outputs(sess)
    
    # Process an image
    image_path = "input.png"
    
    try:
        img = preprocess_image(image_path)
        print(f"✓ Preprocessed image shape: {img.shape}")
        print(f"Input range: [{np.min(img):.3f}, {np.max(img):.3f}]")
    except Exception as e:
        print(f"❌ Failed to preprocess image: {e}")
        exit(1)
    
    # Run inference with error handling
    print(f"\nRunning inference...")
    
    try:
        # Warm-up run
        if cuda_available:
            print("Performing warm-up run...")
            _ = sess.run(None, {"input": img})
        
        # Actual inference
        start_time = time.time()
        outputs = sess.run(None, {"input": img})
        end_time = time.time()
        
        print(f"✓ Inference completed in {end_time - start_time:.6f} seconds")
        
        # Process output with fixed function
        alpha = postprocess_output_fixed(outputs, debug=True)
        
        # Save alpha matte
        alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha_img.save("output_alpha_fixed.png")
        print(f"\n✓ Output saved as 'output_alpha_fixed.png'")
        
        # Additional validation
        print(f"\nFinal validation:")
        print(f"Alpha shape: {alpha.shape}")
        print(f"Alpha range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"Alpha mean: {np.mean(alpha):.6f}")
        print(f"Contains NaN: {np.isnan(alpha).any()}")
        print(f"Contains Inf: {np.isinf(alpha).any()}")
        
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
