#!/usr/bin/env python3
"""
Complete FP16 Testing Script - FIXED VERSION

This script performs the complete workflow:
1. Convert FP32 ONNX to FP16
2. Test both models
3. Compare outputs
4. Convert to TensorRT if successful

Run this in your conda onnxgpu environment:
    conda activate onnxgpu
    python run_fp16_test_fixed.py
"""

import os
import sys
import subprocess
import numpy as np
import onnxruntime as ort
from PIL import Image

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"Command: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True
        else:
            print("❌ FAILED")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_model_quick(model_path, test_image="input.png"):
    """Quick test of model output - FIXED DATA TYPE ISSUE"""
    print(f"\n--- Quick Test: {model_path} ---")
    
    try:
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return False
            
        if not os.path.exists(test_image):
            print(f"❌ Test image not found: {test_image}")
            return False
        
        # Load model
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        print(f"  Model expects: {input_info.type}")
        
        # Determine target size
        if len(input_shape) >= 4:
            target_size = (input_shape[2], input_shape[3])
        else:
            target_size = (800, 800)
        
        # Preprocess image - FIXED TO ENSURE FLOAT32
        img = Image.open(test_image).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy array as float32
        img = np.array(img, dtype=np.float32) / 255.0
        
        # Normalization with explicit float32 arrays
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img = (img - mean) / std
        
        # Transpose and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        # Final check - ensure it's float32
        img = img.astype(np.float32)
        
        print(f"  Input tensor dtype: {img.dtype}")
        print(f"  Input tensor shape: {img.shape}")
        print(f"  Input range: [{np.min(img):.3f}, {np.max(img):.3f}]")
        
        # Run inference
        outputs = session.run(None, {input_info.name: img})
        output = outputs[0].squeeze()
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"  Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"  Mean: {output_mean:.4f}, Std: {output_std:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        
        # Determine if model is working correctly
        if nan_count > 0 or inf_count > 0:
            print("  ❌ FAIL: Contains NaN or Inf values")
            return False
        elif white_percentage > 95:
            print("  ❌ FAIL: White alpha matte issue detected")
            return False
        elif output_std < 0.01:
            print("  ❌ FAIL: No contrast (flat output)")
            return False
        else:
            print("  ✅ PASS: Model output looks good")
            return True
            
    except Exception as e:
        print(f"  ❌ FAIL: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_models(fp32_model, fp16_model, test_image="input.png"):
    """Compare FP32 and FP16 model outputs"""
    print(f"\n{'='*60}")
    print("COMPARING FP32 vs FP16 OUTPUTS")
    print('='*60)
    
    try:
        # Test both models
        fp32_result = test_model_quick(fp32_model, test_image)
        fp16_result = test_model_quick(fp16_model, test_image)
        
        if fp32_result and fp16_result:
            print("\n✅ Both models working correctly!")
            return True
        elif fp32_result and not fp16_result:
            print("\n❌ FP32 works but FP16 has issues")
            return False
        elif not fp32_result and fp16_result:
            print("\n⚠️ FP32 has issues but FP16 works (unexpected)")
            return True
        else:
            print("\n❌ Both models have issues")
            return False
            
    except Exception as e:
        print(f"\n❌ Comparison failed: {e}")
        return False

def main():
    print("Complete FP16 Testing Workflow - FIXED VERSION")
    print("=" * 60)
    
    # Check if we're in the right environment
    try:
        import onnx
        import onnxruntime
        print("✅ ONNX libraries available")
        print(f"  ONNX Runtime version: {ort.__version__}")
    except ImportError as e:
        print(f"❌ Missing ONNX libraries: {e}")
        print("Please activate your conda environment: conda activate onnxgpu")
        return 1
    
    # Check if input files exist
    fp32_model = "InsPyReNet_800x800.onnx"
    test_image = "input.png"
    
    if not os.path.exists(fp32_model):
        print(f"❌ FP32 model not found: {fp32_model}")
        print("Please run the ONNX creation script first")
        return 1
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please provide an input image named 'input.png'")
        return 1
    
    print(f"✅ Input files found")
    
    # Step 1: Test FP32 model
    print(f"\n{'='*60}")
    print("STEP 1: Test FP32 Model")
    print('='*60)
    
    if not test_model_quick(fp32_model, test_image):
        print("❌ FP32 model has issues. Please fix before proceeding.")
        return 1
    
    # Step 2: Convert to FP16
    print(f"\n{'='*60}")
    print("STEP 2: Convert to FP16")
    print('='*60)
    
    fp16_model = "InsPyReNet_800x800_fp16_fixed.onnx"
    
    if os.path.exists(fp16_model):
        print(f"✅ FP16 model already exists: {fp16_model}")
    else:
        cmd = f"python onnx_convert_fp16_fixed.py --input {fp32_model} --method mixed"
        if not run_command(cmd, "Convert FP32 to FP16"):
            print("❌ FP16 conversion failed")
            return 1
    
    # Step 3: Test FP16 model
    print(f"\n{'='*60}")
    print("STEP 3: Test FP16 Model")
    print('='*60)
    
    if not test_model_quick(fp16_model, test_image):
        print("❌ FP16 model has issues")
        return 1
    
    # Step 4: Compare models
    if not compare_models(fp32_model, fp16_model, test_image):
        print("❌ Model comparison failed")
        return 1
    
    # Step 5: Convert to TensorRT (optional)
    print(f"\n{'='*60}")
    print("STEP 5: Convert to TensorRT FP16 (Optional)")
    print('='*60)
    
    engine_file = "InsPyReNet_800x800_fp16_fixed.engine"
    
    if os.path.exists(engine_file):
        print(f"✅ TensorRT engine already exists: {engine_file}")
    else:
        cmd = f"python convert_onnx_to_tensorrt.py --onnx {fp16_model} --fp16"
        if run_command(cmd, "Convert FP16 ONNX to TensorRT"):
            print("✅ TensorRT conversion successful!")
        else:
            print("⚠️ TensorRT conversion failed, but FP16 ONNX model is working")
    
    # Final summary
    print(f"\n{'🎉'*20}")
    print("SUCCESS! FP16 CONVERSION COMPLETED")
    print('🎉'*20)
    print(f"\nFiles created:")
    if os.path.exists(fp16_model):
        print(f"  ✅ {fp16_model}")
    if os.path.exists(engine_file):
        print(f"  ✅ {engine_file}")
    
    print(f"\nNext steps:")
    print(f"1. Test ONNX FP16 inference:")
    print(f"   python use_onnx_cuda_fp16_fixed.py")
    print(f"")
    if os.path.exists(engine_file):
        print(f"2. Test TensorRT FP16 inference:")
        print(f"   python InferenceTensorRT.py --source input.png --engine {engine_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())