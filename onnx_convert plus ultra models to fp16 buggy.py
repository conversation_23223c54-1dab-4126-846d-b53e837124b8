﻿import os
import glob
import argparse
import onnx
import warnings
from onnxconverter_common import float16

# Suppress truncation warnings
warnings.filterwarnings("ignore", message="the float32 number .* will be truncated to .*")

def convert_onnx_to_fp16(input_path, output_path=None, keep_io_types=True):
    """
    Convert an ONNX model from FP32 to FP16
    Args:
        input_path: Path to input ONNX model (FP32)
        output_path: Path to save FP16 model (optional, auto-generated if None)
        keep_io_types: Keep input/output as FP32 for compatibility
    """
    try:
        # Generate output path if not provided
        if output_path is None:
            base_name = input_path.replace('.onnx', '')
            output_path = f"{base_name}_fp16.onnx"
        
        print(f"Converting {input_path} to FP16...")
        
        # Load the FP32 model
        model_fp32 = onnx.load(input_path)
        
        # Check model validity
        onnx.checker.check_model(model_fp32)
        
        # Get original model size
        original_size = os.path.getsize(input_path) / (1024 * 1024)  # MB
        
        # Try multiple conversion approaches
        conversion_success = False
        
        # Method 1: Standard conversion with shape inference disabled
        try:
            print("  Attempting conversion with disabled shape inference...")
            model_fp16 = float16.convert_float_to_float16(
                model_fp32,
                keep_io_types=keep_io_types,
                disable_shape_infer=True,  # Disable shape inference to avoid topology issues
                min_positive_val=1e-4,     # More conservative range to avoid NaN
                max_finite_val=1e3,        # Prevent overflow
                op_block_list=['Sigmoid', 'Exp', 'Log', 'Div', 'Sqrt', 'Cast', 'Shape', 'Reshape']  # Keep critical ops in FP32
            )
            
            # Save FP16 model
            onnx.save(model_fp16, output_path)
            
            # Check converted model validity (skip strict checking for now)
            try:
                onnx.checker.check_model(model_fp16)
            except Exception as check_e:
                print(f"  Warning: Model validation failed but proceeding: {check_e}")
            
            conversion_success = True
            
        except Exception as e1:
            print(f"  Method 1 failed: {e1}")
            
            # Method 2: Try with different options
            try:
                print("  Attempting conversion with min/max values...")
                model_fp16 = float16.convert_float_to_float16(
                    model_fp32,
                    keep_io_types=keep_io_types,
                    disable_shape_infer=True,
                    min_positive_val=1e-7,  # Set explicit min values to avoid truncation warnings
                    max_finite_val=1e4
                )
                
                # Save FP16 model
                onnx.save(model_fp16, output_path)
                conversion_success = True
                
            except Exception as e2:
                print(f"  Method 2 failed: {e2}")
                
                # Method 3: Manual approach - convert weights only
                try:
                    print("  Attempting manual weight conversion...")
                    model_fp16 = convert_weights_to_fp16_manual(model_fp32, keep_io_types)
                    onnx.save(model_fp16, output_path)
                    conversion_success = True
                    
                except Exception as e3:
                    print(f"  Method 3 failed: {e3}")
                    raise Exception(f"All conversion methods failed. Last error: {e3}")
        
        if conversion_success:
            # Get converted model size
            converted_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            size_reduction = (1 - converted_size / original_size) * 100
            
            print(f"✓ Successfully converted to {output_path}")
            print(f"  Original size: {original_size:.2f} MB")
            print(f"  FP16 size: {converted_size:.2f} MB")
            print(f"  Size reduction: {size_reduction:.1f}%")
            
            return output_path
        
    except Exception as e:
        print(f"✗ Error converting {input_path}: {e}")
        return None

def convert_weights_to_fp16_manual(model, keep_io_types=True):
    """
    Manual approach to convert only the weights to FP16 while preserving graph structure
    """
    import numpy as np
    from onnx import numpy_helper
    
    # Create a copy of the model
    model_copy = onnx.ModelProto()
    model_copy.CopyFrom(model)
    
    # Convert initializers (weights) to FP16
    for initializer in model_copy.graph.initializer:
        if initializer.data_type == onnx.TensorProto.FLOAT:  # FP32
            # Get the numpy array
            weights = numpy_helper.to_array(initializer)
            
            # Convert to FP16
            weights_fp16 = weights.astype(np.float16)
            
            # Create new initializer
            new_initializer = numpy_helper.from_array(weights_fp16, initializer.name)
            
            # Replace the old initializer
            initializer.CopyFrom(new_initializer)
    
    # Optionally keep input/output types as FP32
    if keep_io_types:
        # Ensure inputs remain FP32
        for input_info in model_copy.graph.input:
            if input_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
                input_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
        
        # Ensure outputs remain FP32  
        for output_info in model_copy.graph.output:
            if output_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
                output_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    return model_copy

def convert_plus_ultra_models(input_dir=".", output_dir=None, keep_io_types=True):
    """
    Convert specific Plus_Ultra models to FP16 based on your model dimensions
    Args:
        input_dir: Directory containing Plus_Ultra ONNX models
        output_dir: Directory to save converted models (optional, same as input if None)
        keep_io_types: Keep input/output as FP32 for compatibility
    """
    if output_dir is None:
        output_dir = input_dir
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Define the expected model dimensions (width x height format for naming)
    expected_models = [
        "Plus_Ultra_1280x720.onnx",   # 16:9 landscape
        "Plus_Ultra_1280x960.onnx",   # 4:3 landscape  
        "Plus_Ultra_720x1280.onnx",   # 9:16 portrait
        "Plus_Ultra_960x1280.onnx",   # 3:4 portrait
        "Plus_Ultra_800x800.onnx"     # 1:1 square
    ]
    
    print("Converting Plus_Ultra models to FP16:")
    print("=" * 60)
    
    successful_conversions = 0
    failed_conversions = 0
    found_models = []
    missing_models = []
    
    for model_name in expected_models:
        model_path = os.path.join(input_dir, model_name)
        
        if os.path.exists(model_path):
            found_models.append(model_path)
            
            # Generate output path
            base_name = model_name.replace('.onnx', '')
            output_filename = f"{base_name}_fp16.onnx"
            output_path = os.path.join(output_dir, output_filename)
            
            # Convert model
            result = convert_onnx_to_fp16(model_path, output_path, keep_io_types)
            
            if result:
                successful_conversions += 1
            else:
                failed_conversions += 1
                
        else:
            missing_models.append(model_name)
    
    print("=" * 60)
    print(f"Conversion Summary:")
    print(f"  Found models: {len(found_models)}")
    print(f"  Successful conversions: {successful_conversions}")
    print(f"  Failed conversions: {failed_conversions}")
    
    if missing_models:
        print(f"  Missing models: {len(missing_models)}")
        print("  Missing files:")
        for missing in missing_models:
            print(f"    - {missing}")
    
    if found_models:
        print("  Converted models:")
        for model in found_models:
            base_name = os.path.basename(model).replace('.onnx', '')
            print(f"    ✓ {base_name}_fp16.onnx")

def convert_directory(input_dir, output_dir=None, pattern="*.onnx", keep_io_types=True):
    """
    Convert all ONNX models in a directory to FP16
    Args:
        input_dir: Directory containing ONNX models
        output_dir: Directory to save converted models (optional, same as input if None)
        pattern: File pattern to match (default: "*.onnx")
        keep_io_types: Keep input/output as FP32 for compatibility
    """
    if output_dir is None:
        output_dir = input_dir
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all ONNX files
    search_pattern = os.path.join(input_dir, pattern)
    onnx_files = glob.glob(search_pattern)
    
    if not onnx_files:
        print(f"No ONNX files found in {input_dir} with pattern {pattern}")
        return
    
    print(f"Found {len(onnx_files)} ONNX files to convert")
    print("=" * 60)
    
    successful_conversions = 0
    failed_conversions = 0
    
    for onnx_file in onnx_files:
        # Skip files that are already FP16
        if '_fp16' in os.path.basename(onnx_file):
            print(f"Skipping {onnx_file} (already FP16)")
            continue
            
        # Generate output path
        filename = os.path.basename(onnx_file)
        base_name = filename.replace('.onnx', '')
        output_filename = f"{base_name}_fp16.onnx"
        output_path = os.path.join(output_dir, output_filename)
        
        # Convert model
        result = convert_onnx_to_fp16(onnx_file, output_path, keep_io_types)
        
        if result:
            successful_conversions += 1
        else:
            failed_conversions += 1
        
        print("-" * 40)
    
    print(f"\nConversion Summary:")
    print(f"  Successful: {successful_conversions}")
    print(f"  Failed: {failed_conversions}")
    print(f"  Total: {len(onnx_files)}")

def main():
    parser = argparse.ArgumentParser(description='Convert ONNX models from FP32 to FP16')
    parser.add_argument('--input', '-i', type=str, default='.',
                       help='Input ONNX file or directory containing ONNX files (default: current directory)')
    parser.add_argument('--output', '-o', type=str, 
                       help='Output file/directory (optional, auto-generated if not provided)')
    parser.add_argument('--pattern', '-p', type=str, default='*.onnx',
                       help='File pattern for directory mode (default: *.onnx)')
    parser.add_argument('--keep-io-fp32', action='store_true', default=True,
                       help='Keep input/output tensors as FP32 for compatibility (default: True)')
    parser.add_argument('--full-fp16', action='store_true',
                       help='Convert everything to FP16 including inputs/outputs')
    parser.add_argument('--plus-ultra', action='store_true', default=True,
                       help='Convert specific Plus_Ultra models (default: True)')
    parser.add_argument('--all-models', action='store_true',
                       help='Convert all ONNX models in directory (overrides --plus-ultra)')
    
    args = parser.parse_args()
    
    # Determine keep_io_types
    keep_io_types = not args.full_fp16
    
    if os.path.isfile(args.input):
        # Single file conversion
        print(f"Converting single file: {args.input}")
        convert_onnx_to_fp16(args.input, args.output, keep_io_types)
    elif os.path.isdir(args.input):
        if args.all_models:
            # Convert all models in directory
            print(f"Converting all ONNX models in directory: {args.input}")
            convert_directory(args.input, args.output, args.pattern, keep_io_types)
        else:
            # Convert specific Plus_Ultra models (default behavior)
            print(f"Converting Plus_Ultra models in directory: {args.input}")
            convert_plus_ultra_models(args.input, args.output, keep_io_types)
    else:
        print(f"Error: {args.input} is not a valid file or directory")

if __name__ == "__main__":
    main()