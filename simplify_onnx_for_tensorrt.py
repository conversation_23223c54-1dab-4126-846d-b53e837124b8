#!/usr/bin/env python3
"""
ONNX Model Simplifier for TensorRT

This script simplifies ONNX models to make them more compatible with TensorRT
by removing problematic operations and optimizing the graph.

Usage:
    python simplify_onnx_for_tensorrt.py Plus_Ultra_800x800.onnx
"""

import os
import sys
import argparse
import glob

def simplify_onnx_model(input_path, output_path):
    """Simplify ONNX model for better TensorRT compatibility"""
    
    try:
        import onnx
        from onnxsim import simplify
        print("✓ onnx and onnxsim available")
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Install with: pip install onnx onnxsim")
        return False
    
    try:
        print(f"Loading ONNX model: {input_path}")
        model = onnx.load(input_path)
        
        print("Original model info:")
        print(f"  IR version: {model.ir_version}")
        print(f"  Producer: {model.producer_name}")
        print(f"  Nodes: {len(model.graph.node)}")
        
        # Get input info
        input_info = model.graph.input[0]
        print(f"  Input: {input_info.name}")
        
        # Simplify the model
        print("Simplifying model...")
        simplified_model, check = simplify(
            model,
            check_n=3,  # Check with 3 different inputs
            perform_optimization=True,
            skip_fuse_bn=False,
            skip_optimization=False,
            skip_shape_inference=False
        )
        
        if not check:
            print("⚠️ Simplification check failed, but continuing...")
        else:
            print("✓ Simplification check passed")
        
        print("Simplified model info:")
        print(f"  Nodes: {len(simplified_model.graph.node)}")
        
        # Save simplified model
        print(f"Saving simplified model: {output_path}")
        onnx.save(simplified_model, output_path)
        
        # Verify the saved model
        onnx.checker.check_model(output_path)
        print("✓ Model verification passed")
        
        # Compare file sizes
        original_size = os.path.getsize(input_path) / (1024 * 1024)
        simplified_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"File size: {original_size:.1f} MB → {simplified_size:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Simplification failed: {e}")
        return False

def convert_simplified_to_tensorrt(onnx_path, engine_path):
    """Convert simplified ONNX to TensorRT"""
    
    # Try to find trtexec
    import subprocess
    
    possible_paths = ["trtexec", "trtexec.exe"]
    trtexec_path = None
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, timeout=5)
            if result.returncode == 0:
                trtexec_path = path
                break
        except:
            continue
    
    if not trtexec_path:
        print("❌ trtexec not found")
        return False
    
    # Get input shape from filename
    if "800x800" in onnx_path:
        shape = "1x3x800x800"
    elif "1280x720" in onnx_path:
        shape = "1x3x720x1280"
    elif "1280x960" in onnx_path:
        shape = "1x3x960x1280"
    else:
        shape = "1x3x1280x1280"
    
    # Build conversion command
    cmd = [
        trtexec_path,
        f"--onnx={onnx_path}",
        f"--saveEngine={engine_path}",
        f"--shapes=input:{shape}",
        "--fp16",
        "--memPoolSize=workspace:1024",
        "--verbose"
    ]
    
    print(f"Converting to TensorRT...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ TensorRT conversion successful!")
            size_mb = os.path.getsize(engine_path) / (1024 * 1024)
            print(f"Engine created: {engine_path} ({size_mb:.1f} MB)")
            return True
        else:
            print("❌ TensorRT conversion failed!")
            print("STDERR:", result.stderr[-500:])
            return False
            
    except Exception as e:
        print(f"❌ Conversion error: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Simplify ONNX models for TensorRT')
    parser.add_argument('onnx_file', nargs='?', help='Input ONNX file')
    parser.add_argument('--output', type=str, help='Output simplified ONNX file')
    parser.add_argument('--engine', type=str, help='Output TensorRT engine file')
    parser.add_argument('--convert', action='store_true', help='Also convert to TensorRT')
    parser.add_argument('--list', action='store_true', help='List ONNX files')
    
    args = parser.parse_args()
    
    print("ONNX Simplifier for TensorRT")
    print("=" * 40)
    
    # List ONNX files
    onnx_files = sorted(glob.glob("*.onnx"))
    
    if args.list or not args.onnx_file:
        print("Available ONNX files:")
        for f in onnx_files:
            size_mb = os.path.getsize(f) / (1024 * 1024)
            print(f"  {f} ({size_mb:.1f} MB)")
        
        if args.list:
            return 0
        
        if onnx_files:
            args.onnx_file = onnx_files[0]
            print(f"\nUsing: {args.onnx_file}")
        else:
            print("No ONNX files found!")
            return 1
    
    if not os.path.exists(args.onnx_file):
        print(f"❌ File not found: {args.onnx_file}")
        return 1
    
    # Set output paths
    base_name = os.path.splitext(args.onnx_file)[0]
    
    if not args.output:
        args.output = f"{base_name}_simplified.onnx"
    
    if not args.engine:
        args.engine = f"{base_name}_simplified.plan"
    
    print(f"Input: {args.onnx_file}")
    print(f"Simplified ONNX: {args.output}")
    if args.convert:
        print(f"TensorRT Engine: {args.engine}")
    
    # Simplify the model
    success = simplify_onnx_model(args.onnx_file, args.output)
    
    if not success:
        print("❌ Simplification failed")
        return 1
    
    # Convert to TensorRT if requested
    if args.convert:
        print("\nConverting to TensorRT...")
        trt_success = convert_simplified_to_tensorrt(args.output, args.engine)
        
        if trt_success:
            print(f"\n🎉 Complete! Test with:")
            print(f"python run/InferenceTensorRT.py --source input.png --engine {args.engine}")
        else:
            print(f"\n⚠️ TensorRT conversion failed. Use ONNX instead:")
            print(f"python run/InferenceONNXTensorRT.py --onnx {args.output}")
    else:
        print(f"\n✅ Simplified ONNX created: {args.output}")
        print(f"To convert to TensorRT:")
        print(f"python simplify_onnx_for_tensorrt.py {args.onnx_file} --convert")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
