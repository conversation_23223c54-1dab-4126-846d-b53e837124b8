import numpy as np
from PIL import Image
import os
import time
import onnxruntime as ort
import argparse
import statistics

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    original_size = img.size  # (width, height)
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img, (original_size[1], original_size[0])  # Return (height, width)

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

def benchmark_model(model_path, image_path, num_runs=5, use_dml=True, warmup=2):
    """
    Benchmark ONNX model using DirectML if available
    Args:
        model_path: Path to the ONNX model
        image_path: Path to the input image
        num_runs: Number of inference runs for benchmarking
        use_dml: Whether to use DirectML acceleration
        warmup: Number of warmup runs before benchmarking
    """
    print(f"Starting benchmark of {model_path}")
    print(f"Available providers: {ort.get_available_providers()}")
    
    # Configure session options
    sess_options = ort.SessionOptions()
    sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    sess_options.log_severity_level = 0
    
    # Enable DirectML or use other available providers
    if use_dml and 'DmlExecutionProvider' in ort.get_available_providers():
        # Configure DirectML to use the specified GPU device
        dml_options = {'device_id': args.gpu_device} if hasattr(args, 'gpu_device') else {}
        providers = [('DmlExecutionProvider', dml_options), 'CPUExecutionProvider']
        print(f"Using DirectML acceleration with device_id: {dml_options.get('device_id', 0)}")
    elif 'CUDAExecutionProvider' in ort.get_available_providers():
        # Configure CUDA to use the specified GPU device
        cuda_options = {'device_id': args.gpu_device} if hasattr(args, 'gpu_device') else {}
        sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
        providers = [('CUDAExecutionProvider', cuda_options), 'CPUExecutionProvider']
        print(f"Using CUDA acceleration with device_id: {cuda_options.get('device_id', 0)}")
    else:
        providers = ['CPUExecutionProvider']
        sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
        print("Using CPU execution")
    
    # Create session
    session = ort.InferenceSession(model_path, sess_options, providers=providers)
    print(f"Active providers: {session.get_providers()}")
    
    # Get input name
    input_name = session.get_inputs()[0].name
    
    # Preprocess image
    img, original_size = preprocess_image(image_path)
    
    # Warmup runs
    print(f"Performing {warmup} warmup runs...")
    for _ in range(warmup):
        _ = session.run(None, {input_name: img})
    
    # Benchmark runs
    print(f"Running {num_runs} benchmark iterations...")
    inference_times = []
    
    for i in range(num_runs):
        # Run inference and measure time
        start_time = time.time()
        outputs = session.run(None, {input_name: img})
        end_time = time.time()
        
        inference_time = end_time - start_time
        inference_times.append(inference_time)
        
        print(f"Run {i+1}: {inference_time:.4f} seconds")
        
        # Save the output of the first run
        if i == 0:
            alpha = postprocess_output(outputs, original_size)
            output_filename = f"output_dml_{os.path.basename(image_path)}"
            alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
            alpha_img.save(output_filename)
            print(f"Saved output to {output_filename}")
    
    # Calculate statistics
    avg_time = statistics.mean(inference_times)
    min_time = min(inference_times)
    max_time = max(inference_times)
    std_dev = statistics.stdev(inference_times) if len(inference_times) > 1 else 0
    fps = 1.0 / avg_time
    
    # Print results
    print("\nBenchmark Results:")
    print(f"Average inference time: {avg_time:.4f} seconds")
    print(f"Min inference time: {min_time:.4f} seconds")
    print(f"Max inference time: {max_time:.4f} seconds")
    print(f"Standard deviation: {std_dev:.4f} seconds")
    print(f"Average FPS: {fps:.2f}")
    
    return inference_times

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Benchmark ONNX model using DirectML')
    parser.add_argument('--model', type=str, default='model_optimized.onnx',
                        help='Path to the ONNX model')
    parser.add_argument('--image', type=str, default='input.png',
                        help='Path to the input image')
    parser.add_argument('--runs', type=int, default=5,
                        help='Number of benchmark runs')
    parser.add_argument('--warmup', type=int, default=2,
                        help='Number of warmup runs')
    parser.add_argument('--no-dml', action='store_true', default=True,
                        help='Disable DirectML acceleration')
    parser.add_argument('--gpu-device', type=int, default=0,
                        help='GPU device ID to use (0, 1, etc.)')
    
    args = parser.parse_args()
    
    # Run benchmark
    benchmark_model(
        model_path=args.model,
        image_path=args.image,
        num_runs=args.runs,
        use_dml=not args.no_dml,
        warmup=args.warmup
    )