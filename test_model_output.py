#!/usr/bin/env python3
"""
Test Model Output

This script tests the model output to ensure it's producing proper alpha mattes
before and after FP16 conversion.

Usage:
    python test_model_output.py
    python test_model_output.py --model Plus_Ultra_800x800.onnx
"""

import os
import argparse
import numpy as np
import onnxruntime as ort
from PIL import Image
import matplotlib.pyplot as plt

def preprocess_image(image_path, target_size=(800, 800)):
    """Preprocess image for model input"""
    try:
        # Load and resize image
        img = Image.open(image_path).convert('RGB')
        original_size = img.size
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy array and normalize
        img = np.array(img, dtype=np.float32)
        img = img / 255.0
        img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
        
        # Transpose to CHW format and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        return img, original_size
    except Exception as e:
        print(f"Error preprocessing image: {e}")
        return None, None

def test_model_output(model_path, image_path="input.png", save_output=True):
    """Test model output and analyze the results"""
    
    print(f"Testing model: {model_path}")
    print("-" * 50)
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    if not os.path.exists(image_path):
        print(f"❌ Input image not found: {image_path}")
        return False
    
    try:
        # Load model
        providers = ['CPUExecutionProvider']  # Use CPU for consistent testing
        session = ort.InferenceSession(model_path, providers=providers)
        
        print(f"✓ Model loaded successfully")
        print(f"  Provider: {session.get_providers()[0]}")
        
        # Get model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        
        print(f"  Input: {input_info.name}, shape: {input_info.shape}, type: {input_info.type}")
        print(f"  Output: {output_info.name}, shape: {output_info.shape}, type: {output_info.type}")
        
        # Determine target size from model input shape
        if len(input_info.shape) >= 4:
            target_size = (input_info.shape[2], input_info.shape[3])  # H, W
        else:
            target_size = (800, 800)  # Default
        
        print(f"  Target size: {target_size}")
        
        # Preprocess image
        img_tensor, original_size = preprocess_image(image_path, target_size)
        if img_tensor is None:
            return False
        
        print(f"✓ Image preprocessed successfully")
        print(f"  Input tensor shape: {img_tensor.shape}")
        print(f"  Input tensor range: [{np.min(img_tensor):.3f}, {np.max(img_tensor):.3f}]")
        
        # Run inference
        outputs = session.run(None, {input_info.name: img_tensor})
        output = outputs[0]
        
        print(f"✓ Inference completed successfully")
        print(f"  Output shape: {output.shape}")
        print(f"  Output dtype: {output.dtype}")
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        print(f"  Output statistics:")
        print(f"    Range: [{output_min:.6f}, {output_max:.6f}]")
        print(f"    Mean: {output_mean:.6f}")
        print(f"    Std: {output_std:.6f}")
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        
        if nan_count > 0:
            print(f"  ❌ Found {nan_count} NaN values!")
            return False
        if inf_count > 0:
            print(f"  ❌ Found {inf_count} Inf values!")
            return False
        
        # Check value distribution
        zero_count = np.sum(output < 0.01)
        one_count = np.sum(output > 0.99)
        mid_count = np.sum((output >= 0.01) & (output <= 0.99))
        total_pixels = output.size
        
        print(f"  Value distribution:")
        print(f"    Near 0 (< 0.01): {zero_count} ({zero_count/total_pixels*100:.1f}%)")
        print(f"    Near 1 (> 0.99): {one_count} ({one_count/total_pixels*100:.1f}%)")
        print(f"    Middle (0.01-0.99): {mid_count} ({mid_count/total_pixels*100:.1f}%)")
        
        # Diagnose issues
        if one_count / total_pixels > 0.95:
            print(f"  ⚠️ WARNING: {one_count/total_pixels*100:.1f}% of pixels are near 1.0 (white)")
            print(f"     This suggests over-normalization or saturation")
        elif zero_count / total_pixels > 0.95:
            print(f"  ⚠️ WARNING: {zero_count/total_pixels*100:.1f}% of pixels are near 0.0 (black)")
            print(f"     This suggests under-normalization or clipping")
        elif mid_count / total_pixels < 0.1:
            print(f"  ⚠️ WARNING: Only {mid_count/total_pixels*100:.1f}% of pixels are in middle range")
            print(f"     This suggests poor contrast or binary output")
        else:
            print(f"  ✓ Good value distribution - model appears to be working correctly")
        
        # Save output if requested
        if save_output:
            # Remove batch dimension and convert to 2D if needed
            alpha = output.squeeze()
            if len(alpha.shape) > 2:
                alpha = alpha[0] if alpha.shape[0] == 1 else alpha
            
            # Convert to image
            alpha_img = (alpha * 255).astype(np.uint8)
            
            # Save alpha matte
            base_name = os.path.splitext(os.path.basename(model_path))[0]
            output_path = f"test_output_{base_name}.png"
            Image.fromarray(alpha_img).save(output_path)
            print(f"  ✓ Alpha matte saved: {output_path}")
            
            # Create visualization
            create_visualization(image_path, alpha, output_path.replace('.png', '_viz.png'))
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_visualization(original_image_path, alpha, output_path):
    """Create a visualization showing original, alpha, and composite"""
    try:
        # Load original image
        original = Image.open(original_image_path).convert('RGB')
        original = original.resize((alpha.shape[1], alpha.shape[0]), Image.BILINEAR)
        original = np.array(original) / 255.0
        
        # Create composite with green background
        green_bg = np.ones_like(original) * [0.0, 1.0, 0.0]  # Green background
        alpha_3d = np.stack([alpha] * 3, axis=-1)
        composite = original * alpha_3d + green_bg * (1 - alpha_3d)
        
        # Create visualization
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].imshow(original)
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        axes[1].imshow(alpha, cmap='gray')
        axes[1].set_title('Alpha Matte')
        axes[1].axis('off')
        
        axes[2].imshow(composite)
        axes[2].set_title('Composite (Green BG)')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"  ✓ Visualization saved: {output_path}")
        
    except Exception as e:
        print(f"  ⚠️ Could not create visualization: {e}")

def test_all_models():
    """Test all available models"""
    
    # Look for ONNX models
    model_patterns = [
        "*.onnx"
    ]
    
    import glob
    found_models = []
    for pattern in model_patterns:
        found_models.extend(glob.glob(pattern))
    
    # Filter out some test outputs
    models_to_test = [m for m in found_models if not any(skip in m for skip in ['test_', 'temp_', 'debug_'])]
    
    if not models_to_test:
        print("No ONNX models found to test")
        return
    
    print(f"Found {len(models_to_test)} models to test:")
    for model in models_to_test:
        print(f"  {model}")
    
    print("\n" + "=" * 60)
    print("TESTING ALL MODELS")
    print("=" * 60)
    
    results = {}
    for model_path in models_to_test:
        print(f"\n{'='*60}")
        success = test_model_output(model_path)
        results[model_path] = success
        print(f"{'='*60}")
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    for model_path, success in results.items():
        status = "✓ PASS" if success else "❌ FAIL"
        print(f"  {status}: {model_path}")
    
    if passed == total:
        print(f"\n🎉 All models passed testing!")
    else:
        print(f"\n⚠️ {total - passed} models failed testing")

def main():
    parser = argparse.ArgumentParser(description='Test model output quality')
    parser.add_argument('--model', '-m', type=str, 
                       help='Specific model to test (if not specified, tests all models)')
    parser.add_argument('--image', '-i', type=str, default='input.png',
                       help='Input image path (default: input.png)')
    parser.add_argument('--no-save', action='store_true',
                       help='Do not save output images')
    
    args = parser.parse_args()
    
    print("Model Output Tester")
    print("=" * 30)
    print("This script tests model outputs to diagnose issues with alpha matte generation")
    print("=" * 30)
    
    if args.model:
        # Test specific model
        success = test_model_output(args.model, args.image, not args.no_save)
        if success:
            print(f"\n✅ Model test completed successfully")
        else:
            print(f"\n❌ Model test failed")
    else:
        # Test all models
        test_all_models()

if __name__ == "__main__":
    main()