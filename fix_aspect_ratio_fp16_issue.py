#!/usr/bin/env python3
"""
Fix Aspect Ratio FP16 Issue

This script diagnoses and fixes the NaN issue that occurs when using
different aspect ratios with TensorRT FP16 engines.

The issue: 1280x720 FP16 engine produces NaN values while FP32 works fine.
The cause: Different aspect ratios stress the model differently in FP16 precision.

Usage:
    conda activate onnxgpu
    python fix_aspect_ratio_fp16_issue.py
"""

import os
import sys
import subprocess
import numpy as np
from PIL import Image

def diagnose_aspect_ratio_issue():
    """
    Diagnose the aspect ratio FP16 issue by testing different models
    """
    print("Aspect Ratio FP16 Issue Diagnosis")
    print("=" * 50)
    
    # Test different aspect ratios
    test_models = [
        ("InsPyReNet_800x800.onnx", "InsPyReNet_800x800_fp16.engine", "800x800 (1:1)"),
        ("InsPyReNet_1280x720.onnx", "InsPyReNet_1280x720_fp16.engine", "1280x720 (16:9)"),
        ("InsPyReNet_1280x960.onnx", "InsPyReNet_1280x960_fp16.engine", "1280x960 (4:3)"),
        ("InsPyReNet_720x1280.onnx", "InsPyReNet_720x1280_fp16.engine", "720x1280 (9:16)"),
        ("InsPyReNet_960x1280.onnx", "InsPyReNet_960x1280_fp16.engine", "960x1280 (3:4)"),
    ]
    
    results = {}
    
    for onnx_model, engine_model, description in test_models:
        print(f"\nTesting {description}:")
        print("-" * 30)
        
        # Check if files exist
        onnx_exists = os.path.exists(onnx_model)
        engine_exists = os.path.exists(engine_model)
        
        print(f"ONNX model: {'✓' if onnx_exists else '✗'} {onnx_model}")
        print(f"Engine: {'✓' if engine_exists else '✗'} {engine_model}")
        
        if not onnx_exists:
            results[description] = "ONNX missing"
            continue
        
        if not engine_exists:
            results[description] = "Engine missing"
            continue
        
        # Test the engine
        result = test_engine_for_nan(engine_model)
        results[description] = result
        
        if result == "NaN detected":
            print(f"❌ {description}: NaN issue detected")
        elif result == "Working":
            print(f"✅ {description}: Working correctly")
        else:
            print(f"⚠️ {description}: {result}")
    
    # Summary
    print(f"\n{'='*50}")
    print("DIAGNOSIS SUMMARY")
    print('='*50)
    
    working_models = []
    nan_models = []
    
    for desc, result in results.items():
        if result == "Working":
            working_models.append(desc)
        elif result == "NaN detected":
            nan_models.append(desc)
    
    print(f"\n✅ Working models: {len(working_models)}")
    for model in working_models:
        print(f"  {model}")
    
    print(f"\n❌ Models with NaN issues: {len(nan_models)}")
    for model in nan_models:
        print(f"  {model}")
    
    return nan_models

def test_engine_for_nan(engine_path):
    """
    Test a TensorRT engine for NaN output
    """
    try:
        # Use the inference script to test
        cmd = f"python InferenceTensorRT.py --source input.png --engine {engine_path} --dest temp_test_output.png"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode != 0:
            return "Inference failed"
        
        # Check if output file was created and is valid
        if os.path.exists("temp_test_output.png"):
            try:
                img = np.array(Image.open("temp_test_output.png"))
                
                # Check for NaN-like issues (all black, all white, etc.)
                if np.all(img == 0):
                    return "All black output"
                elif np.all(img == 255):
                    return "All white output"
                elif np.std(img) < 1:
                    return "No contrast"
                else:
                    return "Working"
            except Exception:
                return "Invalid output file"
            finally:
                # Clean up
                if os.path.exists("temp_test_output.png"):
                    os.remove("temp_test_output.png")
        else:
            return "No output file"
            
    except Exception as e:
        return f"Test failed: {str(e)[:50]}"

def create_conservative_fp16_engines(nan_models):
    """
    Create more conservative FP16 engines for models with NaN issues
    """
    print(f"\n{'='*50}")
    print("CREATING CONSERVATIVE FP16 ENGINES")
    print('='*50)
    
    if not nan_models:
        print("No models need fixing!")
        return
    
    print(f"Creating conservative FP16 engines for {len(nan_models)} problematic models...")
    
    # Map descriptions to actual file names
    model_mapping = {
        "1280x720 (16:9)": ("InsPyReNet_1280x720.onnx", "InsPyReNet_1280x720_fp16_conservative.engine"),
        "1280x960 (4:3)": ("InsPyReNet_1280x960.onnx", "InsPyReNet_1280x960_fp16_conservative.engine"),
        "720x1280 (9:16)": ("InsPyReNet_720x1280.onnx", "InsPyReNet_720x1280_fp16_conservative.engine"),
        "960x1280 (3:4)": ("InsPyReNet_960x1280.onnx", "InsPyReNet_960x1280_fp16_conservative.engine"),
        "800x800 (1:1)": ("InsPyReNet_800x800.onnx", "InsPyReNet_800x800_fp16_conservative.engine"),
    }
    
    for model_desc in nan_models:
        if model_desc in model_mapping:
            onnx_path, engine_path = model_mapping[model_desc]
            
            print(f"\nCreating conservative engine for {model_desc}...")
            
            if create_conservative_engine(onnx_path, engine_path):
                print(f"✅ Conservative engine created: {engine_path}")
                
                # Test the new engine
                result = test_engine_for_nan(engine_path)
                if result == "Working":
                    print(f"✅ Conservative engine works correctly!")
                else:
                    print(f"⚠️ Conservative engine still has issues: {result}")
            else:
                print(f"❌ Failed to create conservative engine")

def create_conservative_engine(onnx_path, engine_path):
    """
    Create a conservative FP16 engine with more operations kept in FP32
    """
    try:
        # Use trtexec with conservative FP16 settings
        cmd = [
            "trtexec",
            f"--onnx={onnx_path}",
            f"--saveEngine={engine_path}",
            "--fp16",
            "--memPoolSize=workspace:2048",  # 2GB workspace
            "--verbose",
            "--noTF32",  # Disable TF32 for more predictable behavior
            "--strictTypes",  # Strict type checking
        ]
        
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0 and os.path.exists(engine_path):
            return True
        else:
            print(f"trtexec failed: {result.stderr[:500]}")
            return False
            
    except subprocess.TimeoutExpired:
        print("trtexec timed out")
        return False
    except Exception as e:
        print(f"Error running trtexec: {e}")
        return False

def create_mixed_precision_engines(nan_models):
    """
    Alternative approach: Create mixed precision engines using the convert script
    """
    print(f"\n{'='*50}")
    print("CREATING MIXED PRECISION ENGINES")
    print('='*50)
    
    model_mapping = {
        "1280x720 (16:9)": "InsPyReNet_1280x720.onnx",
        "1280x960 (4:3)": "InsPyReNet_1280x960.onnx", 
        "720x1280 (9:16)": "InsPyReNet_720x1280.onnx",
        "960x1280 (3:4)": "InsPyReNet_960x1280.onnx",
        "800x800 (1:1)": "InsPyReNet_800x800.onnx",
    }
    
    for model_desc in nan_models:
        if model_desc in model_mapping:
            onnx_path = model_mapping[model_desc]
            engine_path = onnx_path.replace('.onnx', '_fp16_mixed.engine')
            
            print(f"\nCreating mixed precision engine for {model_desc}...")
            
            # Use the convert script with workspace size and conservative settings
            cmd = f"python convert_onnx_to_tensorrt.py --onnx {onnx_path} --fp16 --engine {engine_path} --workspace 4G"
            
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0 and os.path.exists(engine_path):
                    print(f"✅ Mixed precision engine created: {engine_path}")
                    
                    # Test the new engine
                    test_result = test_engine_for_nan(engine_path)
                    if test_result == "Working":
                        print(f"✅ Mixed precision engine works correctly!")
                    else:
                        print(f"⚠️ Mixed precision engine still has issues: {test_result}")
                else:
                    print(f"❌ Failed to create mixed precision engine")
                    
            except subprocess.TimeoutExpired:
                print("Conversion timed out")
            except Exception as e:
                print(f"Error: {e}")

def provide_workaround_solutions():
    """
    Provide alternative solutions for aspect ratio FP16 issues
    """
    print(f"\n{'='*50}")
    print("WORKAROUND SOLUTIONS")
    print('='*50)
    
    print(f"\nIf FP16 engines still have NaN issues, here are alternatives:")
    
    print(f"\n1. Use FP32 engines for problematic aspect ratios:")
    print(f"   python convert_onnx_to_tensorrt.py --onnx InsPyReNet_1280x720.onnx --fp32")
    print(f"   (FP32 is slower but more stable)")
    
    print(f"\n2. Use the working 800x800 FP16 engine and resize:")
    print(f"   - Resize input to 800x800")
    print(f"   - Use InsPyReNet_800x800_fp16.engine")
    print(f"   - Resize output back to desired size")
    
    print(f"\n3. Use ONNX inference instead of TensorRT:")
    print(f"   python use_onnx_cuda_fp32.py")
    print(f"   (Uses ONNX Runtime with CUDA)")
    
    print(f"\n4. Use PyTorch inference:")
    print(f"   python run/Inference.py --source input.png")
    print(f"   (Original PyTorch model)")
    
    print(f"\n5. Modify TensorRT build settings:")
    print(f"   - Use larger workspace size")
    print(f"   - Enable strict types")
    print(f"   - Disable TF32")

def main():
    print("Aspect Ratio FP16 Issue Fixer")
    print("=" * 60)
    print("This script diagnoses and fixes NaN issues in FP16 TensorRT engines")
    print("for different aspect ratios.")
    print("=" * 60)
    
    # Step 1: Diagnose the issue
    nan_models = diagnose_aspect_ratio_issue()
    
    if not nan_models:
        print(f"\n🎉 All models are working correctly!")
        print(f"No fixes needed.")
        return 0
    
    # Step 2: Try to fix the problematic models
    print(f"\nAttempting to fix {len(nan_models)} problematic models...")
    
    # Try conservative approach first
    create_conservative_fp16_engines(nan_models)
    
    # Try mixed precision approach
    create_mixed_precision_engines(nan_models)
    
    # Step 3: Provide workarounds
    provide_workaround_solutions()
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    
    print(f"\nThe aspect ratio FP16 issue is caused by:")
    print(f"  • Different aspect ratios stressing the model differently")
    print(f"  • FP16 precision limits being exceeded")
    print(f"  • Vision Transformer components being sensitive to precision")
    
    print(f"\nRecommended solutions:")
    print(f"  1. Use the working 800x800 FP16 engine")
    print(f"  2. Use FP32 engines for problematic aspect ratios")
    print(f"  3. Use conservative FP16 engines if created successfully")
    
    print(f"\nThe 800x800 FP16 engine works reliably and can be used")
    print(f"for most use cases by resizing inputs/outputs as needed.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())