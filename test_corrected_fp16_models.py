#!/usr/bin/env python3
"""
Test Corrected FP16 Models

This script tests all the newly created FP16 models to verify they work correctly
and don't produce NaN values.

Usage:
    python test_corrected_fp16_models.py
"""

import os
import glob
import numpy as np
import onnxruntime as ort
from PIL import Image
import time

def preprocess_image(image_path, target_size=(800, 800)):
    """Preprocess image for inference"""
    try:
        img = Image.open(image_path).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy and normalize
        img = np.array(img, dtype=np.float32) / 255.0
        img = (img - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
        
        # CHW format and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        return img
    except Exception as e:
        print(f"❌ Preprocessing failed: {e}")
        return None

def postprocess_output_safe(output, debug=False):
    """Safe postprocessing that handles FP16 issues"""
    alpha = output[0]
    
    if debug:
        print(f"    Raw output shape: {alpha.shape}")
        print(f"    Raw output dtype: {alpha.dtype}")
        print(f"    Raw output range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
    
    # Convert to FP32 if needed
    if alpha.dtype == np.float16:
        alpha = alpha.astype(np.float32)
    
    alpha = alpha.squeeze()
    
    # Check for NaN/Inf
    nan_count = np.isnan(alpha).sum()
    inf_count = np.isinf(alpha).sum()
    
    if nan_count > 0 or inf_count > 0:
        print(f"    ❌ Found {nan_count} NaN and {inf_count} Inf values")
        alpha = np.nan_to_num(alpha, nan=0.0, posinf=1.0, neginf=0.0)
        print(f"    🔧 Replaced with safe values")
    
    # Apply sigmoid if needed (raw logits)
    if np.min(alpha) < -1 or np.max(alpha) > 2:
        alpha = 1.0 / (1.0 + np.exp(-np.clip(alpha, -500, 500)))
    
    # Normalize
    alpha_min, alpha_max = np.min(alpha), np.max(alpha)
    if alpha_max > alpha_min:
        alpha = (alpha - alpha_min) / (alpha_max - alpha_min + 1e-8)
    
    alpha = np.clip(alpha, 0.0, 1.0)
    
    if debug:
        print(f"    Final range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"    Final mean: {np.mean(alpha):.6f}")
    
    return alpha

def test_model(model_path, test_image="input.png"):
    """Test a single FP16 model"""
    print(f"\n📋 Testing: {os.path.basename(model_path)}")
    print("-" * 50)
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return False
    
    # Get model size
    size_mb = os.path.getsize(model_path) / (1024 * 1024)
    print(f"  Model size: {size_mb:.1f} MB")
    
    try:
        # Create session
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        active_provider = session.get_providers()[0]
        print(f"  Using provider: {active_provider}")
        
        # Get model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        print(f"  Input: {input_info.name}, shape: {input_info.shape}")
        print(f"  Output: {output_info.name}, shape: {output_info.shape}")
        
        # Determine target size from model name
        if "800x800" in model_path:
            target_size = (800, 800)
        elif "1280x720" in model_path:
            target_size = (1280, 720)
        elif "1280x960" in model_path:
            target_size = (1280, 960)
        elif "720x1280" in model_path:
            target_size = (720, 1280)
        elif "960x1280" in model_path:
            target_size = (960, 1280)
        else:
            target_size = (800, 800)  # Default
        
        print(f"  Target size: {target_size}")
        
        # Preprocess image
        img = preprocess_image(test_image, target_size)
        if img is None:
            print(f"❌ Failed to preprocess image")
            return False
        
        # Run inference
        start_time = time.time()
        outputs = session.run(None, {input_info.name: img})
        inference_time = time.time() - start_time
        
        print(f"  ✓ Inference completed in {inference_time:.4f}s")
        
        # Process output
        alpha = postprocess_output_safe(outputs, debug=True)
        
        # Validate output
        if np.isnan(alpha).any() or np.isinf(alpha).any():
            print(f"  ❌ Output contains NaN/Inf values")
            return False
        
        if np.min(alpha) < 0 or np.max(alpha) > 1:
            print(f"  ⚠️ Output range outside [0,1]: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        
        # Save test output
        output_name = f"test_output_{os.path.basename(model_path).replace('.onnx', '.png')}"
        alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha_img.save(output_name)
        print(f"  ✓ Test output saved: {output_name}")
        
        print(f"  ✅ Model test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Model test FAILED: {e}")
        return False

def main():
    print("Testing Corrected FP16 Models")
    print("=" * 60)
    
    # Check if test image exists
    test_image = "input.png"
    if not os.path.exists(test_image):
        print(f"⚠️ Test image not found: {test_image}")
        print("Creating a dummy test image...")
        
        # Create a dummy test image
        dummy_img = np.random.randint(0, 255, (800, 800, 3), dtype=np.uint8)
        Image.fromarray(dummy_img).save(test_image)
        print(f"✓ Created dummy test image: {test_image}")
    
    # Find all FP16 models
    fp16_models = sorted(glob.glob("*_fp16.onnx"))
    
    if not fp16_models:
        print("❌ No FP16 models found!")
        print("Expected models:")
        expected = [
            "Plus_Ultra_800x800_fp16.onnx",
            "Plus_Ultra_1280x720_fp16.onnx",
            "Plus_Ultra_1280x960_fp16.onnx",
            "Plus_Ultra_720x1280_fp16.onnx",
            "Plus_Ultra_960x1280_fp16.onnx"
        ]
        for model in expected:
            status = "✓" if os.path.exists(model) else "❌"
            print(f"  {status} {model}")
        return 1
    
    print(f"Found {len(fp16_models)} FP16 models:")
    for model in fp16_models:
        print(f"  {model}")
    
    # Test each model
    passed = 0
    failed = 0
    
    for model in fp16_models:
        success = test_model(model, test_image)
        if success:
            passed += 1
        else:
            failed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Total models tested: {len(fp16_models)}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All models passed! Your corrected FP16 models are working properly.")
        print("\nYou can now use them with:")
        print("python use_onnx_cuda_fp16_fixed.py")
    else:
        print(f"\n⚠️ {failed} models failed. Consider:")
        print("1. Using the model fixer: python fix_fp16_onnx_models.py --test-all")
        print("2. Converting back to FP32")
        print("3. Using the original PyTorch models")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
