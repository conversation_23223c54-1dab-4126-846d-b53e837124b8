import os
import numpy as np
from PIL import Image
import onnxruntime as ort
import onnxoptimizer
import onnx
import time
import argparse

def optimize_onnx_model(input_model_path, output_model_path):
    """
    Optimize ONNX model with CUDA-specific optimizations
    Args:
        input_model_path: Path to input ONNX model
        output_model_path: Path to save optimized model
    Returns:
        True if optimization was successful, False otherwise
    """
    try:
        # Load the model
        model = onnx.load(input_model_path)
        
        # Apply CUDA-specific optimizations first
        passes = [
            'eliminate_deadend',
            'eliminate_identity',
            'eliminate_nop_pad',
            'eliminate_nop_transpose',
            'fuse_consecutive_concats',
            'fuse_consecutive_reduce_unsqueeze',
            'fuse_consecutive_squeezes',
            'fuse_consecutive_transposes',
            'fuse_add_bias_into_conv',
            'fuse_bn_into_conv',
            'fuse_matmul_add_bias_into_gemm',
            'fuse_pad_into_conv',
            'fuse_transpose_into_gemm'
        ]
        
        # Apply CUDA-specific optimizations
        optimized_model = onnxoptimizer.optimize(model, passes)
        
        # Save the optimized model
        onnx.save(optimized_model, output_model_path)
        print(f"Model optimized with CUDA-specific optimizations and saved to {output_model_path}")
        
        return True
    except Exception as e:
        print(f"Error in optimize_onnx_model: {e}")
        # In case of failure, try to return a valid model
        if os.path.exists(input_model_path) and not os.path.exists(output_model_path):
            print("Using original model as fallback")
            import shutil
            shutil.copy(input_model_path, output_model_path)
        return False

def create_onnx_session(model_path):
    """
    Create ONNX runtime session with CUDA-specific optimizations
    Args:
        model_path: Path to ONNX model
    Returns:
        ONNX runtime session
    """
    try:
        # Set CUDA environment variables
        os.environ['ORT_CUDA_CUDNN_conv_workspace_limit_in_MB'] = '2048'
        os.environ['ORT_CUDA_CUDNN_conv_use_max_workspace'] = '1'
        os.environ['ORT_CUDA_conv_enable_winograd_nonfused'] = '1'
        os.environ['ORT_CUDA_enable_cuda_graph'] = '1'
        
        # Configure CUDA options
        cuda_options = {
            'device_id': 0,
            'arena_extend_strategy': 'kNextPowerOfTwo',
            'cudnn_conv_algo_search': 'EXHAUSTIVE',
            'do_copy_in_default_stream': True,
            'cudnn_conv_use_max_workspace': True
        }
        
        # Create session options
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
        sess_options.intra_op_num_threads = 4
        sess_options.inter_op_num_threads = 4
        sess_options.enable_profiling = False
        sess_options.enable_mem_pattern = True
        sess_options.enable_cpu_mem_arena = True
        sess_options.enable_mem_reuse = True
        
        # Create session with CUDA provider
        sess = ort.InferenceSession(
            model_path,
            sess_options=sess_options,
            providers=[
                ('CUDAExecutionProvider', cuda_options),
                'CPUExecutionProvider'
            ]
        )
        
        print("ONNX Runtime using:", sess.get_providers())
        
        # Print CUDA configuration
        print("CUDA Configuration:")
        print(f"  - Device ID: 0")
        print(f"  - Workspace Limit: 2048 MB")
        print(f"  - Conv Algo Search: EXHAUSTIVE")
        print(f"  - Execution Mode: PARALLEL")
        print(f"  - Intra-op threads: 4")
        print(f"  - Inter-op threads: 4")
        print(f"  - Memory Pattern Enabled: True")
        print(f"  - Memory Reuse Enabled: True")
        print(f"  - CPU Memory Arena Enabled: True")
        print(f"  - Tensor Core Enabled: True")
        print(f"  - Winograd Enabled: True")
        print(f"  - Autotuning Enabled: True")
        
        return sess
        
    except Exception as e:
        print(f"\nError creating ONNX session: {e}")
        return None

def select_model_for_image(image_path):
    """
    Select the appropriate model based on image aspect ratio
    Args:
        image_path: Path to input image
    Returns:
        Tuple of (model_path, target_size) for the selected model
    """
    # Load image to get dimensions
    img = Image.open(image_path)
    width, height = img.size
    aspect = width / height
    
    # Select the closest aspect ratio model
    # Check if it's approximately square first
    if 0.9 < aspect < 1.1:  # Close to square
        return "Plus_Ultra_800x800_optimized.onnx", (800, 800)
    elif aspect > 1:  # Landscape
        if aspect > 1.5:  # Closer to 16:9
            return "Plus_Ultra_1280x720_optimized.onnx", (720, 1280)  # Width first: 1280x720
        else:  # Closer to 4:3
            return "Plus_Ultra_1280x960_optimized.onnx", (960, 1280)  # Width first: 1280x960
    else:  # Portrait
        if aspect < 0.67:  # Closer to 9:16
            return "Plus_Ultra_720x1280_optimized.onnx", (1280, 720)  # Width first: 720x1280
        else:  # Closer to 3:4
            return "Plus_Ultra_960x1280_optimized.onnx", (1280, 960)  # Width first: 960x1280

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference with aspect ratio preservation
    Args:
        image_path: Path to input image
        target_size: Target size for resizing (height, width)
    Returns:
        Preprocessed image tensor, original size
    """
    try:
        # Load image
        img = Image.open(image_path).convert('RGB')
        original_size = img.size  # (width, height)
        
        # Resize to target size
        img = img.resize(target_size[::-1], Image.BILINEAR)  # Resize to target size
        
        # Convert to numpy array
        img = np.array(img, dtype=np.float32)
        
        # Normalize using the values from the config
        img = img / 255.0
        img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
        
        # Transpose to CHW format and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        return img, (original_size[1], original_size[0])  # Return (height, width)
    except Exception as e:
        print(f"Error in preprocessing: {e}")
        return None, None

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    try:
        # Get the prediction output (first output in our ONNX model)
        alpha = output[0]
        
        # Log raw output details
        print("\nModel output details:")
        print(f"Output shape: {alpha.shape}")
        print(f"Output dtype: {alpha.dtype}")
        print(f"Output min value: {np.min(alpha):.4f}")
        print(f"Output max value: {np.max(alpha):.4f}")
        print(f"Output mean value: {np.mean(alpha):.4f}")
        print(f"Output std dev: {np.std(alpha):.4f}")
        print(f"Output contains NaN: {np.isnan(alpha).any()}")
        
        # Convert to numpy if needed
        alpha = np.array(alpha)
        
        # Log after numpy conversion
        print("\nAfter numpy conversion:")
        print(f"Numpy shape: {alpha.shape}")
        print(f"Numpy dtype: {alpha.dtype}")
        print(f"Numpy min value: {np.min(alpha):.4f}")
        print(f"Numpy max value: {np.max(alpha):.4f}")
        print(f"Numpy mean value: {np.mean(alpha):.4f}")
        print(f"Numpy std dev: {np.std(alpha):.4f}")
        print(f"Numpy contains NaN: {np.isnan(alpha).any()}")
        
        # Convert to float32 for better precision
        alpha = alpha.astype(np.float32)
        
        # Log after float32 conversion
        print("\nAfter float32 conversion:")
        print(f"Float32 shape: {alpha.shape}")
        print(f"Float32 dtype: {alpha.dtype}")
        print(f"Float32 min value: {np.min(alpha):.4f}")
        print(f"Float32 max value: {np.max(alpha):.4f}")
        print(f"Float32 mean value: {np.mean(alpha):.4f}")
        print(f"Float32 std dev: {np.std(alpha):.4f}")
        print(f"Float32 contains NaN: {np.isnan(alpha).any()}")
        
        # Remove batch dimension
        alpha = alpha.squeeze()
        
        # Clip values to [0,1]
        alpha = np.clip(alpha, 0, 1)
        
        # Log after clipping
        print("\nAfter clipping:")
        print(f"Clipped min value: {np.min(alpha):.4f}")
        print(f"Clipped max value: {np.max(alpha):.4f}")
        print(f"Clipped contains NaN: {np.isnan(alpha).any()}")
        
        # If original size is provided, resize back to original dimensions
        if original_size is not None:
            alpha = Image.fromarray((alpha * 255).astype(np.uint8))
            alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
            alpha = np.array(alpha) / 255.0
            
            # Log after resizing
            print("\nAfter resizing:")
            print(f"Resized min value: {np.min(alpha):.4f}")
            print(f"Resized max value: {np.max(alpha):.4f}")
            print(f"Resized contains NaN: {np.isnan(alpha).any()}")
        
        return alpha
    except Exception as e:
        print(f"Error in postprocessing: {e}")
        # Return a default alpha matte if processing fails
        if original_size:
            print(f"Returning default alpha matte with shape {original_size}")
            return np.full(original_size, 0.5, dtype=np.float32)  # Default to 50% alpha
        print("Returning default alpha matte with shape (1280,1280)")
        return np.full((1280, 1280), 0.5, dtype=np.float32)

def check_model_input_info(model_path):
    """
    Check and print information about model inputs and outputs
    Args:
        model_path: Path to the ONNX model
    """
    # Create a minimal session just to query input/output info
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Get input details
    inputs = session.get_inputs()
    print("\nModel Input Details:")
    for i, input_info in enumerate(inputs):
        print(f"Input {i}:")
        print(f"  Name: {input_info.name}")
        print(f"  Shape: {input_info.shape}")
        print(f"  Type: {input_info.type}")
    
    # Get output details
    outputs = session.get_outputs()
    print("\nModel Output Details:")
    for i, output_info in enumerate(outputs):
        print(f"Output {i}:")
        print(f"  Name: {output_info.name}")
        print(f"  Shape: {output_info.shape}")
        print(f"  Type: {output_info.type}")

def test_cuda_performance(session, batch_sizes=[1], input_size=(1280, 1280)):
    """
    Test CUDA performance with different batch sizes
    Args:
        session: ONNX runtime session
        batch_sizes: List of batch sizes to test
        input_size: Input image size (height, width)
    """
    # Get input and output names
    input_info = session.get_inputs()[0]
    output_info = session.get_outputs()[0]
    input_name = input_info.name
    output_name = output_info.name
    
    # Print model input/output details
    print("\nModel Input Details:")
    print(f"  Name: {input_name}")
    print(f"  Shape: {input_info.shape}")
    print(f"  Type: {input_info.type}")
    print("\nModel Output Details:")
    print(f"  Name: {output_name}")
    print(f"  Shape: {output_info.shape}")
    print(f"  Type: {output_info.type}")
    
    print("\nTesting CUDA performance with different batch sizes:")
    for batch_size in batch_sizes:
        try:
            # Create dummy input with the correct shape
            dummy_input = np.random.randn(batch_size, 3, *input_size).astype(np.float32)
            
            # Check if the input shape is compatible
            if len(input_info.shape) == 4 and input_info.shape[0] == 1:
                print(f"\nSkipping batch size {batch_size} because model expects batch size 1")
                continue
            
            # Warmup
            for _ in range(5):
                session.run([output_name], {input_name: dummy_input})
            
            # Measure performance
            start_time = time.time()
            for _ in range(5):  
                session.run([output_name], {input_name: dummy_input})
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 5  
            print(f"Batch size {batch_size}: Average inference time = {avg_time:.4f} seconds")
            
        except Exception as e:
            print(f"\nError with batch size {batch_size}:")
            print(f"Error: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run inference with optimized ONNX models.')
    parser.add_argument('--image', '-i', type=str, default="input full.png", help='Path to input image')
    parser.add_argument('--output', '-o', type=str, default="output_alpha.png", help='Path to output alpha matte')
    parser.add_argument('--optimize-all', action='store_true', default=True, help='Optimize all aspect ratio models')
    parser.add_argument('--specific-model', type=str, help='Use a specific model instead of auto-selecting')
    
    args = parser.parse_args()
    
    # Check if the input image exists
    if not os.path.exists(args.image):
        print(f"Error: Input image {args.image} not found.")
        exit(1)
    
    # Handle optimization of all models
    if args.optimize_all:
        models_info = [
            (720, 1280),   # 16:9 landscape
            (960, 1280),   # 4:3 landscape
            (1280, 720),   # 9:16 portrait
            (1280, 960),   # 3:4 portrait
            (800, 800)     # 1:1 square
        ]
        
        for height, width in models_info:
            # Always use width x height in naming (standard convention)
            input_model = f"Plus_Ultra_{width}x{height}.onnx"
            optimized_model = f"Plus_Ultra_{width}x{height}_optimized.onnx"
            
            if not os.path.exists(input_model):
                print(f"Warning: Model {input_model} not found. Skipping optimization.")
                continue
                
            print(f"Optimizing {width}x{height} model...")
            optimize_onnx_model(input_model, optimized_model)
        exit(0)
    
    # Select the appropriate model based on image aspect ratio or user choice
    if args.specific_model:
        optimized_model = args.specific_model
        # Extract target size from model name
        import re
        # Try to extract dimensions from the model name (width x height format)
        match = re.search(r'(\d+)x(\d+)', optimized_model)
        if match:
            width, height = int(match.group(1)), int(match.group(2))
            # Remember that target_size is in (height, width) format for processing
            target_size = (height, width)
        else:
            # Fallback to known dimensions if the pattern isn't found
            if "1280x720" in optimized_model:
                target_size = (720, 1280)  # height, width format for processing
            elif "1280x960" in optimized_model:
                target_size = (960, 1280)
            elif "720x1280" in optimized_model:
                target_size = (1280, 720)
            elif "960x1280" in optimized_model:
                target_size = (1280, 960)
            elif "800x800" in optimized_model:
                target_size = (800, 800)
            else:
                target_size = (1280, 1280)
    else:
        optimized_model, target_size = select_model_for_image(args.image)
        
        # If optimized model doesn't exist, try to optimize it
        if not os.path.exists(optimized_model):
            base_model = optimized_model.replace("_optimized.onnx", ".onnx")
            if os.path.exists(base_model):
                print(f"Optimizing {base_model}...")
                optimize_onnx_model(base_model, optimized_model)
            else:
                print(f"Error: Neither {optimized_model} nor {base_model} found.")
                print("Try running with --optimize-all flag first.")
                exit(1)
    
    print(f"Using model: {optimized_model} with target size: {target_size}")
    
    # Create optimized session
    print("Creating ONNX runtime session...")
    sess = create_onnx_session(optimized_model)
    
    # Get input name from session metadata
    input_name = sess.get_inputs()[0].name
    
    # Process the image
    img, original_size = preprocess_image(args.image, target_size)
        
    # Run inference
    for i in range(5):  # Runs the loop 5 times
        print(f"\nRunning inference {i+1}/5...")
        start_time = time.time()
        outputs = sess.run(None, {input_name: img})
        end_time = time.time()
        print(f"Inference {i+1} completed in {end_time - start_time:.4f} seconds")
        
        # Get alpha matte
        alpha = postprocess_output(outputs, original_size)
        
        # Save alpha matte
        # Ensure alpha values are properly scaled to 0-255 range
        alpha_np = (alpha * 255).astype(np.uint8)
        
        # Print more detailed information about the output
        print(f"\nFinal output pixel statistics:")
        print(f"Min pixel value: {np.min(alpha_np)}")
        print(f"Max pixel value: {np.max(alpha_np)}")
        print(f"Mean pixel value: {np.mean(alpha_np)}")
        print(f"Unique values: {np.unique(alpha_np).size} different values")
        print(f"Percentage of black pixels: {np.sum(alpha_np == 0) / alpha_np.size * 100:.2f}%")
        print(f"Percentage of white pixels: {np.sum(alpha_np == 255) / alpha_np.size * 100:.2f}%")
        
        # Save both the output and a version with enhanced contrast for debugging
        alpha_img = Image.fromarray(alpha_np)
        alpha_img.save(args.output)
        
        # Also save a contrast-enhanced version to help visualize subtle details
        if np.max(alpha_np) > 0:  # Avoid division by zero
            # Enhance contrast for visualization
            enhanced = np.clip(((alpha_np.astype(float) - np.min(alpha_np)) / (np.max(alpha_np) - np.min(alpha_np)) * 255), 0, 255).astype(np.uint8)
            enhanced_img = Image.fromarray(enhanced)
            enhanced_path = args.output.replace('.png', '_enhanced.png')
            enhanced_img.save(enhanced_path)
            print(f"Saved enhanced visualization to {enhanced_path}")
        
        print(f"Alpha matte saved to {args.output}")
        
        print(f"Inference time: {end_time - start_time:.4f} seconds")