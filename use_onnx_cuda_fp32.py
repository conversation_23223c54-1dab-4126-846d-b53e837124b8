import onnxruntime as ort
import numpy as np
from PIL import Image
import torch
import time
import os
import glob

def preprocess_image(image_path, target_size=(800, 800)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def postprocess_output(output, original_size=None, debug=True):
    """
    Postprocess ONNX model output to get alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
        debug: Print debug information
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output
    alpha = output[0]
    
    if debug:
        print(f"Raw output shape: {alpha.shape}")
        print(f"Raw output dtype: {alpha.dtype}")
        print(f"Raw output range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
    
    # Remove batch dimension
    alpha = alpha.squeeze()
    
    # Check for NaN/Inf values
    if np.isnan(alpha).any() or np.isinf(alpha).any():
        print("⚠️ Warning: Found NaN or Inf values in output!")
        alpha = np.nan_to_num(alpha, nan=0.0, posinf=1.0, neginf=0.0)
    
    # Apply sigmoid if values are not in [0,1] range (raw logits)
    if np.min(alpha) < -1 or np.max(alpha) > 2:
        if debug:
            print("Applying sigmoid activation")
        alpha = 1.0 / (1.0 + np.exp(-np.clip(alpha, -500, 500)))
    
    # Apply normalization like the original model
    alpha_min = np.min(alpha)
    alpha_max = np.max(alpha)
    
    if alpha_max > alpha_min:
        alpha = (alpha - alpha_min) / (alpha_max - alpha_min + 1e-8)
    
    # Final clipping to ensure [0,1] range
    alpha = np.clip(alpha, 0.0, 1.0)
    
    if debug:
        print(f"Final alpha range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"Final alpha mean: {np.mean(alpha):.6f}")
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha_img = alpha_img.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha_img) / 255.0
    
    return alpha

def find_best_model():
    """Find the best available ONNX model"""
    
    # Priority order: smaller models first for RTX 3050 Ti
    model_priorities = [
        "Plus_Ultra_800x800.onnx",
        "Plus_Ultra_1280x720.onnx", 
        "Plus_Ultra_720x1280.onnx",
        "Plus_Ultra_1280x960.onnx",
        "Plus_Ultra_960x1280.onnx"
    ]
    
    print("Looking for available ONNX models...")
    
    for model_name in model_priorities:
        if os.path.exists(model_name):
            size_mb = os.path.getsize(model_name) / (1024 * 1024)
            print(f"✓ Found: {model_name} ({size_mb:.1f} MB)")
            return model_name
    
    # If none of the priority models found, look for any ONNX model
    all_onnx = glob.glob("*.onnx")
    fp32_models = [m for m in all_onnx if not m.endswith('_fp16.onnx')]
    
    if fp32_models:
        model_name = fp32_models[0]
        size_mb = os.path.getsize(model_name) / (1024 * 1024)
        print(f"✓ Using: {model_name} ({size_mb:.1f} MB)")
        return model_name
    
    print("❌ No suitable ONNX models found!")
    return None

def get_target_size_from_model_name(model_name):
    """Extract target size from model filename"""
    if "800x800" in model_name:
        return (800, 800)
    elif "1280x720" in model_name:
        return (1280, 720)
    elif "1280x960" in model_name:
        return (1280, 960)
    elif "720x1280" in model_name:
        return (720, 1280)
    elif "960x1280" in model_name:
        return (960, 1280)
    else:
        return (800, 800)  # Default

# Example usage
if __name__ == "__main__":
    print("InSPyReNet ONNX Inference (FP32)")
    print("=" * 40)
    
    # Check if CUDA is available
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Find best model
    model_path = find_best_model()
    if not model_path:
        print("Please ensure you have ONNX models in the current directory")
        exit(1)
    
    # Get target size
    target_size = get_target_size_from_model_name(model_path)
    print(f"Using target size: {target_size}")
    
    # Set up ONNX Runtime providers
    providers = []
    if cuda_available:
        # Add CUDA provider with optimized settings for RTX 3050 Ti
        providers.append(('CUDAExecutionProvider', {
            'device_id': 0,
            'arena_extend_strategy': 'kNextPowerOfTwo',
            'gpu_mem_limit': 3 * 1024 * 1024 * 1024,  # 3GB limit for RTX 3050 Ti
            'cudnn_conv_algo_search': 'EXHAUSTIVE',
            'do_copy_in_default_stream': True,
        }))
    
    # Add CPU provider as fallback
    providers.append('CPUExecutionProvider')
    
    print(f"Using providers: {[p[0] if isinstance(p, tuple) else p for p in providers]}")
    
    # Load ONNX model
    try:
        sess = ort.InferenceSession(model_path, providers=providers)
        print(f"✓ Successfully loaded model: {model_path}")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        exit(1)
    
    # Verify which provider is being used
    active_providers = sess.get_providers()
    print(f"Active providers: {active_providers}")
    
    # Get model info
    input_info = sess.get_inputs()[0]
    output_info = sess.get_outputs()[0]
    print(f"Input: {input_info.name}, shape: {input_info.shape}, type: {input_info.type}")
    print(f"Output: {output_info.name}, shape: {output_info.shape}, type: {output_info.type}")
    
    # Process an image
    image_path = "input.png"
    
    if not os.path.exists(image_path):
        print(f"⚠️ Test image not found: {image_path}")
        print("Creating a dummy test image...")
        dummy_img = np.random.randint(0, 255, (800, 800, 3), dtype=np.uint8)
        Image.fromarray(dummy_img).save(image_path)
        print(f"✓ Created dummy test image: {image_path}")
    
    try:
        img = preprocess_image(image_path, target_size)
        print(f"✓ Preprocessed image shape: {img.shape}")
        print(f"Input range: [{np.min(img):.3f}, {np.max(img):.3f}]")
    except Exception as e:
        print(f"❌ Failed to preprocess image: {e}")
        exit(1)
    
    # Run inference
    print(f"\nRunning inference...")
    
    try:
        # Warm-up run for GPU
        if cuda_available and 'CUDAExecutionProvider' in active_providers:
            print("Performing warm-up run...")
            _ = sess.run(None, {input_info.name: img})
        
        # Actual inference with timing
        start_time = time.time()
        outputs = sess.run(None, {input_info.name: img})
        end_time = time.time()
        
        print(f"✓ Inference completed in {end_time - start_time:.6f} seconds")
        
        # Process output
        alpha = postprocess_output(outputs, debug=True)
        
        # Save alpha matte
        alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha_img.save("output_alpha_fp32.png")
        print(f"\n✓ Output saved as 'output_alpha_fp32.png'")
        
        # Final validation
        print(f"\nFinal validation:")
        print(f"Alpha shape: {alpha.shape}")
        print(f"Alpha range: [{np.min(alpha):.6f}, {np.max(alpha):.6f}]")
        print(f"Alpha mean: {np.mean(alpha):.6f}")
        print(f"Contains NaN: {np.isnan(alpha).any()}")
        print(f"Contains Inf: {np.isinf(alpha).any()}")
        
        if not np.isnan(alpha).any() and not np.isinf(alpha).any():
            print("🎉 Success! FP32 model working correctly.")
        
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
