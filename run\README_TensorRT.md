# InSPyReNet TensorRT Inference

This document explains how to use the TensorRT-accelerated inference script for InSPyReNet.

## Overview

The `InferenceTensorRT.py` script provides high-performance inference using TensorRT optimization. It supports:

- Native TensorRT engines (`.plan` files)
- torch2trt models (`.pth` files)
- All the same input/output formats as the original inference script
- Automatic fallback between different TensorRT implementations

## Prerequisites

### Required
- PyTorch with CUDA support
- TensorRT (for `.plan` files)
- OpenCV
- PIL/Pillow
- tqdm
- numpy

### Optional
- torch2trt (for `.pth` files and alternative loading)

## Usage

### Basic Image Inference
```bash
# Using TensorRT engine file
python run/InferenceTensorRT.py --source input.png --engine model.plan

# Using torch2trt model
python run/InferenceTensorRT.py --source input.png --engine model_trt.pth
```

### Video Processing
```bash
# Process video file
python run/InferenceTensorRT.py --source video.mp4 --dest output_video.mp4 --engine model.plan

# Process from webcam
python run/InferenceTensorRT.py --source 0 --engine model.plan
```

### Different Output Types
```bash
# Generate saliency map (default)
python run/InferenceTensorRT.py --source input.png --type map --engine model.plan

# Generate RGBA output with alpha channel
python run/InferenceTensorRT.py --source input.png --type rgba --engine model.plan

# Green screen effect
python run/InferenceTensorRT.py --source input.png --type green --engine model.plan

# Blur background
python run/InferenceTensorRT.py --source input.png --type blur --engine model.plan

# Overlay effect
python run/InferenceTensorRT.py --source input.png --type overlay --engine model.plan

# Custom background
python run/InferenceTensorRT.py --source input.png --type background.jpg --engine model.plan
```

### Batch Processing
```bash
# Process all images in a directory
python run/InferenceTensorRT.py --source input_folder/ --dest output_folder/ --engine model.plan
```

## Command Line Arguments

- `--config, -c`: Path to model configuration file (default: configs/extra_dataset/Plus_Ultra.yaml)
- `--source, -s`: Input source (image, video, directory, or webcam index)
- `--dest, -d`: Output destination directory or file
- `--type, -t`: Output type (map, rgba, green, blur, overlay, or background image path)
- `--engine, -e`: Path to TensorRT engine file (.plan) or torch2trt model (.pth)
- `--verbose, -v`: Enable verbose output
- `--use_torch2trt`: Force use of torch2trt TRTModule (if available)

## TensorRT Engine Files

### Using Existing Engine
If you have a `model.plan` file in the root directory, you can use it directly:
```bash
python run/InferenceTensorRT.py --source input.png --engine model.plan
```

### Creating TensorRT Engines
You can create TensorRT engines using the conversion scripts in the repository:
- `convert_to_tensorrt Directly.py`
- `convert_to_tensorrt with Torchscript.py`
- `convert_to_tensorrt with onnx.py`
- `convert_to_tensorrt with freezing.py`

### torch2trt Models
If you have torch2trt installed and a `.pth` model file:
```bash
python run/InferenceTensorRT.py --source input.png --engine model_trt.pth --use_torch2trt
```

## Performance Benefits

TensorRT provides significant performance improvements:
- Faster inference times (typically 2-5x speedup)
- Lower memory usage
- Optimized for specific GPU architectures
- Support for mixed precision (FP16/FP32)

## Troubleshooting

### Common Issues

1. **"Failed to load TensorRT engine"**
   - Ensure the engine file exists and is valid
   - Check that TensorRT is properly installed
   - Verify GPU compatibility

2. **"torch2trt not available"**
   - Install torch2trt if you want to use `.pth` files
   - Use native TensorRT with `.plan` files instead

3. **Shape mismatch errors**
   - Ensure the engine was created with the correct input dimensions
   - Check that the input image preprocessing matches the training configuration

### Checking Available Files
The script will automatically list available engine files if loading fails:
```
Available files in current directory:
  model.plan
  model_trt.pth
  engine.trt.torchscript
```

## Comparison with Original Inference

| Feature | Original Inference.py | InferenceTensorRT.py |
|---------|----------------------|---------------------|
| Speed | Baseline | 2-5x faster |
| Memory | Baseline | Lower usage |
| Precision | FP32 | FP16/FP32 optimized |
| Setup | Simple | Requires TensorRT engine |
| Compatibility | All PyTorch models | TensorRT engines only |

## Example Workflow

1. Train your model using the standard training scripts
2. Convert the trained model to TensorRT using one of the conversion scripts
3. Use `InferenceTensorRT.py` for high-performance inference

```bash
# Step 1: Convert model to TensorRT (example)
python convert_to_tensorrt\ with\ Torchscript.py

# Step 2: Run TensorRT inference
python run/InferenceTensorRT.py --source input.png --engine model.plan --type rgba
```
