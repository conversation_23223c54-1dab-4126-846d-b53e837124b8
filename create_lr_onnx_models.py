#!/usr/bin/env python3
"""
Create LR-Optimized ONNX Models

This script creates ONNX models specifically optimized for the LR (384x384) processing path,
which should be used for smaller images instead of downscaled HR models.

Usage:
    python create_lr_onnx_models.py
    python create_lr_onnx_models.py --config configs/extra_dataset/Plus_Ultra.yaml
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import numpy as np

# Add repository path
filepath = os.path.split(os.path.abspath(__file__))[0]
repopath = filepath
sys.path.append(repopath)

from lib import *
from utils.misc import *

class LROptimizedWrapper(nn.Module):
    """
    Wrapper that forces the model to use LR-optimized processing
    This simulates what happens when the original model processes small images
    """
    def __init__(self, model, lr_size=(384, 384)):
        super().__init__()
        self.model = model
        self.lr_size = lr_size
        self.model.eval()
        
        # Force the model to use inference mode
        self.model.forward = self.model.forward_inference
        
    def forward(self, x):
        """
        Forward pass optimized for LR processing
        This mimics the original model's behavior for small images
        """
        # Create a sample dict like the original dataloader
        sample = {
            'image': x,
            'image_resized': torch.nn.functional.interpolate(
                x, size=self.lr_size, mode='bilinear', align_corners=False
            )
        }
        
        # The original model will choose the appropriate processing path
        # For small images, it uses single-scale processing
        output = self.model(sample)
        
        # Return only the prediction
        return output['pred']

class LRDirectWrapper(nn.Module):
    """
    Wrapper that directly processes at LR resolution
    This is more efficient as it processes everything at 384x384
    """
    def __init__(self, model, lr_size=(384, 384)):
        super().__init__()
        self.model = model
        self.lr_size = lr_size
        self.model.eval()
        
    def forward(self, x):
        """
        Direct LR processing - resize input to LR size and process
        """
        # Resize input to LR size
        x_lr = torch.nn.functional.interpolate(
            x, size=self.lr_size, mode='bilinear', align_corners=False
        )
        
        # Create sample dict
        sample = {
            'image': x_lr,
            'image_resized': x_lr  # Same as image for LR processing
        }
        
        # Process
        output = self.model.forward_inference(sample)
        
        # Return prediction
        return output['pred']

def create_lr_onnx_model(config_path, output_path, lr_size=(384, 384), method='optimized'):
    """
    Create LR-optimized ONNX model
    
    Args:
        config_path: Path to model config
        output_path: Output ONNX file path
        lr_size: LR processing size (H, W)
        method: 'optimized' or 'direct'
    """
    
    print(f"Creating LR ONNX model...")
    print(f"Config: {config_path}")
    print(f"Output: {output_path}")
    print(f"LR size: {lr_size}")
    print(f"Method: {method}")
    
    # Load configuration
    opt = load_config(config_path)
    
    # Load model
    print("Loading PyTorch model...")
    model = eval(opt.Model.name)(**opt.Model)
    
    checkpoint_path = os.path.join(opt.Test.Checkpoint.checkpoint_dir, 'latest.pth')
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return False
    
    model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
    model = model.cuda()
    model.eval()
    
    print(f"✓ Model loaded from {checkpoint_path}")
    
    # Create wrapper
    if method == 'optimized':
        wrapper = LROptimizedWrapper(model, lr_size)
        input_size = (1, 3, lr_size[0], lr_size[1])  # Process at LR size
    elif method == 'direct':
        wrapper = LRDirectWrapper(model, lr_size)
        input_size = (1, 3, lr_size[0], lr_size[1])  # Input at LR size
    else:
        print(f"❌ Unknown method: {method}")
        return False
    
    wrapper = wrapper.cuda()
    wrapper.eval()
    
    print(f"✓ Created {method} wrapper")
    print(f"Input size: {input_size}")
    
    # Create dummy input
    dummy_input = torch.randn(input_size).cuda()
    
    # Test the wrapper
    print("Testing wrapper...")
    with torch.no_grad():
        try:
            test_output = wrapper(dummy_input)
            print(f"✓ Test successful, output shape: {test_output.shape}")
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    # Export to ONNX
    print("Exporting to ONNX...")
    try:
        torch.onnx.export(
            wrapper,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        print(f"✓ ONNX export successful: {output_path}")
        
        # Check file size
        size_mb = os.path.getsize(output_path) / (1024 * 1024)
        print(f"Model size: {size_mb:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ ONNX export failed: {e}")
        return False

def test_lr_model(onnx_path, test_size=(384, 384)):
    """Test the created LR ONNX model"""
    
    print(f"\nTesting LR ONNX model: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # Create session
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        session = ort.InferenceSession(onnx_path, providers=providers)
        
        print(f"✓ Model loaded with provider: {session.get_providers()[0]}")
        
        # Get model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        print(f"Input: {input_info.name}, shape: {input_info.shape}")
        print(f"Output: {output_info.name}, shape: {output_info.shape}")
        
        # Create test input
        test_input = np.random.randn(1, 3, test_size[0], test_size[1]).astype(np.float32)
        
        # Run inference
        import time
        start_time = time.time()
        outputs = session.run(None, {input_info.name: test_input})
        inference_time = time.time() - start_time
        
        output = outputs[0]
        print(f"✓ Inference successful in {inference_time:.4f}s")
        print(f"Output shape: {output.shape}")
        print(f"Output range: [{np.min(output):.6f}, {np.max(output):.6f}]")
        
        # Check for issues
        if np.isnan(output).any():
            print("⚠️ Warning: Output contains NaN values")
        if np.isinf(output).any():
            print("⚠️ Warning: Output contains Inf values")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Create LR-optimized ONNX models')
    parser.add_argument('--config', '-c', type=str, 
                       default='configs/extra_dataset/Plus_Ultra.yaml',
                       help='Model configuration file')
    parser.add_argument('--output-dir', '-o', type=str, default='.',
                       help='Output directory for ONNX models')
    parser.add_argument('--lr-size', type=int, nargs=2, default=[384, 384],
                       help='LR processing size (height width)')
    parser.add_argument('--method', '-m', type=str, default='direct',
                       choices=['optimized', 'direct'],
                       help='LR processing method')
    parser.add_argument('--test', action='store_true', default=True,
                       help='Test the created models')
    
    args = parser.parse_args()
    
    print("LR-Optimized ONNX Model Creator")
    print("=" * 50)
    
    # Check config file
    if not os.path.exists(args.config):
        print(f"❌ Config file not found: {args.config}")
        return 1
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    lr_size = tuple(args.lr_size)
    
    # Create LR model
    output_name = f"Plus_Ultra_LR_{lr_size[0]}x{lr_size[1]}_{args.method}.onnx"
    output_path = os.path.join(args.output_dir, output_name)
    
    print(f"Creating LR model: {output_name}")
    
    success = create_lr_onnx_model(
        args.config, 
        output_path, 
        lr_size, 
        args.method
    )
    
    if not success:
        print("❌ Failed to create LR model")
        return 1
    
    # Test the model
    if args.test:
        test_success = test_lr_model(output_path, lr_size)
        if not test_success:
            print("⚠️ Model created but testing failed")
    
    print(f"\n🎉 Success!")
    print(f"LR-optimized model created: {output_path}")
    print(f"\nUsage recommendations:")
    print(f"- Use this model for images ≤ 1280px max dimension")
    print(f"- Expect ~5-10x speed improvement over original PyTorch")
    print(f"- Quality should be optimized for small image processing")
    
    # Create additional sizes
    additional_sizes = [(320, 320), (512, 512), (640, 640)]
    
    print(f"\nWould you like to create additional LR sizes?")
    print(f"Recommended sizes: {additional_sizes}")
    print(f"Run with different --lr-size values to create them")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
