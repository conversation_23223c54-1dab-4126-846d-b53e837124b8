import numpy as np
from PIL import Image
import os
import time
import onnx
import onnxruntime as ort
import onnxoptimizer as optimizer
import torch
import pycuda.driver as cuda
import pycuda.autoinit

def optimize_onnx_model(input_model_path, output_model_path, input_shape=(1, 3, 1280, 1280)):
    """
    Optimize ONNX model while preserving dynamic axes
    Args:
        input_model_path: Path to original ONNX model
        output_model_path: Path to save optimized model
        input_shape: Fixed input shape (batch, channels, height, width)
    """
    # Load the model
    model = onnx.load(input_model_path)
    
    # Preserve dynamic axes by not setting fixed dimensions
    # Instead, we'll use the original dynamic shape from the model
    # model.graph.input[0].type.tensor_type.shape.dim[0].dim_value = input_shape[0]  # batch size
    # model.graph.input[0].type.tensor_type.shape.dim[1].dim_value = input_shape[1]  # channels
    # model.graph.input[0].type.tensor_type.shape.dim[2].dim_value = input_shape[2]  # height
    # model.graph.input[0].type.tensor_type.shape.dim[3].dim_value = input_shape[3]  # width
    
    # Apply basic graph optimizations that preserve dynamic axes
    optimized_model = optimizer.optimize(model, 
                                         [
                                             'eliminate_identity',
                                             'eliminate_nop_transpose',
                                             'fuse_consecutive_transposes',
                                             'fuse_bn_into_conv',
                                             'fuse_pad_into_conv',
                                             'fuse_add_bias_into_conv'
                                         ],
                                         fixed_point=True)
    
    # Save the optimized model
    onnx.save(optimized_model, output_model_path)
    print(f"Optimized model saved to {output_model_path}")
    
    return True

def create_onnx_session(model_path, enable_optimization=True, batch_size=1):
    """
    Create an optimized ONNX runtime session with CUDA-specific optimizations
    Args:
        model_path: Path to the ONNX model
        enable_optimization: Whether to enable graph optimization
        batch_size: Target batch size for optimization
    Returns:
        ONNX Runtime InferenceSession
    """
    # Set up session options
    sess_options = ort.SessionOptions()
    
    if enable_optimization:
        # Enable all graph optimizations
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Enable parallel execution
        sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
        
        # Use all available cores
        sess_options.intra_op_num_threads = os.cpu_count()
        
        # Enable CUDA-specific optimizations
        sess_options.enable_profiling = True
        sess_options.enable_mem_pattern = True
        sess_options.enable_cpu_mem_arena = True
        
        # Set CUDA-specific environment variables
        os.environ['ORT_CUDA_CUDNN_conv_workspace_limit_in_MB'] = '1024'
        os.environ['ORT_CUDA_CUDNN_conv_use_max_workspace'] = '1'
        
    # Check for GPU and use it if available
    providers = [
        (
            'CUDAExecutionProvider',
            {
                'device_id': 0,
                'arena_extend_strategy': 'kNextPowerOfTwo',
                'cudnn_conv_algo_search': 'EXHAUSTIVE',
                'do_copy_in_default_stream': True,
                'cudnn_conv_use_max_workspace': 1
            }
        ),
        'CPUExecutionProvider'
    ] if 'CUDAExecutionProvider' in ort.get_available_providers() \
    else ['CPUExecutionProvider']
    
    # Create ONNX Runtime session
    session = ort.InferenceSession(
        model_path,
        sess_options,
        providers=providers
    )
    
    # Log configuration
    print(f"ONNX Runtime using: {session.get_providers()}")
    print("CUDA Configuration:")
    print(f"  - Device ID: 0")
    print(f"  - Convolution Optimization: EXHAUSTIVE")
    
    return session

def create_pipeline(session, num_streams=4):
    """
    Create a pipeline with multiple CUDA streams for parallel processing
    """
    # Get input and output names
    input_info = session.get_inputs()[0]
    output_info = session.get_outputs()[0]
    input_name = input_info.name
    output_name = output_info.name
    
    # Create CUDA streams
    streams = [cuda.Stream() for _ in range(num_streams)]
    
    # Create device buffers
    input_buffers = [cuda.mem_alloc(input_info.shape[1:].numel() * input_info.type.size) 
                    for _ in range(num_streams)]
    output_buffers = [cuda.mem_alloc(output_info.shape[1:].numel() * output_info.type.size) 
                     for _ in range(num_streams)]
    
    return {
        'streams': streams,
        'input_buffers': input_buffers,
        'output_buffers': output_buffers,
        'input_name': input_name,
        'output_name': output_name
    }

def process_images_pipeline(session, images, pipeline, batch_size=1):
    """
    Process multiple images using pipeline processing
    """
    # Get pipeline components
    streams = pipeline['streams']
    input_buffers = pipeline['input_buffers']
    output_buffers = pipeline['output_buffers']
    input_name = pipeline['input_name']
    output_name = pipeline['output_name']
    
    # Create host buffers
    input_host = np.zeros(images[0].shape, dtype=np.float32)
    output_host = np.zeros(images[0].shape, dtype=np.float32)
    
    # Process images in batches
    results = []
    num_streams = len(streams)
    
    for i in range(0, len(images), batch_size):
        # Get current batch
        batch = images[i:i + batch_size]
        
        # Process each image in the batch
        for j, img in enumerate(batch):
            stream_idx = (i + j) % num_streams
            stream = streams[stream_idx]
            input_buffer = input_buffers[stream_idx]
            output_buffer = output_buffers[stream_idx]
            
            # Copy input to device
            cuda.memcpy_htod_async(input_buffer, img, stream)
            
            # Run inference
            session.run([output_name], {input_name: input_buffer}, stream=stream)
            
            # Copy output to host
            cuda.memcpy_dtoh_async(output_host, output_buffer, stream)
            
            # Append result
            results.append(output_host.copy())
            
        # Synchronize stream
        stream.synchronize()
    
    return results

def test_pipeline_performance(session, num_images=100, input_size=(1280, 1280)):
    """
    Test pipeline processing performance
    """
    print("\nTesting pipeline processing performance:")
    
    # Get input and output names
    input_info = session.get_inputs()[0]
    output_info = session.get_outputs()[0]
    input_name = input_info.name
    output_name = output_info.name
    
    # Print model input/output details
    print("\nModel Input Details:")
    print(f"  Name: {input_name}")
    print(f"  Shape: {input_info.shape}")
    print(f"  Type: {input_info.type}")
    print("\nModel Output Details:")
    print(f"  Name: {output_name}")
    print(f"  Shape: {output_info.shape}")
    print(f"  Type: {output_info.type}")
    
    # Create dummy images
    images = [np.random.randn(1, 3, *input_size).astype(np.float32) 
              for _ in range(num_images)]
    
    # Warmup
    for i in range(5):
        session.run([output_name], {input_name: images[0]})
    
    # Process images using pipeline
    results = []
    start_time = time.time()
    
    # Create a pool of worker threads for parallel processing
    from concurrent.futures import ThreadPoolExecutor
    
    def process_image(img):
        return session.run([output_name], {input_name: img})[0]
    
    # Use ThreadPoolExecutor for parallel processing
    with ThreadPoolExecutor(max_workers=4) as executor:
        # Submit all images for processing
        futures = [executor.submit(process_image, img) for img in images]
        
        # Collect results as they complete
        for future in futures:
            results.append(future.result())
    
    end_time = time.time()
    
    total_time = end_time - start_time
    throughput = num_images / total_time
    
    print(f"\nPipeline Processing Results:")
    print(f"Total time: {total_time:.4f} seconds")
    print(f"Throughput: {throughput:.2f} images/second")
    print(f"Average time per image: {total_time/num_images:.4f} seconds")
    
    return results

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    original_size = img.size  # (width, height)
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img, (original_size[1], original_size[0])  # Return (height, width)

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

def check_model_input_info(model_path):
    """
    Check and print information about model inputs and outputs
    Args:
        model_path: Path to the ONNX model
    """
    # Create a minimal session just to query input/output info
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Get input details
    inputs = session.get_inputs()
    print("\nModel Input Details:")
    for i, input_info in enumerate(inputs):
        print(f"Input {i}:")
        print(f"  Name: {input_info.name}")
        print(f"  Shape: {input_info.shape}")
        print(f"  Type: {input_info.type}")
    
    # Get output details
    outputs = session.get_outputs()
    print("\nModel Output Details:")
    for i, output_info in enumerate(outputs):
        print(f"Output {i}:")
        print(f"  Name: {output_info.name}")
        print(f"  Shape: {output_info.shape}")
        print(f"  Type: {output_info.type}")

if __name__ == "__main__":
    input_model = "model.onnx"
    optimized_model = "model_optimized.onnx"
    
    # Optimize the model without fixing batch size
    #optimize_onnx_model(input_model, optimized_model)
    
    # Create optimized session
    sess = create_onnx_session(optimized_model)
    
    # Test pipeline processing
    test_pipeline_performance(sess)
    
    # Verify model input info
    check_model_input_info(optimized_model)
    
    # Get input name from session metadata
    input_name = sess.get_inputs()[0].name
    
    # Process an image
    image_path = "input.png"
    img, original_size = preprocess_image(image_path)
    
    # Run inference
    for i in range(5):  # Runs the loop 5 times
        print(f"\nRunning inference {i+1}/5...")
        start_time = time.time()
        outputs = sess.run(None, {input_name: img})
        end_time = time.time()
        print(f"Inference {i+1} completed in {end_time - start_time:.4f} seconds")
    
    # Get alpha matte
    alpha = postprocess_output(outputs, original_size)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")
    
    print(f"Inference time: {end_time - start_time:.4f} seconds")
    print(f"Alpha matte saved to output_alpha.png")