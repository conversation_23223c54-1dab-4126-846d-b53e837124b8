#!/usr/bin/env python3
"""
Direct FP16 Fix

This script directly fixes the FP16 conversion without subprocess calls
to avoid encoding issues.

Usage:
    conda activate onnxgpu
    python direct_fp16_fix.py
"""

import os
import sys
import warnings
import onnx
import numpy as np
from onnx import numpy_helper
from onnxconverter_common import float16

# Suppress the truncation warnings we saw
warnings.filterwarnings("ignore", message="the float32 number .* will be truncated to .*")

def create_working_fp16_model(input_model_path, output_model_path):
    """
    Create a working FP16 model by keeping LayerNormalization in FP32
    """
    print(f"Converting {input_model_path} to FP16...")
    print(f"Output: {output_model_path}")
    
    try:
        # Load the original FP32 model
        print("Loading FP32 model...")
        model = onnx.load(input_model_path)
        
        # Check model
        onnx.checker.check_model(model)
        print("✓ FP32 model is valid")
        
        # Convert with conservative settings
        print("Converting to FP16 with LayerNormalization in FP32...")
        
        # Keep these operations in FP32 to avoid type mismatches
        fp32_ops = [
            'LayerNormalization',    # Main culprit
            'BatchNormalization',
            'InstanceNormalization', 
            'GroupNormalization',
            'Softmax',
            'Sigmoid',               # Final activation
            'Add',                   # Used with LayerNorm
            'Sub',                   # Used with LayerNorm  
            'Mul',                   # Used with LayerNorm
            'Div',                   # Division operations
            'Sqrt',                  # Used in normalization
            'ReduceMean',            # Used in normalization
            'ReduceSum',             # Used in normalization
            'Cast',                  # Type conversions
            'Shape', 'Reshape', 'Transpose',  # Shape operations
        ]
        
        print(f"Keeping {len(fp32_ops)} operation types in FP32")
        
        # Convert to FP16
        model_fp16 = float16.convert_float_to_float16(
            model,
            keep_io_types=True,           # Keep inputs/outputs as FP32
            disable_shape_infer=True,     # Avoid shape inference issues
            min_positive_val=1e-4,        # FP16-safe minimum value
            max_finite_val=1e4,           # Conservative maximum value
            op_block_list=fp32_ops        # Operations to keep in FP32
        )
        
        print("✓ FP16 conversion completed")
        
        # Save the model
        onnx.save(model_fp16, output_model_path)
        print(f"✓ Model saved: {output_model_path}")
        
        # Get file sizes
        original_size = os.path.getsize(input_model_path) / (1024 * 1024)
        fp16_size = os.path.getsize(output_model_path) / (1024 * 1024)
        reduction = (1 - fp16_size / original_size) * 100
        
        print(f"Size reduction: {original_size:.1f}MB → {fp16_size:.1f}MB ({reduction:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fp16_model(model_path, test_image="input.png"):
    """
    Test the FP16 model to make sure it works
    """
    print(f"\nTesting FP16 model: {model_path}")
    
    try:
        import onnxruntime as ort
        from PIL import Image
        
        if not os.path.exists(model_path):
            print(f"�� Model not found: {model_path}")
            return False
            
        if not os.path.exists(test_image):
            print(f"❌ Test image not found: {test_image}")
            return False
        
        # Load model
        print("Loading model...")
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        print("✓ Model loaded successfully")
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        # Determine target size
        if len(input_shape) >= 4:
            target_size = (input_shape[2], input_shape[3])
        else:
            target_size = (800, 800)
        
        print(f"Target size: {target_size}")
        
        # Preprocess image
        print("Preprocessing image...")
        img = Image.open(test_image).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        
        # Convert to numpy array as float32
        img = np.array(img, dtype=np.float32) / 255.0
        
        # Normalization with explicit float32 arrays
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img = (img - mean) / std
        
        # Transpose and add batch dimension
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        img = img.astype(np.float32)
        
        print("✓ Image preprocessed")
        
        # Run inference
        print("Running inference...")
        outputs = session.run(None, {input_info.name: img})
        output = outputs[0].squeeze()
        
        print("✓ Inference completed")
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        # Check for issues
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"\nOutput Analysis:")
        print(f"  Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"  Mean: {output_mean:.4f}, Std: {output_std:.4f}")
        print(f"  White pixels (>0.95): {white_percentage:.1f}%")
        print(f"  NaN: {nan_count}, Inf: {inf_count}")
        
        # Determine if model is working correctly
        if nan_count > 0 or inf_count > 0:
            print("❌ FAIL: Contains NaN or Inf values")
            return False
        elif white_percentage > 95:
            print("❌ FAIL: White alpha matte issue detected")
            return False
        elif output_std < 0.01:
            print("❌ FAIL: No contrast (flat output)")
            return False
        else:
            print("✅ PASS: Model output looks good")
            
            # Save test output
            output_img = (output * 255).astype(np.uint8)
            test_output_path = f"test_output_{os.path.basename(model_path).replace('.onnx', '')}.png"
            Image.fromarray(output_img).save(test_output_path)
            print(f"✓ Test output saved: {test_output_path}")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Direct FP16 Fix")
    print("=" * 40)
    
    # Input and output paths
    input_model = "InsPyReNet_800x800.onnx"
    output_model = "InsPyReNet_800x800_fp16_working.onnx"
    
    # Check if input exists
    if not os.path.exists(input_model):
        print(f"❌ Input model not found: {input_model}")
        print("Please make sure you have the FP32 ONNX model")
        return 1
    
    print(f"✅ Found input model: {input_model}")
    
    # Step 1: Create FP16 model
    print(f"\n{'='*50}")
    print("STEP 1: Creating FP16 Model")
    print('='*50)
    
    if os.path.exists(output_model):
        print(f"✅ FP16 model already exists: {output_model}")
    else:
        if not create_working_fp16_model(input_model, output_model):
            print("❌ Failed to create FP16 model")
            return 1
    
    # Step 2: Test the FP16 model
    print(f"\n{'='*50}")
    print("STEP 2: Testing FP16 Model")
    print('='*50)
    
    if not test_fp16_model(output_model):
        print("❌ FP16 model test failed")
        return 1
    
    # Step 3: Convert to TensorRT (optional)
    print(f"\n{'='*50}")
    print("STEP 3: TensorRT Conversion (Optional)")
    print('='*50)
    
    engine_file = output_model.replace('.onnx', '.engine')
    
    if os.path.exists(engine_file):
        print(f"✅ TensorRT engine already exists: {engine_file}")
    else:
        print(f"To convert to TensorRT FP16, run:")
        print(f"python convert_onnx_to_tensorrt.py --onnx {output_model} --fp16")
    
    # Success!
    print(f"\n{'🎉'*20}")
    print("SUCCESS! FP16 MODEL WORKING")
    print('🎉'*20)
    print(f"\nFiles created:")
    print(f"  ✅ {output_model}")
    if os.path.exists(engine_file):
        print(f"  ✅ {engine_file}")
    
    print(f"\nNext steps:")
    print(f"1. Convert to TensorRT FP16:")
    print(f"   python convert_onnx_to_tensorrt.py --onnx {output_model} --fp16")
    print(f"")
    print(f"2. Test TensorRT inference:")
    print(f"   python InferenceTensorRT.py --source input.png --engine {engine_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())