#!/usr/bin/env python3
"""
Simple ONNX to TensorRT Converter

This script uses basic trtexec commands that work with all TensorRT versions.
Specifically designed for RTX 3050 Ti with limited VRAM.

Usage:
    python simple_onnx_to_tensorrt.py Plus_Ultra_800x800.onnx
    python simple_onnx_to_tensorrt.py Plus_Ultra_1280x720.onnx --fp16
"""

import os
import sys
import argparse
import subprocess
import glob

def find_trtexec():
    """Find trtexec executable"""
    possible_paths = [
        "trtexec",
        "trtexec.exe",
        "/usr/src/tensorrt/bin/trtexec",
        "/opt/tensorrt/bin/trtexec",
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\TensorRT\\bin\\trtexec.exe",
        "C:\\TensorRT\\bin\\trtexec.exe",
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    return None

def get_onnx_input_shape(onnx_path):
    """Get input shape from ONNX model"""
    try:
        import onnx
        model = onnx.load(onnx_path)
        input_info = model.graph.input[0]
        input_name = input_info.name
        
        # Extract shape
        shape = []
        for dim in input_info.type.tensor_type.shape.dim:
            if dim.dim_value:
                shape.append(dim.dim_value)
            else:
                shape.append(-1)  # Dynamic dimension
        
        return input_name, shape
    except Exception as e:
        print(f"Could not parse ONNX model: {e}")
        return "input", [1, 3, 1280, 1280]  # Default shape

def convert_simple(onnx_path, engine_path, use_fp16=True):
    """Convert ONNX to TensorRT using simple trtexec command"""
    
    trtexec_path = find_trtexec()
    if not trtexec_path:
        print("❌ trtexec not found!")
        print("Please install TensorRT and ensure trtexec is in your PATH")
        return False
    
    print(f"✓ Found trtexec: {trtexec_path}")
    
    # Get input info
    input_name, input_shape = get_onnx_input_shape(onnx_path)
    print(f"Input: {input_name}, shape: {input_shape}")
    
    # Build simple command
    cmd = [
        trtexec_path,
        f"--onnx={onnx_path}",
        f"--saveEngine={engine_path}",
    ]
    
    # Add FP16 if requested
    if use_fp16:
        cmd.append("--fp16")
        print("Using FP16 precision")
    
    # Add memory pool size for TensorRT 10.x (conservative for RTX 3050 Ti)
    cmd.append("--memPoolSize=workspace:1024")  # 1GB workspace
    
    # Add verbose output
    cmd.append("--verbose")
    
    print(f"Converting {onnx_path} to {engine_path}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10 minute timeout
        
        if result.returncode == 0:
            print("✅ Conversion successful!")
            if os.path.exists(engine_path):
                size_mb = os.path.getsize(engine_path) / (1024 * 1024)
                print(f"Engine file created: {engine_path} ({size_mb:.1f} MB)")
                return True
            else:
                print("❌ Engine file was not created")
                return False
        else:
            print("❌ Conversion failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            
            # Try fallback without memory pool specification
            print("\nTrying fallback conversion without memory pool...")
            cmd_fallback = [
                trtexec_path,
                f"--onnx={onnx_path}",
                f"--saveEngine={engine_path}",
            ]
            if use_fp16:
                cmd_fallback.append("--fp16")
            
            result_fallback = subprocess.run(cmd_fallback, capture_output=True, text=True, timeout=600)
            
            if result_fallback.returncode == 0:
                print("✅ Fallback conversion successful!")
                if os.path.exists(engine_path):
                    size_mb = os.path.getsize(engine_path) / (1024 * 1024)
                    print(f"Engine file created: {engine_path} ({size_mb:.1f} MB)")
                    return True
            else:
                print("❌ Fallback conversion also failed!")
                print("STDOUT:", result_fallback.stdout)
                print("STDERR:", result_fallback.stderr)
            
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Conversion timed out (10 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False

def test_engine(engine_path):
    """Test the created engine"""
    try:
        import tensorrt as trt
        
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        if engine:
            print(f"✅ Engine test successful!")
            print(f"Engine has {engine.num_bindings} bindings")
            
            # Print binding info
            for i in range(engine.num_bindings):
                name = engine.get_binding_name(i)
                shape = engine.get_binding_shape(i)
                dtype = engine.get_binding_dtype(i)
                is_input = engine.binding_is_input(i)
                print(f"  Binding {i}: {name}, shape: {shape}, dtype: {dtype}, input: {is_input}")
            
            return True
        else:
            print("❌ Engine test failed - could not deserialize")
            return False
            
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Simple ONNX to TensorRT converter')
    parser.add_argument('onnx_file', nargs='?', help='ONNX model file to convert')
    parser.add_argument('--engine', type=str, help='Output TensorRT engine file')
    parser.add_argument('--fp16', action='store_true', default=True, help='Use FP16 precision (default: True)')
    parser.add_argument('--fp32', action='store_true', help='Use FP32 precision instead of FP16')
    parser.add_argument('--test', action='store_true', help='Test the engine after conversion')
    parser.add_argument('--list', action='store_true', help='List available ONNX models')
    
    args = parser.parse_args()
    
    print("Simple ONNX to TensorRT Converter")
    print("=" * 40)
    
    # List available ONNX models
    onnx_models = sorted(glob.glob("*.onnx"))
    
    if args.list or not args.onnx_file:
        print("Available ONNX models:")
        if onnx_models:
            for i, model in enumerate(onnx_models, 1):
                size_mb = os.path.getsize(model) / (1024 * 1024)
                print(f"  {i}. {model} ({size_mb:.1f} MB)")
        else:
            print("  No ONNX models found in current directory")
        
        if args.list:
            return 0
        
        if not args.onnx_file and onnx_models:
            print(f"\nUsing first available model: {onnx_models[0]}")
            args.onnx_file = onnx_models[0]
        elif not args.onnx_file:
            print("No ONNX model specified and none found!")
            return 1
    
    # Validate ONNX file
    if not os.path.exists(args.onnx_file):
        print(f"❌ ONNX file not found: {args.onnx_file}")
        return 1
    
    # Set output engine path
    if not args.engine:
        base_name = os.path.splitext(args.onnx_file)[0]
        args.engine = f"{base_name}.plan"
    
    # Handle FP32 override
    use_fp16 = args.fp16 and not args.fp32
    
    print(f"Input ONNX: {args.onnx_file}")
    print(f"Output Engine: {args.engine}")
    print(f"Precision: {'FP16' if use_fp16 else 'FP32'}")
    
    # Convert
    success = convert_simple(args.onnx_file, args.engine, use_fp16=use_fp16)
    
    if success and args.test:
        print("\nTesting engine...")
        test_engine(args.engine)
    
    if success:
        print(f"\n🎉 Success! You can now use:")
        print(f"python run/InferenceTensorRT.py --source input.png --engine {args.engine}")
        return 0
    else:
        print(f"\n❌ Conversion failed. Alternatives:")
        print(f"1. Use ONNX inference: python run/InferenceONNXTensorRT.py --onnx {args.onnx_file}")
        print(f"2. Use original inference: python run/Inference.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
