import gc
import os
import torch
import torch.nn as nn
import tensorrt as trt
import numpy as np
from torch2trt import torch2trt

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

def create_engine(config_path, checkpoint_dir, engine_path, batch_size=1, workspace_size=3<<30):
    """
    Create TensorRT engine from PyTorch model using TorchScript as an intermediate representation
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        engine_path: Path to save the TensorRT engine
        batch_size: Batch size for optimization
        workspace_size: Maximum workspace size in bytes
    """
    # Check if TensorRT engine already exists
    if os.path.exists(engine_path):
        print(f"TensorRT engine already exists at {engine_path}")
        try:
            # Try loading the existing engine
            TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = runtime.deserialize_cuda_engine(engine_data)
            print("Successfully loaded existing engine")
            return engine
        except Exception as e:
            print(f"Could not load existing engine: {e}")
            print("Will recreate the engine")
    
    # Define path for TorchScript model
    torchscript_path = "temp_model.pt"
    
    # Check if TorchScript model already exists
    torchscript_exists = os.path.exists(torchscript_path)
    
    if not torchscript_exists:
        # Load configuration
        opt = load_config(config_path)
        
        # Load model
        model = eval(opt.Model.name)(**opt.Model)
        model.load_state_dict(torch.load(os.path.join(checkpoint_dir, 'latest.pth'), map_location=torch.device('cpu')), strict=True)
        model = model.cuda()
        model.eval()
        
        # Create wrapper for inference to ensure consistent interface
        # Completely redesigned wrapper to avoid any hooks
        class InferenceWrapper(nn.Module):
            def __init__(self, model):
                super().__init__()
                self.model = model
                self.model.eval()

            def forward(self, x):
                # Create input dict directly
                with torch.no_grad():
                    input_dict = {'image': x}
                    # Call forward_inference directly without any hooks
                    output = self.model.forward_inference(input_dict)
                    pred = output['pred']
                    saliency = output['saliency']
                    laplacian = output['laplacian']
                    return pred, saliency[0], saliency[1], saliency[2], saliency[3], laplacian[0], laplacian[1], laplacian[2]

        # Create the wrapper
        wrapper = InferenceWrapper(model)
        wrapper.eval()
        
        # Create dummy input tensor
        input_shape = (batch_size, 3, 1280, 1280)
        dummy_input = torch.randn(input_shape).cuda()
        
        print("Converting model to TorchScript...")
        # Convert to TorchScript with extra care to avoid hooks
        try:
            # Disable gradient computation
            with torch.no_grad():
                # Create a completely new forward pass to avoid any hooks
                # Use tracing mode which is less likely to have hook issues
                traced_model = torch.jit.trace(wrapper, dummy_input)
                
                # Optimize the traced model
                traced_model = torch.jit.optimize_for_inference(torch.jit.freeze(traced_model))
            
            # Save the model
            torch.jit.save(traced_model, torchscript_path)
            print(f"TorchScript export completed successfully and saved to {torchscript_path}")
            
            # Force garbage collection to free memory
            del traced_model, wrapper, model
            torch.cuda.empty_cache()
            gc.collect()
        except Exception as e:
            print(f"Error during TorchScript tracing: {e}")
            # Fall back to script mode if tracing fails
            print("Falling back to script mode...")
            try:
                with torch.no_grad():
                    scripted_model = torch.jit.script(wrapper)
                    scripted_model = torch.jit.optimize_for_inference(torch.jit.freeze(scripted_model))
                
                torch.jit.save(scripted_model, torchscript_path)
                
                # Force garbage collection to free memory
                del scripted_model, wrapper, model
                torch.cuda.empty_cache()
                gc.collect()
                
                print(f"TorchScript script mode export completed successfully and saved to {torchscript_path}")
            except Exception as e:
                print(f"Error during TorchScript scripting: {e}")
                return None
    else:
        print(f"Using existing TorchScript model from {torchscript_path}")
    
    # Convert TorchScript model to TensorRT
    print("Converting TorchScript model to TensorRT...")
    
    # TensorRT logger
    TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
    
    # Using torch2trt for conversion with modified approach to avoid CUDA issues
    try:
        # Load TorchScript model
        ts_model = torch.jit.load(torchscript_path)
        ts_model.eval()
        
        # Move model to GPU without using .cuda() method that might cause issues
        if torch.cuda.is_available():
            ts_model = ts_model.to(device="cuda")
        
        # Create dummy input tensor - avoiding direct .cuda() call
        input_shape = (batch_size, 3, 1280, 1280)
        dummy_input = torch.randn(input_shape, device="cuda" if torch.cuda.is_available() else "cpu")
        
        # Define conversion parameters
        conversion_params = {
            'fp16_mode': True,                # Enable FP16 precision
            'max_workspace_size': workspace_size,  # Maximum workspace size
            'strict_type_constraints': False,  # Less strict for better compatibility
            'max_batch_size': batch_size      # Maximum batch size
        }
        
        # Convert to TensorRT using torch2trt
        print("Starting torch2trt conversion...")
        trt_model = torch2trt(ts_model, [dummy_input], **conversion_params)
        print("torch2trt conversion completed")
        
        # Extract and save the native TensorRT engine
        with open(engine_path, "wb") as f:
            f.write(trt_model.engine.serialize())
        
        print(f"TensorRT engine saved to {engine_path}")
        
        # Clean up
        del ts_model, trt_model
        torch.cuda.empty_cache()
        gc.collect()
        
        # Reload engine to return
        runtime = trt.Runtime(TRT_LOGGER)
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        return runtime.deserialize_cuda_engine(engine_data)
        
    except Exception as e:
        print(f"Error during TensorRT conversion: {e}")
        # Try a more direct approach with TensorRT builder API
        print("Attempting direct TensorRT conversion...")
        
        try:
            # Create the TensorRT builder and network
            builder = trt.Builder(TRT_LOGGER)
            network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            config = builder.create_builder_config()
            config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, workspace_size)
            config.set_flag(trt.BuilderFlag.FP16)
            
            # Try to load the TorchScript model
            print("Loading TorchScript model...")
            torch.ops.load_library("torch_tensorrt")
            
            # Create a new dummy tensor directly on CPU to avoid CUDA method issues
            dummy_input = torch.randn(input_shape, device="cpu")
            
            # Try a different approach with torch2trt
            print("Using torch2trt with CPU tensors...")
            ts_model = torch.jit.load(torchscript_path, map_location="cpu")
            ts_model.eval()
            
            # Try with conservative settings
            trt_model = torch2trt(
                ts_model, 
                [dummy_input], 
                fp16_mode=True,
                max_workspace_size=workspace_size,
                log_level=trt.Logger.VERBOSE,
                max_batch_size=batch_size
            )
            
            # Save the engine
            with open(engine_path, "wb") as f:
                f.write(trt_model.engine.serialize())
            
            print(f"TensorRT engine saved to {engine_path}")
            
            # Return the engine
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            return runtime.deserialize_cuda_engine(engine_data)
            
        except Exception as e:
            print(f"Error during direct TensorRT conversion: {e}")
            print("Unable to create TensorRT engine. Please check if the model architecture is compatible with TensorRT.")
            return None

def load_and_test_engine(engine_path, input_shape=(1, 3, 1280, 1280)):
    """
    Load and test the TensorRT engine
    Args:
        engine_path: Path to the TensorRT engine
        input_shape: Shape of the input tensor
    """
    try:
        # Initialize TensorRT
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        # Load engine
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        context = engine.create_execution_context()
        
        # Test with random input - avoid using .cuda() method
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        dummy_input = torch.randn(input_shape, device=device)
        
        # Allocate memory for inputs and outputs
        bindings = []
        input_idx = engine.get_binding_index("image") if engine.num_bindings > 0 and engine.get_binding_name(0) == "image" else 0
        context.set_binding_shape(input_idx, input_shape)
        bindings.append(dummy_input.data_ptr())
        
        # Allocate output memory
        output_shapes = []
        for i in range(1, engine.num_bindings):
            output_shape = context.get_binding_shape(i)
            output = torch.empty(tuple(output_shape), dtype=torch.float32, device=device)
            bindings.append(output.data_ptr())
            output_shapes.append(output)
        
        # Execute inference
        context.execute_v2(bindings)
        print("Engine loaded and tested successfully")
        return engine
            
    except Exception as e:
        print(f"Error testing TensorRT engine: {e}")
        return None
    
    # Using torch2trt for conversion
    try:
        # Load TorchScript model
        ts_model = torch.jit.load(torchscript_path)
        ts_model.eval()
        ts_model.cuda()
        
        # Create dummy input tensor for the loaded model
        input_shape = (batch_size, 3, 1280, 1280)
        dummy_input = torch.randn(input_shape).cuda()
        
        # Define conversion parameters
        conversion_params = {
            'fp16_mode': True,                # Enable FP16 precision
            'max_workspace_size': workspace_size,  # Maximum workspace size
            'strict_type_constraints': True,  # Enforce precision constraints
            'max_batch_size': batch_size      # Maximum batch size
        }
        
        # Convert to TensorRT using torch2trt
        trt_model = torch2trt(ts_model, [dummy_input], **conversion_params)
        
        # Extract and save the native TensorRT engine
        with open(engine_path, "wb") as f:
            f.write(trt_model.engine.serialize())
        
        print(f"TensorRT engine saved to {engine_path}")
        
        # Clean up
        del ts_model, trt_model
        torch.cuda.empty_cache()
        gc.collect()
        
        # Reload engine to return
        runtime = trt.Runtime(TRT_LOGGER)
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        return runtime.deserialize_cuda_engine(engine_data)
        
    except Exception as e:
        print(f"Error during TensorRT conversion: {e}")
        # Modified approach to avoid using PyTorchParser which is not available
        print("Attempting manual TensorRT conversion using direct serialization...")
        
        try:
            # Try using direct serialization method from torch2trt
            # Reload the TorchScript model 
            ts_model = torch.jit.load(torchscript_path)
            ts_model.eval()
            ts_model.cuda()
            
            # Create dummy input tensor
            input_shape = (batch_size, 3, 1280, 1280)
            dummy_input = torch.randn(input_shape).cuda()
            
            # Use a different approach with torch2trt with simplified settings
            trt_model = torch2trt(
                ts_model, 
                [dummy_input],
                fp16_mode=True,
                max_batch_size=batch_size,
                # Use a more conservative approach with fewer optimizations
                strict_type_constraints=False,
                max_workspace_size=workspace_size
            )
            
            # Save the TensorRT engine
            with open(engine_path, "wb") as f:
                f.write(trt_model.engine.serialize())
            
            print(f"TensorRT engine successfully created with simplified settings and saved to {engine_path}")
            
            # Return the engine
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            return runtime.deserialize_cuda_engine(engine_data)
            
        except Exception as e:
            print(f"Error during direct TensorRT conversion: {e}")
            print("Unable to create TensorRT engine. Please ensure TensorRT and torch2trt are correctly installed.")
            return None

def load_and_test_engine(engine_path, input_shape=(1, 3, 1280, 1280)):
    """
    Load and test the TensorRT engine
    Args:
        engine_path: Path to the TensorRT engine
        input_shape: Shape of the input tensor
    """
    try:
        # Initialize TensorRT
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        # Load engine
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        context = engine.create_execution_context()
        
        # Test with random input
        dummy_input = torch.randn(input_shape).cuda()
        
        # Allocate memory for inputs and outputs
        bindings = []
        input_idx = engine.get_binding_index("image") if engine.num_bindings > 0 and engine.get_binding_name(0) == "image" else 0
        context.set_binding_shape(input_idx, input_shape)
        bindings.append(dummy_input.data_ptr())
        
        # Allocate output memory
        output_shapes = []
        for i in range(1, engine.num_bindings):
            output_shape = context.get_binding_shape(i)
            output = torch.empty(tuple(output_shape), dtype=torch.float32, device=torch.device('cuda'))
            bindings.append(output.data_ptr())
            output_shapes.append(output)
        
        # Execute inference
        context.execute_v2(bindings)
        print("Engine loaded and tested successfully")
        return engine
            
    except Exception as e:
        print(f"Error testing TensorRT engine: {e}")
        return None
    
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert model to TensorRT using TorchScript')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to configuration file')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint')
    parser.add_argument('--engine-path', '-e', type=str, default="engine.trt", help='Path to save TensorRT engine')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workspace-size', type=int, default=3<<30, help='Workspace size in bytes')
    parser.add_argument('--test', action='store_true', help='Test the engine after conversion')
    
    args = parser.parse_args()
    
    # Create TensorRT engine
    engine = create_engine(args.config, args.checkpoint_dir, args.engine_path, args.batch_size, args.workspace_size)
    
    # Test engine if requested
    if args.test and engine is not None:
        load_and_test_engine(args.engine_path, (args.batch_size, 3, 1280, 1280))