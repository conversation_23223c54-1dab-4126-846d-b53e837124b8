#!/usr/bin/env python3
"""
Fix FP16 Type Mismatch

This script fixes type mismatch errors in FP16 ONNX models by ensuring
consistent data types throughout the graph.

Usage:
    python fix_fp16_type_mismatch.py Plus_Ultra_1280x720_fp16.onnx
    python fix_fp16_type_mismatch.py --fix-all
"""

import os
import sys
import argparse
import glob
import onnx
from onnx import helper, numpy_helper
import numpy as np

def fix_type_mismatch(model_path, output_path=None, method='keep_fp32'):
    """
    Fix type mismatch in FP16 models
    
    Args:
        model_path: Path to problematic FP16 model
        output_path: Path to save fixed model
        method: 'keep_fp32', 'force_fp16', or 'mixed'
    """
    
    if output_path is None:
        output_path = model_path.replace('.onnx', '_fixed.onnx')
    
    print(f"Fixing type mismatch: {model_path}")
    print(f"Method: {method}")
    
    try:
        # Load model
        model = onnx.load(model_path)
        
        print(f"Original model info:")
        print(f"  Nodes: {len(model.graph.node)}")
        print(f"  Inputs: {len(model.graph.input)}")
        print(f"  Outputs: {len(model.graph.output)}")
        
        if method == 'keep_fp32':
            # Convert everything back to FP32 for compatibility
            fixed_model = convert_to_fp32(model)
            print("Converted model to FP32 for compatibility")
            
        elif method == 'force_fp16':
            # Force everything to FP16 (may cause numerical issues)
            fixed_model = force_fp16_consistency(model)
            print("Forced FP16 consistency")
            
        elif method == 'mixed':
            # Use mixed precision approach
            fixed_model = create_mixed_precision_model(model)
            print("Created mixed precision model")
        
        # Save fixed model
        onnx.save(fixed_model, output_path)
        print(f"✓ Fixed model saved: {output_path}")
        
        # Test the fixed model
        print("Testing fixed model...")
        success = test_model_loading(output_path)
        
        if success:
            print("✅ Model fix successful!")
            return output_path
        else:
            print("❌ Model still has issues")
            return None
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return None

def convert_to_fp32(model):
    """Convert entire model back to FP32"""
    from onnx import numpy_helper
    
    # Create a copy
    model_copy = onnx.ModelProto()
    model_copy.CopyFrom(model)
    
    # Convert initializers to FP32
    for initializer in model_copy.graph.initializer:
        if initializer.data_type == onnx.TensorProto.FLOAT16:
            # Get the numpy array
            weights = numpy_helper.to_array(initializer)
            
            # Convert to FP32
            weights_fp32 = weights.astype(np.float32)
            
            # Create new initializer
            new_initializer = numpy_helper.from_array(weights_fp32, initializer.name)
            
            # Replace the old initializer
            initializer.CopyFrom(new_initializer)
    
    # Update value info types
    for value_info in model_copy.graph.value_info:
        if value_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            value_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    # Update input types
    for input_info in model_copy.graph.input:
        if input_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            input_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    # Update output types
    for output_info in model_copy.graph.output:
        if output_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            output_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    return model_copy

def force_fp16_consistency(model):
    """Force all operations to use FP16 consistently"""
    from onnx import numpy_helper
    
    # Create a copy
    model_copy = onnx.ModelProto()
    model_copy.CopyFrom(model)
    
    # Convert all initializers to FP16
    for initializer in model_copy.graph.initializer:
        if initializer.data_type == onnx.TensorProto.FLOAT:
            weights = numpy_helper.to_array(initializer)
            weights_fp16 = weights.astype(np.float16)
            new_initializer = numpy_helper.from_array(weights_fp16, initializer.name)
            initializer.CopyFrom(new_initializer)
    
    # Update all value info to FP16
    for value_info in model_copy.graph.value_info:
        if value_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT:
            value_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT16
    
    # Keep inputs as FP32 for compatibility
    for input_info in model_copy.graph.input:
        if input_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            input_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    # Keep outputs as FP32 for compatibility
    for output_info in model_copy.graph.output:
        if output_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            output_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    return model_copy

def create_mixed_precision_model(model):
    """Create a mixed precision model with better type consistency"""
    # This is more complex - for now, just convert to FP32
    return convert_to_fp32(model)

def test_model_loading(model_path):
    """Test if model can be loaded without errors"""
    try:
        import onnxruntime as ort
        
        # Try to create session
        providers = ['CPUExecutionProvider']  # Use CPU for testing
        session = ort.InferenceSession(model_path, providers=providers)
        
        print(f"  ✓ Model loads successfully")
        print(f"  Active provider: {session.get_providers()[0]}")
        
        # Get model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        print(f"  Input: {input_info.name}, shape: {input_info.shape}, type: {input_info.type}")
        print(f"  Output: {output_info.name}, shape: {output_info.shape}, type: {output_info.type}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model loading failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Fix FP16 type mismatch in ONNX models')
    parser.add_argument('model', nargs='?', help='ONNX model file to fix')
    parser.add_argument('--output', '-o', type=str, help='Output file path')
    parser.add_argument('--method', '-m', type=str, default='keep_fp32',
                       choices=['keep_fp32', 'force_fp16', 'mixed'],
                       help='Fix method: keep_fp32 (default), force_fp16, or mixed')
    parser.add_argument('--fix-all', action='store_true', default=True, help='Fix all FP16 models in directory')
    parser.add_argument('--test-only', action='store_true', help='Only test loading, do not fix')
    
    args = parser.parse_args()
    
    print("FP16 Type Mismatch Fixer")
    print("=" * 40)
    
    if args.fix_all:
        # Find all FP16 models
        fp16_models = glob.glob("*_fp16.onnx")
        if not fp16_models:
            print("No FP16 models found")
            return 1
        
        print(f"Found {len(fp16_models)} FP16 models:")
        for model in fp16_models:
            print(f"  {model}")
        
        print("\nFixing models...")
        fixed_count = 0
        
        for model in fp16_models:
            print(f"\n{'='*60}")
            if args.test_only:
                success = test_model_loading(model)
            else:
                fixed_path = fix_type_mismatch(model, method=args.method)
                if fixed_path:
                    fixed_count += 1
        
        if not args.test_only:
            print(f"\n✓ Fixed {fixed_count}/{len(fp16_models)} models")
        
        return 0
    
    if not args.model:
        print("Please specify a model file or use --fix-all")
        return 1
    
    if not os.path.exists(args.model):
        print(f"Model file not found: {args.model}")
        return 1
    
    if args.test_only:
        success = test_model_loading(args.model)
        return 0 if success else 1
    
    # Fix the model
    fixed_path = fix_type_mismatch(args.model, args.output, args.method)
    
    if fixed_path:
        print(f"\n🎉 Success! Use the fixed model:")
        print(f"python test_corrected_fp16_models.py")
        print(f"# Or update your inference script to use: {fixed_path}")
        return 0
    else:
        print(f"\n❌ Could not fix the model. Alternatives:")
        print(f"1. Use original FP32 models")
        print(f"2. Try different fix method: --method force_fp16")
        print(f"3. Use PyTorch inference: python run/Inference.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
