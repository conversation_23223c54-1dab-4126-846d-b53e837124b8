"""
InSPyReNet TensorRT Inference Script

This script provides TensorRT-accelerated inference for the InSPyReNet model.
It supports both native TensorRT engines (.plan files) and torch2trt models (.pth files).

Usage examples:
    # Basic inference with TensorRT engine
    python InferenceTensorRT.py --source input.png --engine model.plan

    # Video processing
    python InferenceTensorRT.py --source video.mp4 --dest output_video.mp4 --engine model.plan

    # Using torch2trt model
    python InferenceTensorRT.py --source input.png --engine model_trt.pth --use_torch2trt

    # Different output types
    python InferenceTensorRT.py --source input.png --type rgba --engine model.plan
    python InferenceTensorRT.py --source input.png --type green --engine model.plan

Requirements:
    - TensorRT (for .plan files)
    - torch2trt (optional, for .pth files)
    - PyTorch with CUDA support
"""

import os
import cv2
import sys
import tqdm
import torch
import argparse
import tensorrt as trt
import numpy as np

from PIL import Image

filepath = os.path.split(os.path.abspath(__file__))[0]
repopath = os.path.split(filepath)[0]
sys.path.append(repopath)

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

# TensorRT logger
TRT_LOGGER = trt.Logger(trt.Logger.WARNING)

def _args():
    parser = argparse.ArgumentParser(description='InSPyReNet TensorRT Inference')
    parser.add_argument('--config', '-c',     type=str,            default='configs/extra_dataset/Plus_Ultra.yaml',
                       help='Path to model configuration file')
    parser.add_argument('--source', '-s',     type=str,            default='input.png',
                       help='Input source: image file, video file, directory, or webcam index')
    parser.add_argument('--dest', '-d',       type=str,            default='output.png',
                       help='Output destination directory or file')
    parser.add_argument('--type', '-t',       type=str,            default='map',
                       help='Output type: map, rgba, green, blur, overlay, or background image path')
    parser.add_argument('--engine', '-e',     type=str,            default='Plus_Ultra_800x800_fixed.plan',
                       help='Path to TensorRT engine file (.plan) or torch2trt model (.pth)')
    parser.add_argument('--verbose', '-v',    action='store_true', default=True,
                       help='Enable verbose output')
    parser.add_argument('--use_torch2trt',    action='store_true', default=False,
                       help='Force use of torch2trt TRTModule (if available)')
    parser.add_argument('--check_compatibility', action='store_true', default=False,
                       help='Check TensorRT version compatibility and exit')
    return parser.parse_args()

def get_format(source):
    img_count = len([i for i in source if i.lower().endswith(('.jpg', '.png', '.jpeg'))])
    vid_count = len([i for i in source if i.lower().endswith(('.mp4', '.avi', '.mov' ))])
    
    if img_count * vid_count != 0:
        return ''
    elif img_count != 0:
        return 'Image'
    elif vid_count != 0:
        return 'Video'
    else:
        return ''

class TensorRTInference:
    def __init__(self, engine_path):
        """
        Initialize TensorRT inference engine
        Args:
            engine_path: Path to the TensorRT engine file (.plan)
        """
        self.engine_path = engine_path
        self.engine = None
        self.context = None
        self.inputs = []
        self.outputs = []
        self.bindings = []
        self.stream = None

        self._load_engine()
        self._allocate_buffers()

    def _load_engine(self):
        """Load TensorRT engine from file"""
        try:
            with open(self.engine_path, 'rb') as f:
                engine_data = f.read()

            runtime = trt.Runtime(TRT_LOGGER)
            self.engine = runtime.deserialize_cuda_engine(engine_data)

            if self.engine is None:
                # Check TensorRT version compatibility
                current_version = trt.__version__
                raise RuntimeError(
                    f"Failed to load TensorRT engine from {self.engine_path}\n"
                    f"Current TensorRT version: {current_version}\n"
                    f"This is likely due to version incompatibility. TensorRT engine files (.plan) "
                    f"are not compatible across different major versions.\n"
                    f"Please regenerate the engine file with TensorRT {current_version} or use a "
                    f"compatible engine file."
                )

            self.context = self.engine.create_execution_context()
            print(f"Successfully loaded TensorRT engine from {self.engine_path}")
            print(f"TensorRT version: {trt.__version__}")

            # Print engine info
            print(f"Engine has {self.engine.num_bindings} bindings")
            for i in range(self.engine.num_bindings):
                name = self.engine.get_binding_name(i)
                shape = self.engine.get_binding_shape(i)
                dtype = self.engine.get_binding_dtype(i)
                is_input = self.engine.binding_is_input(i)
                print(f"  Binding {i}: {name}, shape: {shape}, dtype: {dtype}, is_input: {is_input}")

        except Exception as e:
            current_version = trt.__version__
            if "incompatible" in str(e).lower() or "version" in str(e).lower():
                raise RuntimeError(
                    f"TensorRT version compatibility error!\n"
                    f"Current TensorRT version: {current_version}\n"
                    f"The engine file was likely created with a different TensorRT version.\n"
                    f"Solution: Regenerate the engine file using one of these scripts:\n"
                    f"  - convert_to_tensorrt Directly.py\n"
                    f"  - convert_to_tensorrt with Torchscript.py\n"
                    f"  - convert_to_tensorrt with onnx.py\n"
                    f"Original error: {e}"
                )
            else:
                raise RuntimeError(f"Failed to load TensorRT engine: {e}")

    def _allocate_buffers(self):
        """Allocate GPU memory for inputs and outputs"""
        self.inputs = []
        self.outputs = []
        self.bindings = []

        # Create CUDA stream
        self.stream = torch.cuda.Stream()

        for i in range(self.engine.num_bindings):
            binding_name = self.engine.get_binding_name(i)
            shape = self.engine.get_binding_shape(i)
            size = trt.volume(shape)
            dtype = trt.nptype(self.engine.get_binding_dtype(i))

            # Convert TensorRT dtype to PyTorch dtype
            if dtype == np.float32:
                torch_dtype = torch.float32
            elif dtype == np.float16:
                torch_dtype = torch.float16
            else:
                torch_dtype = torch.float32  # Default fallback

            # Allocate GPU memory
            device_mem = torch.empty(size, dtype=torch_dtype, device='cuda')

            self.bindings.append(device_mem.data_ptr())

            if self.engine.binding_is_input(i):
                self.inputs.append({
                    'name': binding_name,
                    'tensor': device_mem,
                    'size': size,
                    'shape': shape,
                    'dtype': torch_dtype
                })
            else:
                self.outputs.append({
                    'name': binding_name,
                    'tensor': device_mem,
                    'size': size,
                    'shape': shape,
                    'dtype': torch_dtype
                })

    def infer(self, input_tensor):
        """
        Run inference on input tensor
        Args:
            input_tensor: Input tensor (torch.Tensor)
        Returns:
            Output tensor
        """
        # Ensure input tensor is on GPU and has correct dtype
        if not input_tensor.is_cuda:
            input_tensor = input_tensor.cuda()

        # Copy input to allocated buffer
        if len(self.inputs) > 0:
            input_info = self.inputs[0]
            input_binding = input_info['tensor']

            # Reshape input tensor to match expected shape
            expected_shape = input_info['shape']
            if input_tensor.shape != expected_shape:
                input_tensor = input_tensor.view(expected_shape)

            # Convert dtype if necessary
            if input_tensor.dtype != input_info['dtype']:
                input_tensor = input_tensor.to(input_info['dtype'])

            input_binding.copy_(input_tensor.flatten())

        # Run inference
        with torch.cuda.stream(self.stream):
            success = self.context.execute_async_v2(bindings=self.bindings, stream_handle=self.stream.cuda_stream)
            if not success:
                raise RuntimeError("TensorRT inference failed")
            self.stream.synchronize()

        # Get output and reshape
        if len(self.outputs) > 0:
            output_info = self.outputs[0]
            output_tensor = output_info['tensor']
            output_shape = output_info['shape']

            # Reshape output to expected shape
            reshaped_output = output_tensor.view(output_shape)

            # Apply sigmoid activation to match the original model output
            # The original model applies sigmoid in forward_inference
            pred = torch.sigmoid(reshaped_output)
            pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-8)

            return pred

        return None

def check_tensorrt_compatibility():
    """
    Check TensorRT version and provide compatibility information
    """
    try:
        version = trt.__version__
        major_version = int(version.split('.')[0])

        print(f"TensorRT version: {version}")

        if major_version >= 10:
            print("You are using TensorRT 10.x - latest version with improved performance")
            print("Recommended conversion method: convert_to_tensorrt with Torchscript.py")
        elif major_version >= 8:
            print("You are using TensorRT 8.x - stable version")
            print("Recommended conversion method: convert_to_tensorrt with onnx.py")
        else:
            print("You are using an older TensorRT version")
            print("Consider upgrading to TensorRT 8.x or 10.x for better performance")

        return version, major_version
    except Exception as e:
        print(f"Could not determine TensorRT version: {e}")
        return None, None

def load_trt_model_alternative(engine_path):
    """
    Alternative method to load TensorRT model using torch2trt TRTModule
    This is useful if the engine was created using torch2trt
    """
    try:
        # Try to import torch2trt
        sys.path.append(os.path.join(repopath, 'torch2trt'))
        from torch2trt import TRTModule

        if engine_path.endswith('.pth'):
            # Load torch2trt saved model
            model_trt = TRTModule()
            model_trt.load_state_dict(torch.load(engine_path))
            print(f"Successfully loaded TensorRT model using torch2trt from {engine_path}")
            return model_trt
        else:
            # Load raw engine file
            model_trt = TRTModule(engine_path)
            print(f"Successfully loaded TensorRT engine using torch2trt from {engine_path}")
            return model_trt

    except ImportError:
        print("torch2trt not available, falling back to native TensorRT implementation")
        return None
    except Exception as e:
        print(f"Failed to load model with torch2trt: {e}")
        return None

def inference(opt, args):
    # Try to load TensorRT model using different methods
    trt_model = None

    # Check if user wants to force torch2trt or if file is .pth
    if args.use_torch2trt or args.engine.endswith('.pth'):
        trt_model = load_trt_model_alternative(args.engine)
        if trt_model is None and args.use_torch2trt:
            print("Failed to load with torch2trt as requested")
            return

    # If torch2trt failed or not requested, use native TensorRT
    if trt_model is None:
        try:
            trt_model = TensorRTInference(args.engine)
        except Exception as e:
            print(f"Failed to load TensorRT engine: {e}")
            print("\n" + "="*60)
            print("TENSORRT ENGINE LOADING FAILED")
            print("="*60)

            if "version" in str(e).lower() or "incompatible" in str(e).lower():
                print("This appears to be a TensorRT version compatibility issue.")
                print(f"Current TensorRT version: {trt.__version__}")
                print("\nSOLUTION: Regenerate the engine file with your current TensorRT version")
                print("\nAvailable conversion scripts:")
                conversion_scripts = [
                    "convert_to_tensorrt Directly.py",
                    "convert_to_tensorrt with Torchscript.py",
                    "convert_to_tensorrt with onnx.py",
                    "convert_to_tensorrt with freezing.py"
                ]
                for script in conversion_scripts:
                    if os.path.exists(script):
                        print(f"  ✓ {script}")
                    else:
                        print(f"  ✗ {script} (not found)")

                print(f"\nExample usage:")
                print(f"  python \"convert_to_tensorrt with Torchscript.py\"")

            print("\nAvailable engine/model files in current directory:")
            found_files = False
            for f in os.listdir('.'):
                if f.endswith(('.plan', '.pth', '.engine', '.trt')):
                    print(f"  {f}")
                    found_files = True
            if not found_files:
                print("  No engine files found")

            print("\nAlternatives:")
            print("  1. Use the original inference script: python run/Inference.py")
            print("  2. Convert your model to TensorRT using the conversion scripts")
            print("  3. Use ONNX inference: python use_onnx.py")
            return
    
    save_dir = None
    _format = None
    
    if args.source.isnumeric() is True:
        _format = 'Webcam'

    elif os.path.isdir(args.source):
        save_dir = os.path.join('results', args.source.split(os.sep)[-1])
        _format = get_format(os.listdir(args.source))

    elif os.path.isfile(args.source):
        save_dir = 'results'
        _format = get_format([args.source])
        
    if args.dest is not None:
        save_dir = args.dest
        
    if save_dir is not None:
        os.makedirs(save_dir, exist_ok=True)
    
    sample_list = eval(_format + 'Loader')(args.source, opt.Test.Dataset.transforms)

    if args.verbose is True:
        samples = tqdm.tqdm(sample_list, desc='TensorRT Inference', total=len(
            sample_list), position=0, leave=False, bar_format='{desc:<30}{percentage:3.0f}%|{bar:50}{r_bar}')
    else:
        samples = sample_list
        
    writer = None
    background = None

    for sample in samples:
        if _format == 'Video' and writer is None:
            writer = cv2.VideoWriter(os.path.join(save_dir, sample['name'] + '.mp4'), cv2.VideoWriter_fourcc(*'mp4v'), sample_list.fps, sample['shape'][::-1])
            samples.total += int(sample_list.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if _format == 'Video' and sample['image'] is None:
            if writer is not None:
                writer.release()
            writer = None
            continue
        
        # Move sample to GPU
        sample = to_cuda(sample)

        with torch.no_grad():
            # Run TensorRT inference based on model type
            if hasattr(trt_model, 'infer'):
                # Custom TensorRT implementation
                output = trt_model.infer(sample['image'])
                out = {'pred': output}
            else:
                # torch2trt TRTModule
                output = trt_model(sample['image'])
                # torch2trt models typically return the raw output, so apply post-processing
                if isinstance(output, torch.Tensor):
                    pred = torch.sigmoid(output)
                    pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-8)
                    out = {'pred': pred}
                else:
                    # If output is already a dictionary
                    out = output if isinstance(output, dict) else {'pred': output}
                
        pred = to_numpy(out['pred'], sample['shape'])
        img = np.array(sample['original'])
        
        if args.type == 'map':
            img = (np.stack([pred] * 3, axis=-1) * 255).astype(np.uint8)
        elif args.type == 'rgba':
            r, g, b = cv2.split(img)
            pred = (pred * 255).astype(np.uint8)
            img = cv2.merge([r, g, b, pred])
        elif args.type == 'green':
            bg = np.stack([np.ones_like(pred)] * 3, axis=-1) * [120, 255, 155]
            img = img * pred[..., np.newaxis] + bg * (1 - pred[..., np.newaxis])
        elif args.type == 'blur':
            img = img * pred[..., np.newaxis] + cv2.GaussianBlur(img, (0, 0), 15) * (1 - pred[..., np.newaxis])
        elif args.type == 'overlay':
            bg = (np.stack([np.ones_like(pred)] * 3, axis=-1) * [120, 255, 155] + img) // 2
            img = bg * pred[..., np.newaxis] + img * (1 - pred[..., np.newaxis])
            border = cv2.Canny(((pred > .5) * 255).astype(np.uint8), 50, 100)
            img[border != 0] = [120, 255, 155]
        elif args.type.lower().endswith(('.jpg', '.jpeg', '.png')):
            if background is None:
                background = cv2.cvtColor(cv2.imread(args.type), cv2.COLOR_BGR2RGB)
                background = cv2.resize(background, img.shape[:2][::-1])
            img = img * pred[..., np.newaxis] + background * (1 - pred[..., np.newaxis])
            
        img = img.astype(np.uint8)
        
        if _format == 'Image':
            Image.fromarray(img).save(os.path.join(save_dir, sample['name'] + '.png'))
        elif _format == 'Video' and writer is not None:
            writer.write(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        elif _format == 'Webcam':
            cv2.imshow('InSPyReNet TensorRT', img)

if __name__ == "__main__":
    args = _args()

    # Handle compatibility check
    if args.check_compatibility:
        print("TensorRT Compatibility Check")
        print("="*40)
        version, major_version = check_tensorrt_compatibility()

        print(f"\nChecking engine file: {args.engine}")
        if os.path.exists(args.engine):
            print(f"✓ Engine file exists: {args.engine}")
            try:
                # Try to load just the engine metadata without full initialization
                with open(args.engine, 'rb') as f:
                    engine_data = f.read()
                runtime = trt.Runtime(TRT_LOGGER)
                engine = runtime.deserialize_cuda_engine(engine_data)
                if engine is not None:
                    print("✓ Engine file is compatible with current TensorRT version")
                else:
                    print("✗ Engine file is NOT compatible with current TensorRT version")
                    print("  → Regenerate the engine file with current TensorRT version")
            except Exception as e:
                print(f"✗ Engine compatibility check failed: {e}")
                print("  → Regenerate the engine file with current TensorRT version")
        else:
            print(f"✗ Engine file not found: {args.engine}")

        print(f"\nAvailable conversion scripts:")
        conversion_scripts = [
            "convert_to_tensorrt Directly.py",
            "convert_to_tensorrt with Torchscript.py",
            "convert_to_tensorrt with onnx.py",
            "convert_to_tensorrt with freezing.py"
        ]
        for script in conversion_scripts:
            if os.path.exists(script):
                print(f"  ✓ {script}")
            else:
                print(f"  ✗ {script}")
        sys.exit(0)

    opt = load_config(args.config)
    inference(opt, args)
