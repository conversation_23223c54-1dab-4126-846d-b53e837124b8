#!/usr/bin/env python3
"""
TensorRT Diagnostic Tool for InSPyReNet
Helps identify optimal TensorRT settings for different versions
"""

import os
import subprocess
import sys
import re
from pathlib import Path

def get_tensorrt_version():
    """Get detailed TensorRT version information"""
    try:
        result = subprocess.run(["trtexec", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("TensorRT Version Information:")
            print("=" * 40)
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")
            
            # Extract version number
            for line in result.stdout.split('\n'):
                if 'TensorRT' in line:
                    version_match = re.search(r'(\d+)\.(\d+)\.(\d+)', line)
                    if version_match:
                        major, minor, patch = map(int, version_match.groups())
                        return (major, minor, patch)
        return None
    except Exception as e:
        print(f"Error getting TensorRT version: {e}")
        return None

def test_conversion_settings(onnx_path, base_engine_name):
    """Test different TensorRT conversion settings"""
    
    if not os.path.exists(onnx_path):
        print(f"❌ ONNX file not found: {onnx_path}")
        return
    
    trt_version = get_tensorrt_version()
    if not trt_version:
        print("❌ Could not determine TensorRT version")
        return
    
    print(f"\nTesting conversion settings for TensorRT {trt_version[0]}.{trt_version[1]}.{trt_version[2]}")
    print("=" * 60)
    
    # Test configurations
    configs = [
        {
            "name": "FP32_Basic",
            "args": ["--fp32"],
            "description": "Basic FP32 conversion"
        },
        {
            "name": "FP32_Conservative", 
            "args": ["--fp32", "--builderOptimizationLevel=3", "--noTF32"],
            "description": "Conservative FP32 with disabled TF32"
        },
        {
            "name": "FP16_Basic",
            "args": ["--fp16"],
            "description": "Basic FP16 conversion"
        },
        {
            "name": "FP16_Conservative",
            "args": ["--fp16", "--builderOptimizationLevel=3", "--noTF32", "--stronglyTyped"],
            "description": "Conservative FP16 with strict typing"
        }
    ]
    
    # Add version-specific configs
    if trt_version >= (10, 11, 0):
        configs.append({
            "name": "FP16_Ultra_Conservative",
            "args": ["--fp16", "--builderOptimizationLevel=1", "--noTF32", "--stronglyTyped", "--sparsity=disable"],
            "description": "Ultra-conservative FP16 for TensorRT 10.11+"
        })
    
    results = []
    
    for config in configs:
        print(f"\n🧪 Testing: {config['name']}")
        print(f"   Description: {config['description']}")
        
        engine_path = f"{base_engine_name}_{config['name']}.engine"
        
        # Build command
        cmd = [
            "trtexec",
            f"--onnx={onnx_path}",
            f"--saveEngine={engine_path}",
            "--memPoolSize=workspace:2048",
            "--verbose"
        ] + config['args']
        
        print(f"   Command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(engine_path):
                engine_size = os.path.getsize(engine_path) / (1024 * 1024)
                print(f"   ✅ SUCCESS - Engine size: {engine_size:.1f}MB")
                results.append({
                    "config": config['name'],
                    "success": True,
                    "engine_path": engine_path,
                    "size_mb": engine_size
                })
            else:
                print(f"   ❌ FAILED")
                if result.stderr:
                    # Show only the most relevant error lines
                    error_lines = [line for line in result.stderr.split('\n') 
                                 if 'error' in line.lower() or 'failed' in line.lower()]
                    if error_lines:
                        print(f"   Error: {error_lines[0][:100]}...")
                results.append({
                    "config": config['name'],
                    "success": False,
                    "error": result.stderr[:200] if result.stderr else "Unknown error"
                })
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ TIMEOUT")
            results.append({
                "config": config['name'],
                "success": False,
                "error": "Conversion timeout"
            })
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            results.append({
                "config": config['name'],
                "success": False,
                "error": str(e)
            })
    
    # Summary
    print(f"\n📊 CONVERSION RESULTS SUMMARY")
    print("=" * 60)
    
    successful_configs = [r for r in results if r['success']]
    failed_configs = [r for r in results if not r['success']]
    
    if successful_configs:
        print("✅ Successful conversions:")
        for config in successful_configs:
            print(f"   • {config['config']}: {config['size_mb']:.1f}MB")
    
    if failed_configs:
        print("\n❌ Failed conversions:")
        for config in failed_configs:
            print(f"   • {config['config']}: {config['error'][:50]}...")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 60)
    
    if trt_version >= (10, 11, 0):
        print("For TensorRT 10.11+:")
        if any(c['config'].startswith('FP32') and c['success'] for c in results):
            print("  ✅ Use FP32 engines - they appear to work reliably")
        if any(c['config'].startswith('FP16') and c['success'] for c in results):
            fp16_configs = [c for c in successful_configs if c['config'].startswith('FP16')]
            if fp16_configs:
                best_fp16 = min(fp16_configs, key=lambda x: x['size_mb'])
                print(f"  ⚠️  FP16 works with conservative settings: {best_fp16['config']}")
            else:
                print("  ❌ FP16 appears problematic - stick with FP32")
        else:
            print("  ❌ FP16 not working - use FP32 only")
    else:
        print("For TensorRT 10.10:")
        if successful_configs:
            print("  ✅ Multiple configurations work")
            best_config = min(successful_configs, key=lambda x: x['size_mb'])
            print(f"  🏆 Best option: {best_config['config']} ({best_config['size_mb']:.1f}MB)")
    
    return results

def main():
    print("🔧 TensorRT Diagnostic Tool for InSPyReNet")
    print("=" * 50)
    
    # Look for ONNX files
    onnx_files = list(Path('.').glob('InsPyReNet_*.onnx'))
    
    if not onnx_files:
        print("❌ No InsPyReNet ONNX files found in current directory")
        print("   Please run the ONNX creation script first")
        return
    
    print(f"Found {len(onnx_files)} ONNX file(s):")
    for i, onnx_file in enumerate(onnx_files):
        print(f"  {i+1}. {onnx_file}")
    
    # Test with the first ONNX file (usually 1024x1024)
    test_onnx = onnx_files[0]
    base_name = test_onnx.stem
    
    print(f"\n🧪 Testing with: {test_onnx}")
    
    results = test_conversion_settings(str(test_onnx), base_name)
    
    print(f"\n🎯 FINAL RECOMMENDATIONS:")
    print("=" * 50)
    print("Based on the test results above:")
    print("1. Use the successful configurations for your inference")
    print("2. If FP16 fails, stick with FP32 - it's more reliable")
    print("3. For TensorRT 10.11+, use conservative optimization levels")
    print("4. Test your specific use case with the generated engines")

if __name__ == "__main__":
    main()
