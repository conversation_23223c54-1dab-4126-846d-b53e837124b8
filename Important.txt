
# To create, optimize and test all the plus ultra onnx models:
python "onnx_create plus ultra.py" --multi-aspect
python "onnx_optimize plus ultra for cuda.py" --optimize-all
python "onnx_optimize plus ultra for cuda.py" --image "input part left.png" --output "output full.png"



conda deactivate
conda remove --name onnxgpu --all
conda create -n onnxgpu -y python==3.10
conda activate onnxgpu
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118
conda install -c conda-forge cudatoolkit=11.8 cudnn
pip install onnxruntime-gpu pip install opencv-python

conda deactivate
conda remove --name onnxoptimizer --all
conda create -n onnxoptimizer -y
conda activate onnxoptimizer
conda install pip
# For tensorRt
pip install tensorrt onnx numpy pycuda
# For Cuda
conda install -c conda-forge onnxoptimizer
pip install onnx onnxruntime-gpu numpy psutil gputil


#For conversion through Torchscript:
conda deactivate
conda remove --name inspyrenet_ts --allconda activate inspyrenet_ts
pip install torch torch-vision [??? torch-tensorrt ???] opencv-python kornia easydict timm tqdm --extra-index-url https://download.pytorch.org/whl/cu128
cd C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\python
pip install tensorrt-**********-cp310-none-win_amd64.whl
f:
cd F:\Catechese\EditeurAudioVideo\Archives\InSPyReNet
git clone https://github.com/NVIDIA-AI-IOT/torch2trt
cd torch2trt
python setup.py install
conda install numpy pyyaml scikit-image matplotlib -y
pip install einops
python -m pip install --upgrade pillow



# To verify installation:
python -c "import torch; import tensorrt as trt; import numpy as np; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'TensorRT version: {trt.__version__}')"

