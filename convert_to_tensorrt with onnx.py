import os
import torch
import torch.nn as nn
import tensorrt as trt
import numpy as np
from torch2trt import torch2trt

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

def create_engine(config_path, checkpoint_dir, engine_path, batch_size=1, workspace_size=3<<30):
    """
    Create TensorRT engine from PyTorch model using TorchScript as an intermediate representation
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        engine_path: Path to save the TensorRT engine
        batch_size: Batch size for optimization
        workspace_size: Maximum workspace size in bytes
    """
    # Load configuration
    opt = load_config(config_path)
    
    # Load model
    model = eval(opt.Model.name)(**opt.Model)
    model.load_state_dict(torch.load(os.path.join(checkpoint_dir, 'latest.pth'), map_location=torch.device('cpu')), strict=True)
    model = model.cuda()
    model.eval()
    
    # Create wrapper for inference to ensure consistent interface
    class InferenceWrapper(nn.Module):
        def __init__(self, model):
            super().__init__()
            self.model = model
            self.model.eval()
            self.model.forward = self.model.forward_inference

        def forward(self, x):
            output = self.model({'image': x})
            pred = output['pred']
            saliency = output['saliency']
            laplacian = output['laplacian']
            return pred, saliency[0], saliency[1], saliency[2], saliency[3], laplacian[0], laplacian[1], laplacian[2]

    # Create the wrapper and prepare input
    wrapper = InferenceWrapper(model)
    wrapper.eval()
    
    # Create dummy input tensor
    input_shape = (batch_size, 3, 1280, 1280)
    dummy_input = torch.randn(input_shape).cuda()
    
    print("Converting model to TorchScript...")
    # Convert to TorchScript (trace mode)
    try:
        # Use torch.jit.trace for stable models with deterministic control flow
        traced_model = torch.jit.trace(wrapper, dummy_input)
        traced_model = torch.jit.freeze(traced_model)
        torch.jit.save(traced_model, "temp_model.pt")
        print("TorchScript export completed successfully")
    except Exception as e:
        print(f"Error during TorchScript tracing: {e}")
        # Fall back to script mode if tracing fails
        print("Falling back to script mode...")
        try:
            scripted_model = torch.jit.script(wrapper)
            torch.jit.save(scripted_model, "temp_model.pt")
            traced_model = scripted_model
            print("TorchScript script mode export completed successfully")
        except Exception as e:
            print(f"Error during TorchScript scripting: {e}")
            return None
    
    # Convert TorchScript model to TensorRT
    print("Converting TorchScript model to TensorRT...")
    
    # TensorRT logger
    TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
    
    # Using torch2trt for conversion
    try:
        # Load TorchScript model
        ts_model = torch.jit.load("temp_model.pt")
        ts_model.eval()
        ts_model.cuda()
        
        # Define conversion parameters
        conversion_params = {
            'fp16_mode': True,                # Enable FP16 precision
            'max_workspace_size': workspace_size,  # Maximum workspace size
            'strict_type_constraints': True,  # Enforce precision constraints
            'max_batch_size': batch_size      # Maximum batch size
        }
        
        # Convert to TensorRT
        trt_model = torch2trt(ts_model, [dummy_input], **conversion_params)
        
        # Save the TensorRT model
        torch.save(trt_model.state_dict(), engine_path)
        print(f"TensorRT engine saved to {engine_path}")
        
        # Clean up
        os.remove("temp_model.pt")
        return trt_model
        
    except Exception as e:
        print(f"Error during TensorRT conversion: {e}")
        # Manual TensorRT conversion if torch2trt fails
        print("Attempting manual TensorRT conversion...")
        
        try:
            # Create builder and network
            builder = trt.Builder(TRT_LOGGER)
            network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            
            # Create config
            config = builder.create_builder_config()
            config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, workspace_size)
            config.set_flag(trt.BuilderFlag.FP16)
            
            # Use PyTorch-TensorRT parser
            parser = trt.PyTorchParser(network)
            parser_success = parser.parse(os.path.abspath("temp_model.pt"))
            
            if not parser_success:
                print("Failed to parse TorchScript model")
                return None
            
            # Build the engine
            serialized_engine = builder.build_serialized_network(network, config)
            if serialized_engine is None:
                print("Failed to create engine")
                return None
            
            # Save the engine
            with open(engine_path, "wb") as f:
                f.write(serialized_engine)
            
            # Clean up
            os.remove("temp_model.pt")
            return serialized_engine
            
        except Exception as e:
            print(f"Error during manual TensorRT conversion: {e}")
            if os.path.exists("temp_model.pt"):
                os.remove("temp_model.pt")
            return None

def load_and_test_engine(engine_path, input_shape=(1, 3, 1280, 1280)):
    """
    Load and test the TensorRT engine
    Args:
        engine_path: Path to the TensorRT engine
        input_shape: Shape of the input tensor
    """
    try:
        # For torch2trt saved models
        if engine_path.endswith('.pth'):
            from torch2trt import TRTModule
            model_trt = TRTModule()
            model_trt.load_state_dict(torch.load(engine_path))
            
            # Test with random input
            dummy_input = torch.randn(input_shape).cuda()
            outputs = model_trt(dummy_input)
            print("Engine loaded and tested successfully using torch2trt")
            return model_trt
        
        # For native TensorRT engines
        else:
            # Initialize TensorRT stuff
            TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
            runtime = trt.Runtime(TRT_LOGGER)
            
            # Load engine
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            
            engine = runtime.deserialize_cuda_engine(engine_data)
            context = engine.create_execution_context()
            
            # Test with random input
            dummy_input = torch.randn(input_shape).cuda()
            
            # Allocate memory for inputs and outputs
            bindings = []
            input_idx = engine.get_binding_index("image")
            context.set_binding_shape(input_idx, input_shape)
            bindings.append(dummy_input.data_ptr())
            
            # Allocate output memory
            output_shapes = []
            for i in range(1, engine.num_bindings):
                output_shape = context.get_binding_shape(i)
                output = torch.empty(tuple(output_shape), dtype=torch.float32, device=torch.device('cuda'))
                bindings.append(output.data_ptr())
                output_shapes.append(output)
            
            # Execute inference
            context.execute_v2(bindings)
            print("Engine loaded and tested successfully using native TensorRT")
            return engine
            
    except Exception as e:
        print(f"Error testing TensorRT engine: {e}")
        return None
    
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert model to TensorRT using TorchScript')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to configuration file')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint')
    parser.add_argument('--engine-path', '-e', type=str, default="engine.trt", help='Path to save TensorRT engine')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workspace-size', type=int, default=3<<30, help='Workspace size in bytes')
    parser.add_argument('--test', action='store_true', help='Test the engine after conversion')
    
    args = parser.parse_args()
    
    # Create TensorRT engine
    engine = create_engine(args.config, args.checkpoint_dir, args.engine_path, args.batch_size, args.workspace_size)
    
    # Test engine if requested
    if args.test and engine is not None:
        load_and_test_engine(args.engine_path, (args.batch_size, 3, 1280, 1280))