#!/usr/bin/env python3
"""
Fix FP16 Type Error

This script specifically addresses the LayerNormalization type mismatch error:
"Type parameter (T) of Optype (LayerNormalization) bound to different types 
(tensor(float16) and tensor(float) in node"

This error occurs when some inputs to LayerNormalization are FP16 while others are FP32.

Usage:
    python fix_fp16_type_error.py
    python fix_fp16_type_error.py --input InsPyReNet_800x800.onnx
"""

import os
import sys
import argparse
import onnx
import numpy as np
from onnx import numpy_helper
from onnxconverter_common import float16

def fix_layernorm_type_mismatch(model_path, output_path=None, method='keep_fp32'):
    """
    Fix LayerNormalization type mismatch by ensuring consistent types
    
    Args:
        model_path: Path to problematic FP16 model
        output_path: Path to save fixed model
        method: 'keep_fp32', 'force_fp16', or 'manual_fix'
    """
    
    if output_path is None:
        base_name = model_path.replace('.onnx', '').replace('_fp16_fixed', '')
        output_path = f"{base_name}_fp16_typefixed.onnx"
    
    print(f"Fixing LayerNormalization type mismatch: {model_path}")
    print(f"Method: {method}")
    print(f"Output: {output_path}")
    
    try:
        # Load the original FP32 model instead of the broken FP16 model
        original_model_path = model_path.replace('_fp16_fixed.onnx', '.onnx')
        if not os.path.exists(original_model_path):
            print(f"❌ Original FP32 model not found: {original_model_path}")
            return None
        
        print(f"Loading original FP32 model: {original_model_path}")
        model = onnx.load(original_model_path)
        
        if method == 'keep_fp32':
            # Don't convert LayerNormalization and related ops to FP16
            fixed_model = convert_with_layernorm_fp32(model)
        elif method == 'force_fp16':
            # Try to make everything FP16 but handle LayerNorm specially
            fixed_model = convert_with_layernorm_fp16(model)
        elif method == 'manual_fix':
            # Manual approach - convert weights only
            fixed_model = manual_fp16_conversion(model)
        else:
            raise ValueError(f"Unknown method: {method}")
        
        # Save fixed model
        onnx.save(fixed_model, output_path)
        print(f"✓ Fixed model saved: {output_path}")
        
        # Test the fixed model
        if test_model_loading(output_path):
            print("✅ Model fix successful!")
            return output_path
        else:
            print("❌ Model still has issues")
            return None
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def convert_with_layernorm_fp32(model):
    """
    Convert model to FP16 but keep LayerNormalization and related ops in FP32
    """
    # Very conservative list - keep all normalization and attention ops in FP32
    fp32_ops = [
        'LayerNormalization',
        'BatchNormalization', 
        'InstanceNormalization',
        'GroupNormalization',
        'Softmax',
        'Sigmoid',
        'Add',  # Often used with LayerNorm
        'Sub',  # Often used with LayerNorm
        'Mul',  # Often used with LayerNorm
        'Div',  # Often used with LayerNorm
        'Sqrt', # Used in normalization
        'ReduceMean',  # Used in normalization
        'ReduceSum',   # Used in normalization
        'MatMul',      # Attention operations
        'Gemm',        # Matrix operations
        'Cast',        # Type conversions
        'Shape', 'Reshape', 'Transpose',  # Shape operations
        'Slice', 'Gather', 'Concat', 'Split',  # Indexing
    ]
    
    print(f"  Converting with {len(fp32_ops)} ops staying in FP32")
    
    model_fp16 = float16.convert_float_to_float16(
        model,
        keep_io_types=True,
        disable_shape_infer=True,
        min_positive_val=1e-4,
        max_finite_val=1e4,
        op_block_list=fp32_ops
    )
    
    return model_fp16

def convert_with_layernorm_fp16(model):
    """
    Try to convert everything to FP16 but handle LayerNorm inputs carefully
    """
    # Minimal block list - only the most critical ops
    fp32_ops = [
        'Sigmoid',  # Final activation
        'Div',      # Division with small values
    ]
    
    print(f"  Aggressive conversion with only {len(fp32_ops)} ops in FP32")
    
    model_fp16 = float16.convert_float_to_float16(
        model,
        keep_io_types=True,
        disable_shape_infer=True,
        min_positive_val=1e-4,
        max_finite_val=1e4,
        op_block_list=fp32_ops
    )
    
    return model_fp16

def manual_fp16_conversion(model):
    """
    Manual conversion - only convert weights, keep all operations in original precision
    """
    print("  Manual conversion: converting weights only")
    
    # Create a copy
    model_copy = onnx.ModelProto()
    model_copy.CopyFrom(model)
    
    # Convert only the initializers (weights) to FP16
    for initializer in model_copy.graph.initializer:
        if initializer.data_type == onnx.TensorProto.FLOAT:
            # Get the numpy array
            weights = numpy_helper.to_array(initializer)
            
            # Convert to FP16
            weights_fp16 = weights.astype(np.float16)
            
            # Create new initializer
            new_initializer = numpy_helper.from_array(weights_fp16, initializer.name)
            
            # Replace the old initializer
            initializer.CopyFrom(new_initializer)
    
    # Keep all value_info, inputs, and outputs as FP32
    # This ensures operations stay in FP32 even with FP16 weights
    
    return model_copy

def test_model_loading(model_path):
    """Test if model can be loaded without errors"""
    try:
        import onnxruntime as ort
        
        print(f"  Testing model loading: {model_path}")
        
        # Try to create session
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        print(f"  ✓ Model loads successfully")
        
        # Get model info
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        print(f"  Input: {input_info.name}, type: {input_info.type}")
        print(f"  Output: {output_info.name}, type: {output_info.type}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model loading failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Fix LayerNormalization type mismatch in FP16 models')
    parser.add_argument('--input', '-i', type=str, default='InsPyReNet_800x800.onnx',
                       help='Input FP32 ONNX model (not the broken FP16 one)')
    parser.add_argument('--output', '-o', type=str,
                       help='Output file path (optional, auto-generated if not provided)')
    parser.add_argument('--method', '-m', type=str, default='keep_fp32',
                       choices=['keep_fp32', 'force_fp16', 'manual_fix'],
                       help='Fix method (default: keep_fp32)')
    
    args = parser.parse_args()
    
    print("LayerNormalization Type Mismatch Fixer")
    print("=" * 50)
    print("This script fixes the specific error:")
    print("'Type parameter (T) of Optype (LayerNormalization) bound to different types'")
    print("=" * 50)
    
    if not os.path.exists(args.input):
        print(f"❌ Input model not found: {args.input}")
        return 1
    
    # Try different methods in order of preference
    methods_to_try = ['keep_fp32', 'manual_fix', 'force_fp16']
    
    if args.method in methods_to_try:
        # Move specified method to front
        methods_to_try.remove(args.method)
        methods_to_try.insert(0, args.method)
    
    for method in methods_to_try:
        print(f"\n{'='*60}")
        print(f"TRYING METHOD: {method}")
        print('='*60)
        
        result = fix_layernorm_type_mismatch(args.input, args.output, method)
        
        if result:
            print(f"\n🎉 SUCCESS with method: {method}")
            print(f"Fixed model: {result}")
            print(f"\nNext steps:")
            print(f"1. Test the fixed model:")
            print(f"   python run_fp16_test_fixed.py")
            print(f"2. Convert to TensorRT:")
            print(f"   python convert_onnx_to_tensorrt.py --onnx {result} --fp16")
            return 0
        else:
            print(f"❌ Method {method} failed, trying next method...")
    
    print(f"\n❌ All methods failed. Recommendations:")
    print(f"1. Use the original FP32 model for TensorRT conversion")
    print(f"2. Try regenerating the ONNX model with different settings")
    print(f"3. Use PyTorch inference instead: python run/Inference.py")
    
    return 1

if __name__ == "__main__":
    sys.exit(main())