@echo off
echo ========================================
echo FP16 Conversion Test Script
echo ========================================
echo.
echo This script will:
echo 1. Activate conda onnxgpu environment
echo 2. Convert FP32 model to FP16
echo 3. Test the FP16 model output
echo 4. Compare FP32 vs FP16 results
echo.

REM Activate conda environment
echo Activating conda environment: onnxgpu
call conda activate onnxgpu
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate conda environment 'onnxgpu'
    echo Please make sure the environment exists: conda env list
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 1: Convert FP32 to FP16
echo ========================================
echo Converting InsPyReNet_800x800.onnx to FP16...
python onnx_convert_fp16_fixed.py --input InsPyReNet_800x800.onnx --method mixed

if %errorlevel% neq 0 (
    echo ERROR: FP16 conversion failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 2: Test FP16 Model Output
echo ========================================
echo Testing the FP16 model...
python test_model_output.py --model InsPyReNet_800x800_fp16_fixed.onnx

if %errorlevel% neq 0 (
    echo ERROR: FP16 model test failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 3: Convert FP16 to TensorRT
echo ========================================
echo Converting FP16 ONNX to TensorRT engine...
python convert_onnx_to_tensorrt.py --onnx InsPyReNet_800x800_fp16_fixed.onnx --fp16

if %errorlevel% neq 0 (
    echo ERROR: TensorRT conversion failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS!
echo ========================================
echo All conversions completed successfully.
echo.
echo Files created:
echo - InsPyReNet_800x800_fp16_fixed.onnx (FP16 ONNX model)
echo - InsPyReNet_800x800_fp16_fixed.engine (TensorRT FP16 engine)
echo - test_output_InsPyReNet_800x800_fp16_fixed.png (FP16 test output)
echo.
echo You can now test TensorRT inference with:
echo python InferenceTensorRT.py --source input.png --engine InsPyReNet_800x800_fp16_fixed.engine
echo.
pause