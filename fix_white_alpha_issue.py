#!/usr/bin/env python3
"""
Fix White Alpha Matte Issue

This script addresses the issue where FP16 models produce completely white alpha mattes
instead of proper transparency masks. The problem is typically caused by:

1. Over-normalization in the model output
2. FP16 precision issues with small epsilon values
3. Incorrect handling of the sigmoid output range

Usage:
    python fix_white_alpha_issue.py
    python fix_white_alpha_issue.py --regenerate-models
    python fix_white_alpha_issue.py --test-existing
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import numpy as np
import onnx
from PIL import Image

# Add lib to path
sys.path.append('.')
from lib import *
from utils.misc import *

def create_fixed_onnx_model(config_path, checkpoint_dir, output_path, input_size=(800, 800)):
    """
    Create ONNX model with fixed normalization that prevents white alpha matte issue
    """
    print(f"Creating fixed ONNX model: {output_path}")
    
    try:
        # Load configuration and model
        opt = load_config(config_path)
        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        class FixedWrapper(nn.Module):
            def __init__(self, base_model):
                super().__init__()
                self.model = base_model
                self.model.eval()
                if hasattr(self.model, 'forward_inference'):
                    self.model.forward = self.model.forward_inference

            def forward(self, x):
                # Get dimensions
                _, _, H, W = x.shape

                # Ensure dimensions are divisible by patch size (4)
                patch_size = 4
                new_H = ((H + patch_size - 1) // patch_size) * patch_size
                new_W = ((W + patch_size - 1) // patch_size) * patch_size
                
                # Pad if needed
                if new_H != H or new_W != W:
                    pad_H = new_H - H
                    pad_W = new_W - W
                    x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

                # Forward pass
                output = self.model({'image': x})
                
                # Get the raw logits (before sigmoid) to avoid double processing
                if 'saliency' in output and len(output['saliency']) > 0:
                    # Use the raw saliency output (d0) before any processing
                    pred_logits = output['saliency'][3]  # d0 - final saliency map
                    
                    # Apply sigmoid to get probabilities
                    pred = torch.sigmoid(pred_logits)
                    
                    # NO NORMALIZATION - this is what causes the white alpha issue
                    # The sigmoid output is already in [0,1] range and properly distributed
                    
                    # Just ensure values are in valid range (should already be due to sigmoid)
                    pred = torch.clamp(pred, 0.0, 1.0)
                    
                else:
                    # Fallback: use the model's pred output but without additional normalization
                    pred = output.get('pred', torch.sigmoid(output['saliency'][3]))
                    pred = torch.clamp(pred, 0.0, 1.0)
                
                return pred

        # Create wrapper
        wrapper = FixedWrapper(model)
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, *input_size)
        
        # Export to ONNX
        torch.onnx.export(
            wrapper,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=17,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
        )
        
        print(f"✓ Fixed ONNX model created: {output_path}")
        
        # Test the model immediately
        if test_onnx_model_output(output_path):
            print("✓ Model test passed - no white alpha issue detected")
            return True
        else:
            print("❌ Model test failed - may still have issues")
            return False
            
    except Exception as e:
        print(f"❌ Error creating fixed model: {e}")
        return False

def test_onnx_model_output(model_path, test_image="input.png"):
    """
    Test ONNX model output to detect white alpha matte issue
    """
    try:
        import onnxruntime as ort
        
        if not os.path.exists(model_path):
            print(f"Model not found: {model_path}")
            return False
            
        if not os.path.exists(test_image):
            print(f"Test image not found: {test_image}")
            return False
        
        # Load model
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(model_path, providers=providers)
        
        # Get input info
        input_info = session.get_inputs()[0]
        input_shape = input_info.shape
        
        # Handle dynamic shapes
        if isinstance(input_shape[2], str) or input_shape[2] is None:
            target_size = (800, 800)
        else:
            target_size = (input_shape[2], input_shape[3])
        
        # Preprocess test image
        img = Image.open(test_image).convert('RGB')
        img = img.resize(target_size, Image.BILINEAR)
        img = np.array(img, dtype=np.float32) / 255.0
        img = (img - np.array([0.485, 0.456, 0.406])) / np.array([0.229, 0.224, 0.225])
        img = np.transpose(img, (2, 0, 1))
        img = np.expand_dims(img, axis=0)
        
        # Run inference
        outputs = session.run(None, {input_info.name: img})
        output = outputs[0].squeeze()
        
        # Analyze output
        output_min = np.min(output)
        output_max = np.max(output)
        output_mean = np.mean(output)
        output_std = np.std(output)
        
        print(f"  Output analysis:")
        print(f"    Range: [{output_min:.4f}, {output_max:.4f}]")
        print(f"    Mean: {output_mean:.4f}")
        print(f"    Std: {output_std:.4f}")
        
        # Check for white alpha issue
        white_pixels = np.sum(output > 0.95)
        total_pixels = output.size
        white_percentage = white_pixels / total_pixels * 100
        
        print(f"    White pixels (>0.95): {white_pixels}/{total_pixels} ({white_percentage:.1f}%)")
        
        # Detect issues
        if white_percentage > 90:
            print(f"  ❌ WHITE ALPHA ISSUE DETECTED: {white_percentage:.1f}% of pixels are white")
            return False
        elif white_percentage > 70:
            print(f"  ⚠️ Potential issue: {white_percentage:.1f}% of pixels are white")
            return False
        elif output_std < 0.05:
            print(f"  ⚠️ Low contrast: std={output_std:.4f} (may indicate poor segmentation)")
            return False
        else:
            print(f"  ✓ Output looks good: {white_percentage:.1f}% white pixels, std={output_std:.4f}")
            return True
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def fix_existing_models():
    """
    Fix existing ONNX models that have the white alpha issue
    """
    import glob
    
    # Find existing models
    models = glob.glob("*.onnx")
    models = [m for m in models if not any(skip in m for skip in ['fixed', 'test', 'temp'])]
    
    if not models:
        print("No ONNX models found to fix")
        return
    
    print(f"Found {len(models)} models to check/fix:")
    for model in models:
        print(f"  {model}")
    
    print("\n" + "=" * 60)
    
    for model_path in models:
        print(f"\nTesting {model_path}:")
        
        if test_onnx_model_output(model_path):
            print(f"✓ {model_path} is working correctly")
        else:
            print(f"❌ {model_path} has issues - needs regeneration")
            print(f"   Recommendation: Regenerate this model using the fixed wrapper")

def regenerate_all_models():
    """
    Regenerate all models with the fixed wrapper
    """
    config_path = "configs/extra_dataset/Plus_Ultra.yaml"
    checkpoint_dir = "snapshots/Plus_Ultra"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return
    
    if not os.path.exists(checkpoint_dir):
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return
    
    # Model dimensions
    model_dimensions = [
        (720, 1280, "1280x720"),   # 16:9 landscape
        (960, 1280, "1280x960"),   # 4:3 landscape
        (1280, 720, "720x1280"),   # 9:16 portrait
        (1280, 960, "960x1280"),   # 3:4 portrait
        (800, 800, "800x800")      # 1:1 square
    ]
    
    print("Regenerating all models with fixed wrapper...")
    print("=" * 60)
    
    successful = 0
    failed = 0
    
    for height, width, name in model_dimensions:
        output_path = f"InsPyReNet_{name}_fixed.onnx"
        print(f"\nCreating {output_path}...")
        
        if create_fixed_onnx_model(config_path, checkpoint_dir, output_path, (height, width)):
            successful += 1
        else:
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"REGENERATION SUMMARY")
    print("=" * 60)
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total: {successful + failed}")
    
    if successful > 0:
        print(f"\n✅ Fixed models created successfully!")
        print(f"You can now convert these to FP16 and TensorRT:")
        print(f"  python onnx_convert_fp16_fixed.py --input InsPyReNet_800x800_fixed.onnx")
        print(f"  python convert_onnx_to_tensorrt.py --onnx InsPyReNet_800x800_fixed_fp16_fixed.onnx --fp16")

def main():
    parser = argparse.ArgumentParser(description='Fix white alpha matte issue in ONNX models')
    parser.add_argument('--regenerate-models', action='store_true',
                       help='Regenerate all models with fixed wrapper')
    parser.add_argument('--test-existing', action='store_true',
                       help='Test existing models for white alpha issue')
    parser.add_argument('--config', type=str, default="configs/extra_dataset/Plus_Ultra.yaml",
                       help='Path to config file')
    parser.add_argument('--checkpoint-dir', type=str, default="snapshots/Plus_Ultra",
                       help='Path to checkpoint directory')
    
    args = parser.parse_args()
    
    print("White Alpha Matte Issue Fixer")
    print("=" * 40)
    print("This script fixes the issue where models produce completely white alpha mattes")
    print("instead of proper transparency masks.")
    print("=" * 40)
    
    if args.test_existing:
        print("Testing existing models...")
        fix_existing_models()
    elif args.regenerate_models:
        print("Regenerating models with fixed wrapper...")
        regenerate_all_models()
    else:
        print("Choose an action:")
        print("  --test-existing: Test current models for white alpha issue")
        print("  --regenerate-models: Create new fixed models")
        print("")
        print("Example usage:")
        print("  python fix_white_alpha_issue.py --test-existing")
        print("  python fix_white_alpha_issue.py --regenerate-models")

if __name__ == "__main__":
    main()