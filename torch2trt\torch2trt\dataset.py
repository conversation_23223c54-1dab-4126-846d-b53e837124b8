import gc
import os
import torch
import torch.nn as nn
import tensorrt as trt
import numpy as np
from torch2trt import torch2trt

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

def create_engine(config_path, checkpoint_dir, engine_path, batch_size=1, workspace_size=3<<30):
    """
    Create TensorRT engine from PyTorch model using TorchScript as an intermediate representation
    Args:
        config_path: Path to configuration file
        checkpoint_dir: Directory containing model checkpoint
        engine_path: Path to save the TensorRT engine
        batch_size: Batch size for optimization
        workspace_size: Maximum workspace size in bytes
    """
    # Check if TensorRT engine already exists
    if os.path.exists(engine_path):
        print(f"TensorRT engine already exists at {engine_path}")
        try:
            # Try loading the existing engine
            TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            engine = runtime.deserialize_cuda_engine(engine_data)
            print("Successfully loaded existing engine")
            return engine
        except Exception as e:
            print(f"Could not load existing engine: {e}")
            print("Will recreate the engine")
    
    # Define path for TorchScript model
    torchscript_path = "temp_model.pt"
    
    # Check if TorchScript model already exists
    torchscript_exists = os.path.exists(torchscript_path)
    
    if not torchscript_exists:
        # Load configuration
        opt = load_config(config_path)
        
        # Load model
        model = eval(opt.Model.name)(**opt.Model)
        model.load_state_dict(torch.load(os.path.join(checkpoint_dir, 'latest.pth'), map_location=torch.device('cpu')), strict=True)
        model = model.cuda()
        model.eval()
        
        # Create wrapper for inference to ensure consistent interface
        # This is a more TorchScript-friendly implementation that avoids hooks
        class InferenceWrapper(nn.Module):
            def __init__(self, model):
                super().__init__()
                self.model = model
                
                # Store a direct reference to important model components
                # Avoid using hooks or dynamic method calls
                # This approach works better with TorchScript
                
                # Define any necessary submodules directly
                # For example, if your model has specific decoder functions
                # self.decoder = model.decoder
            
            def forward(self, x):
                # We must manually implement the forward pass to avoid hooks
                # This should replicate what model.forward_inference does but without hooks
                with torch.no_grad():
                    # Directly implement the forward path
                    # This is a simplified example that needs to be adapted to your specific model
                    input_dict = {'image': x}
                    
                    # Call model functions directly
                    features = self.model.encoder(input_dict['image'])
                    pred = self.model.decoder(features)
                    
                    # Get saliency maps
                    saliency_list = []
                    for i in range(4):  # Assuming 4 saliency maps
                        # Directly access the saliency map computation
                        sal_map = self.model.get_saliency_map(pred, level=i)
                        saliency_list.append(sal_map)
                    
                    # Get laplacian maps
                    laplacian_list = []
                    for i in range(3):  # Assuming 3 laplacian maps
                        # Directly access the laplacian map computation
                        lap_map = self.model.get_laplacian_map(pred, level=i)
                        laplacian_list.append(lap_map)
                    
                    # Return predictions and maps
                    return (pred, 
                            saliency_list[0], saliency_list[1], saliency_list[2], saliency_list[3],
                            laplacian_list[0], laplacian_list[1], laplacian_list[2])

        # Create the wrapper
        wrapper = InferenceWrapper(model)
        wrapper.eval()
        
        # Create dummy input tensor
        input_shape = (batch_size, 3, 1280, 1280)
        dummy_input = torch.randn(input_shape).cuda()
        
        print("Converting model to TorchScript...")
        # Convert to TorchScript using tracing which avoids many hook-related issues
        try:
            # Disable gradient computation
            with torch.no_grad():
                # Use tracing instead of scripting to avoid hook issues
                traced_model = torch.jit.trace(wrapper, dummy_input)
                
                # Apply optimizations and freeze the model
                traced_model = torch.jit.optimize_for_inference(torch.jit.freeze(traced_model))
            
            # Save the model
            torch.jit.save(traced_model, torchscript_path)
            print(f"TorchScript export completed successfully and saved to {torchscript_path}")
            
            # Force garbage collection to free memory
            del traced_model, wrapper, model
            torch.cuda.empty_cache()
            gc.collect()
        except Exception as e:
            print(f"Error during TorchScript tracing: {e}")
            # Try an alternative approach if tracing fails
            try:
                print("Trying alternative approach with direct implementation...")
                
                # Create a more basic wrapper with direct implementation
                class SimpleWrapper(nn.Module):
                    def __init__(self, model):
                        super().__init__()
                        # Store only essential components as attributes
                        self.encoder = model.encoder
                        self.decoder = model.decoder
                        
                        # Store any other processing functions
                        # This depends on your specific model architecture
                    
                    def forward(self, x):
                        # Implement a simpler forward pass
                        with torch.no_grad():
                            # Process input directly without hooks
                            features = self.encoder(x)
                            pred = self.decoder(features)
                            
                            # Create outputs in the expected format
                            # This is simplified and needs to be adapted to your model
                            dummy_output1 = torch.zeros_like(pred)
                            dummy_output2 = torch.zeros_like(pred)
                            dummy_output3 = torch.zeros_like(pred)
                            
                            return (pred, 
                                   dummy_output1, dummy_output1, dummy_output1, dummy_output1,
                                   dummy_output2, dummy_output2, dummy_output3)
                
                # Create and trace the simple wrapper
                simple_wrapper = SimpleWrapper(model)
                simple_wrapper.eval()
                
                with torch.no_grad():
                    traced_model = torch.jit.trace(simple_wrapper, dummy_input)
                    traced_model = torch.jit.optimize_for_inference(torch.jit.freeze(traced_model))
                
                torch.jit.save(traced_model, torchscript_path)
                print(f"Alternative TorchScript export completed successfully and saved to {torchscript_path}")
                
                # Force garbage collection
                del traced_model, simple_wrapper, model
                torch.cuda.empty_cache()
                gc.collect()
            except Exception as e:
                print(f"Error during alternative TorchScript approach: {e}")
                return None
    else:
        print(f"Using existing TorchScript model from {torchscript_path}")
    
    # Convert TorchScript model to TensorRT
    print("Converting TorchScript model to TensorRT...")
    
    # TensorRT logger
    TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
    
    # Using torch2trt for conversion with a properly isolated approach
    try:
        # Load TorchScript model
        ts_model = torch.jit.load(torchscript_path)
        ts_model.eval()
        ts_model = ts_model.cuda()
        
        # Prepare input
        input_shape = (batch_size, 3, 1280, 1280)
        dummy_input = torch.randn(input_shape).cuda()
        
        # Configure conversion parameters
        conversion_params = {
            'fp16_mode': True,
            'max_workspace_size': workspace_size,
            'strict_type_constraints': False,
            'max_batch_size': batch_size
        }
        
        # Convert to TensorRT using torch2trt
        print("Starting torch2trt conversion...")
        trt_model = torch2trt(ts_model, [dummy_input], **conversion_params)
        print("torch2trt conversion completed")
        
        # Save the engine
        with open(engine_path, "wb") as f:
            f.write(trt_model.engine.serialize())
        
        print(f"TensorRT engine saved to {engine_path}")
        
        # Clean up
        del ts_model, trt_model
        torch.cuda.empty_cache()
        gc.collect()
        
        # Load and return the engine
        runtime = trt.Runtime(TRT_LOGGER)
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        return runtime.deserialize_cuda_engine(engine_data)
        
    except Exception as e:
        print(f"Error during TensorRT conversion: {e}")
        
        # Try an alternate approach that doesn't rely on torch_tensorrt
        print("Attempting alternate TensorRT conversion...")
        
        try:
            # Configure a simpler torch2trt conversion
            ts_model = torch.jit.load(torchscript_path)
            ts_model.eval()
            ts_model = ts_model.cuda()
            
            # Create input tensor
            input_shape = (batch_size, 3, 1280, 1280)
            dummy_input = torch.randn(input_shape).cuda()
            
            # Use simpler parameters
            trt_model = torch2trt(
                ts_model, 
                [dummy_input], 
                fp16_mode=True,
                max_workspace_size=workspace_size,
                # No strict type constraints for better compatibility
                strict_type_constraints=False,
                # Set log level to get more information
                log_level=trt.Logger.VERBOSE
            )
            
            # Save the engine
            with open(engine_path, "wb") as f:
                f.write(trt_model.engine.serialize())
            
            print(f"TensorRT engine saved to {engine_path}")
            
            # Return the engine
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            return runtime.deserialize_cuda_engine(engine_data)
            
        except Exception as e:
            print(f"Error during alternate TensorRT conversion: {e}")
            
            # Try one more approach with manual building
            print("Attempting final TensorRT conversion approach...")
            
            try:
                # Try directly using torch2trt with even simpler settings
                ts_model = torch.jit.load(torchscript_path)
                ts_model.eval()
                ts_model = ts_model.cuda()
                
                # Minimal configuration
                trt_model = torch2trt(
                    ts_model, 
                    [dummy_input],
                    # Use FP32 instead of FP16 for better compatibility
                    fp16_mode=False,
                    # Minimal workspace
                    max_workspace_size=1<<28,
                    # Don't enforce type constraints
                    strict_type_constraints=False
                )
                
                # Save the engine
                with open(engine_path, "wb") as f:
                    f.write(trt_model.engine.serialize())
                
                print(f"TensorRT engine saved to {engine_path} with minimal settings")
                
                # Return the engine
                runtime = trt.Runtime(TRT_LOGGER)
                with open(engine_path, 'rb') as f:
                    engine_data = f.read()
                return runtime.deserialize_cuda_engine(engine_data)
                
            except Exception as e:
                print(f"Error during final TensorRT conversion approach: {e}")
                print("Unable to create TensorRT engine. Please verify TensorRT and torch2trt installation and model compatability.")
                return None

def load_and_test_engine(engine_path, input_shape=(1, 3, 1280, 1280)):
    """
    Load and test the TensorRT engine
    Args:
        engine_path: Path to the TensorRT engine
        input_shape: Shape of the input tensor
    """
    try:
        # Initialize TensorRT
        TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(TRT_LOGGER)
        
        # Load engine
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        engine = runtime.deserialize_cuda_engine(engine_data)
        context = engine.create_execution_context()
        
        # Test with random input
        dummy_input = torch.randn(input_shape).cuda()
        
        # Allocate memory for inputs and outputs
        bindings = []
        
        # For the input binding
        input_idx = 0  # We assume the first binding is the input
        context.set_binding_shape(input_idx, input_shape)
        bindings.append(dummy_input.data_ptr())
        
        # Allocate output memory
        output_shapes = []
        for i in range(1, engine.num_bindings):
            output_shape = context.get_binding_shape(i)
            output = torch.empty(tuple(output_shape), dtype=torch.float32, device=torch.device('cuda'))
            bindings.append(output.data_ptr())
            output_shapes.append(output)
        
        # Execute inference
        context.execute_v2(bindings)
        print("Engine loaded and tested successfully")
        return engine
            
    except Exception as e:
        print(f"Error testing TensorRT engine: {e}")
        return None
    
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert model to TensorRT using TorchScript')
    parser.add_argument('--config', '-c', type=str, default="configs/extra_dataset/Plus_Ultra.yaml", help='Path to configuration file')
    parser.add_argument('--checkpoint-dir', '-d', type=str, default="snapshots/Plus_Ultra", help='Directory containing model checkpoint')
    parser.add_argument('--engine-path', '-e', type=str, default="engine.trt", help='Path to save TensorRT engine')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workspace-size', type=int, default=3<<30, help='Workspace size in bytes')
    parser.add_argument('--test', action='store_true', help='Test the engine after conversion')
    
    args = parser.parse_args()
    
    # Create TensorRT engine
    engine = create_engine(args.config, args.checkpoint_dir, args.engine_path, args.batch_size, args.workspace_size)
    
    # Test engine if requested
    if args.test and engine is not None:
        load_and_test_engine(args.engine_path, (args.batch_size, 3, 1280, 1280))