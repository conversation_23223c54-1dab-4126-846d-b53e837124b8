import onnxruntime as ort
import numpy as np
from PIL import Image
import torch
import time

def preprocess_image(image_path, target_size=(800, 800)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

# Example usage
if __name__ == "__main__":
    # Check if CUDA is available
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Set up ONNX Runtime providers
    providers = []
    if cuda_available:
        # Add CUDA provider with options
        providers.append(('CUDAExecutionProvider', {
            'device_id': 0,
            'arena_extend_strategy': 'kNextPowerOfTwo',
            'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB limit
            'cudnn_conv_algo_search': 'EXHAUSTIVE',
            'do_copy_in_default_stream': True,
        }))
    
    # Add CPU provider as fallback
    providers.append('CPUExecutionProvider')
    
    print(f"Using providers: {[p[0] if isinstance(p, tuple) else p for p in providers]}")
    
    # Load ONNX model with CUDA support
    sess = ort.InferenceSession("Inspyrenet_800x800_logits.onnx", providers=providers)
    
    # Verify which provider is being used
    print(f"Active providers: {sess.get_providers()}")
    
    # Process an image
    image_path = "input.png"
    img = preprocess_image(image_path)
    
    # Run inference 5 times and record the time for each run
    num_runs = 5
    print(f"\nRunning {num_runs} inference iterations...")
    
    # Warm-up run (CUDA kernels are often slower on first run)
    if cuda_available:
        print("Performing warm-up run...")
        _ = sess.run(None, {"input": img})
    
    inference_times = []
    for i in range(num_runs):
        # Start timing
        start_time = time.time()
        
        # Run inference
        outputs = sess.run(None, {"input": img})
        
        # End timing
        end_time = time.time()
        inference_time = end_time - start_time
        inference_times.append(inference_time)
        
        print(f"Inference run {i+1}: {inference_time:.6f} seconds")
    
    # Print statistics
    avg_time = np.mean(inference_times)
    min_time = np.min(inference_times)
    max_time = np.max(inference_times)
    print(f"\nInference Statistics:")
    print(f"Average time: {avg_time:.6f} seconds")
    print(f"Min time: {min_time:.6f} seconds")
    print(f"Max time: {max_time:.6f} seconds")
    
    # Get alpha matte from the last run
    alpha = postprocess_output(outputs)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")
    print(f"\nOutput saved as 'output_alpha.png'")