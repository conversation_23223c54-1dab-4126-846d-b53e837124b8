# Contributors

Below is a list of developers who have contributed to torch2trt.  This is also used to track contributors
who have agreed to torch2trt's Contributor License Agreement.

- [<PERSON>](https://github.com/jaybdub) (CLA)
- <PERSON>

## Becoming a Contributor

If you've made a notable contribution to torch2trt and wish to be listed as a contributor, simply do the following.

1. Modify ``CONTRIBUTORS.md`` and add your name with a hyperlink to your GitHub account to the end of the contributors list.

    ```md
    - [<Full name or GitHub account>](https://github.com/<GitHub account>)
    ```

2. Stage the changes in a standalone commit

    ```md
    git add CONTRIBUTORS.md
    ```

3. Make a signed commit with the following message text

    ```md
    git commit -m "Added <Full name or GitHub account> to CONTRIBUTORS.md"
    ```

## Signing Contributor License Agreement (CLA)

In some instances, you may be requested to sign torch2trt's Contributor License Agreement (CLA). To do so,

1. If you're not already listed as a contributor in CONTRIBUTORS.md, make a commit as described above to add yourself to CONTRIBUTORS.md

2. Add the text ``(CLA)`` after your name in ``CONTRIBUTORS.md``
3. Stage the changes in a standalone commit

   ```md
   git add CONTRIBUTORS.md
   ```
4. Make a signed commit with the following text

   ```md
   git commit -S -m "I have read and agree to the Contributor License Agreement as written in the file CLA.md of this project.  Signed, <Full Name>"
   ```

