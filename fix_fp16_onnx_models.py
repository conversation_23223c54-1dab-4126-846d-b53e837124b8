#!/usr/bin/env python3
"""
FP16 ONNX Model Fixer

This script diagnoses and fixes numerical issues in FP16 ONNX models
that cause NaN outputs, specifically for InSPyReNet models.

Usage:
    python fix_fp16_onnx_models.py Plus_Ultra_800x800_fp16.onnx
    python fix_fp16_onnx_models.py --test-all
"""

import os
import sys
import argparse
import glob
import numpy as np
import onnx
import onnxruntime as ort
from onnxconverter_common import float16

def diagnose_model(model_path, test_input_shape=(1, 3, 800, 800)):
    """Diagnose issues with an ONNX model"""
    
    print(f"Diagnosing model: {model_path}")
    print("=" * 50)
    
    try:
        # Load model
        model = onnx.load(model_path)
        print(f"✓ Model loaded successfully")
        
        # Check model
        onnx.checker.check_model(model)
        print(f"✓ Model structure is valid")
        
        # Get model info
        print(f"Model info:")
        print(f"  IR version: {model.ir_version}")
        print(f"  Producer: {model.producer_name}")
        print(f"  Nodes: {len(model.graph.node)}")
        
        # Check inputs/outputs
        for i, input_info in enumerate(model.graph.input):
            print(f"  Input {i}: {input_info.name}, type: {input_info.type.tensor_type.elem_type}")
        
        for i, output_info in enumerate(model.graph.output):
            print(f"  Output {i}: {output_info.name}, type: {output_info.type.tensor_type.elem_type}")
        
        # Test inference
        print(f"\nTesting inference...")
        
        # Create session
        providers = ['CPUExecutionProvider']  # Use CPU for diagnosis
        session = ort.InferenceSession(model_path, providers=providers)
        
        # Create test input
        test_input = np.random.randn(*test_input_shape).astype(np.float32)
        test_input = test_input * 0.5  # Scale down to avoid extreme values
        
        # Run inference
        input_name = session.get_inputs()[0].name
        outputs = session.run(None, {input_name: test_input})
        
        # Check outputs
        for i, output in enumerate(outputs):
            print(f"  Output {i} shape: {output.shape}")
            print(f"  Output {i} dtype: {output.dtype}")
            print(f"  Output {i} range: [{np.min(output):.6f}, {np.max(output):.6f}]")
            print(f"  Output {i} mean: {np.mean(output):.6f}")
            print(f"  Output {i} std: {np.std(output):.6f}")
            
            nan_count = np.isnan(output).sum()
            inf_count = np.isinf(output).sum()
            
            if nan_count > 0:
                print(f"  ❌ Output {i} contains {nan_count} NaN values!")
            if inf_count > 0:
                print(f"  ❌ Output {i} contains {inf_count} Inf values!")
            
            if nan_count == 0 and inf_count == 0:
                print(f"  ✓ Output {i} is numerically stable")
        
        return True
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        return False

def fix_fp16_model(input_path, output_path=None, method='mixed'):
    """
    Fix FP16 model by using mixed precision or other techniques
    
    Args:
        input_path: Path to problematic FP16 model
        output_path: Path to save fixed model
        method: 'mixed', 'selective', or 'fp32'
    """
    
    if output_path is None:
        base_name = input_path.replace('_fp16.onnx', '').replace('.onnx', '')
        output_path = f"{base_name}_fp16_fixed.onnx"
    
    print(f"Fixing model: {input_path}")
    print(f"Method: {method}")
    print(f"Output: {output_path}")
    
    try:
        model = onnx.load(input_path)
        
        if method == 'fp32':
            # Convert back to FP32
            print("Converting back to FP32...")
            fixed_model = float16.convert_float_to_float16(
                model, 
                keep_io_types=True,
                disable_shape_infer=True,
                op_block_list=['Sigmoid', 'Exp', 'Log', 'Div']  # Keep these in FP32
            )
            # Actually, let's convert back to FP32 completely
            fixed_model = convert_fp16_to_fp32(model)
            
        elif method == 'mixed':
            # Keep critical operations in FP32
            print("Using mixed precision (critical ops in FP32)...")
            fixed_model = create_mixed_precision_model(model)
            
        elif method == 'selective':
            # Selective conversion with safe ranges
            print("Using selective FP16 conversion...")
            fixed_model = float16.convert_float_to_float16(
                model,
                keep_io_types=True,
                disable_shape_infer=True,
                min_positive_val=1e-4,  # More conservative range
                max_finite_val=1e3,
                op_block_list=['Sigmoid', 'Exp', 'Log', 'Div', 'Sqrt']
            )
        
        # Save fixed model
        onnx.save(fixed_model, output_path)
        print(f"✓ Fixed model saved to: {output_path}")
        
        # Test the fixed model
        print("Testing fixed model...")
        success = diagnose_model(output_path)
        
        if success:
            print(f"✓ Model fix successful!")
            return output_path
        else:
            print(f"❌ Model fix failed")
            return None
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return None

def convert_fp16_to_fp32(model):
    """Convert FP16 model back to FP32"""
    from onnx import numpy_helper
    
    # Create a copy
    model_copy = onnx.ModelProto()
    model_copy.CopyFrom(model)
    
    # Convert initializers back to FP32
    for initializer in model_copy.graph.initializer:
        if initializer.data_type == onnx.TensorProto.FLOAT16:
            # Get the numpy array
            weights = numpy_helper.to_array(initializer)
            
            # Convert to FP32
            weights_fp32 = weights.astype(np.float32)
            
            # Create new initializer
            new_initializer = numpy_helper.from_array(weights_fp32, initializer.name)
            
            # Replace the old initializer
            initializer.CopyFrom(new_initializer)
    
    # Update value info types
    for value_info in model_copy.graph.value_info:
        if value_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            value_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    # Update input types
    for input_info in model_copy.graph.input:
        if input_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            input_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    # Update output types
    for output_info in model_copy.graph.output:
        if output_info.type.tensor_type.elem_type == onnx.TensorProto.FLOAT16:
            output_info.type.tensor_type.elem_type = onnx.TensorProto.FLOAT
    
    return model_copy

def create_mixed_precision_model(model):
    """Create a mixed precision model keeping critical ops in FP32"""
    
    # List of operations that should stay in FP32 for numerical stability
    fp32_ops = [
        'Sigmoid', 'Exp', 'Log', 'Div', 'Sqrt', 'Pow',
        'ReduceMean', 'ReduceSum', 'Softmax', 'LayerNormalization'
    ]
    
    # Use float16 converter with block list
    mixed_model = float16.convert_float_to_float16(
        model,
        keep_io_types=True,
        disable_shape_infer=True,
        min_positive_val=1e-4,
        max_finite_val=1e3,
        op_block_list=fp32_ops
    )
    
    return mixed_model

def main():
    parser = argparse.ArgumentParser(description='Fix FP16 ONNX models with numerical issues')
    parser.add_argument('model', nargs='?', help='ONNX model file to fix')
    parser.add_argument('--output', '-o', type=str, help='Output file path')
    parser.add_argument('--method', '-m', type=str, default='mixed',
                       choices=['mixed', 'selective', 'fp32'],
                       help='Fix method: mixed (default), selective, or fp32')
    parser.add_argument('--test-all', action='store_true', help='Test all FP16 models in directory')
    parser.add_argument('--diagnose-only', action='store_true', help='Only diagnose, do not fix')
    
    args = parser.parse_args()
    
    print("FP16 ONNX Model Fixer")
    print("=" * 30)
    
    if args.test_all:
        # Find all FP16 models
        fp16_models = glob.glob("*_fp16.onnx")
        if not fp16_models:
            print("No FP16 models found")
            return 1
        
        print(f"Found {len(fp16_models)} FP16 models:")
        for model in fp16_models:
            print(f"  {model}")
        
        print("\nTesting models...")
        for model in fp16_models:
            print(f"\n{'='*60}")
            success = diagnose_model(model)
            if not success and not args.diagnose_only:
                print(f"Attempting to fix {model}...")
                fix_fp16_model(model, method=args.method)
        
        return 0
    
    if not args.model:
        print("Please specify a model file or use --test-all")
        return 1
    
    if not os.path.exists(args.model):
        print(f"Model file not found: {args.model}")
        return 1
    
    # Diagnose the model
    success = diagnose_model(args.model)
    
    if not success or not args.diagnose_only:
        # Try to fix the model
        fixed_path = fix_fp16_model(args.model, args.output, args.method)
        
        if fixed_path:
            print(f"\n🎉 Success! Use the fixed model:")
            print(f"python use_onnx_cuda_fp16_fixed.py")
            print(f"# Update the model path in the script to: {fixed_path}")
            return 0
        else:
            print(f"\n❌ Could not fix the model. Try:")
            print(f"1. Use FP32 model instead")
            print(f"2. Use different conversion method")
            print(f"3. Use the original PyTorch model")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
