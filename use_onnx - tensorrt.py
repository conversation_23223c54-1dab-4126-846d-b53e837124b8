import tensorrt as trt
import numpy as np
from PIL import Image
import torch
import time
import pycuda.driver as cuda
import pycuda.autoinit  # This is needed for initializing CUDA context

class TensorRTEngine:
    def __init__(self, engine_path):
        self.logger = trt.Logger(trt.Logger.WARNING)
        print(f"Loading TensorRT engine from {engine_path}")
        self.engine = self._load_engine(engine_path)
        self.context = self.engine.create_execution_context()
        self._get_engine_info()
        self._allocate_memory()

    def _load_engine(self, engine_path):
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        runtime = trt.Runtime(self.logger)
        return runtime.deserialize_cuda_engine(engine_data)

    def _get_engine_info(self):
        print("Engine loaded!")
        print(f"Number of I/O tensors: {self.engine.num_io_tensors}")
        for i in range(self.engine.num_io_tensors):
            name = self.engine.get_tensor_name(i)
            shape = self.engine.get_tensor_shape(name)
            dtype = self.engine.get_tensor_dtype(name)
            mode = "Input" if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT else "Output"
            print(f"Tensor {i}: {name}, Shape: {shape}, Type: {dtype}, Mode: {mode}")

    def _allocate_memory(self):
        self.inputs = []
        self.outputs = []
        self.bindings = {}
        self.stream = cuda.Stream()

        for i in range(self.engine.num_io_tensors):
            name = self.engine.get_tensor_name(i)
            shape = self.engine.get_tensor_shape(name)
            dtype = trt.nptype(self.engine.get_tensor_dtype(name))
            size = trt.volume(shape)

            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)

            self.context.set_tensor_address(name, device_mem)
            self.bindings[name] = {'host': host_mem, 'device': device_mem}

            if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT:
                self.inputs.append(name)
            else:
                self.outputs.append(name)

    def infer(self, input_array):
        input_name = self.inputs[0]
        output_name = self.outputs[0]

        np.copyto(self.bindings[input_name]['host'], input_array.ravel())
        cuda.memcpy_htod_async(self.bindings[input_name]['device'],
                               self.bindings[input_name]['host'],
                               self.stream)

        self.context.execute_async_v3(stream_handle=self.stream.handle)

        cuda.memcpy_dtoh_async(self.bindings[output_name]['host'],
                               self.bindings[output_name]['device'],
                               self.stream)

        self.stream.synchronize()

        shape = self.engine.get_tensor_shape(output_name)
        output = self.bindings[output_name]['host'].reshape(shape)
        return output

    def __del__(self):
        if hasattr(self, 'bindings'):
            for b in self.bindings.values():
                b['device'].free()

def preprocess_image(image_path, target_size=(1280, 960)):
    """
    Preprocess an image for TensorRT inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    original_size = img.size[::-1]  # PIL uses (W, H), we need (H, W)
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img, original_size

def postprocess_output(output, original_size=None):
    """
    Postprocess the TensorRT output to get the alpha matte
    Args:
        output: TensorRT model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output
    alpha = output.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

# Example usage
if __name__ == "__main__":
    # Check if CUDA is available
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Check TensorRT version
    print(f"TensorRT version: {trt.__version__}")
    
    # Initialize TensorRT engine
    engine_path = "Plus_Ultra_1280x960.engine"
    trt_engine = TensorRTEngine(engine_path)
    
    # Process an image
    image_path = "head.png"
    img, original_size = preprocess_image(image_path)
    
    # Run inference 5 times and record the time for each run
    num_runs = 15
    print(f"\nRunning {num_runs} inference iterations...")
    
    # Warm-up run (CUDA kernels are often slower on first run)
    print("Performing warm-up run...")
    _ = trt_engine.infer(img)
    
    inference_times = []
    for i in range(num_runs):
        # Start timing
        start_time = time.time()
        
        # Run inference
        output = trt_engine.infer(img)
        
        # End timing
        end_time = time.time()
        inference_time = end_time - start_time
        inference_times.append(inference_time)
        
        print(f"Inference run {i+1}: {inference_time:.6f} seconds")
    
    # Print statistics
    avg_time = np.mean(inference_times)
    min_time = np.min(inference_times)
    max_time = np.max(inference_times)
    print(f"\nInference Statistics:")
    print(f"Average time: {avg_time:.6f} seconds")
    print(f"Min time: {min_time:.6f} seconds")
    print(f"Max time: {max_time:.6f} seconds")
    
    # Get alpha matte from the last run
    alpha = postprocess_output(output, original_size)
    
    # Save alpha matte
    alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
    alpha_img.save("output_alpha.png")
    print(f"\nOutput saved as 'output_alpha.png'")
    
    # Cleanup
    del trt_engine