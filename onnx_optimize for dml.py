import numpy as np
from PIL import Image
import os
import time
import onnx
import onnxruntime as ort
import onnxoptimizer as optimizer

def optimize_onnx_model(input_model_path, output_model_path, input_shape=(1, 3, 1280, 1280)):
    """
    Optimize ONNX model using graph optimizations without type conversion
    Args:
        input_model_path: Path to original ONNX model
        output_model_path: Path to save optimized model
        input_shape: Fixed input shape (batch, channels, height, width)
    """
    # Load the model
    model = onnx.load(input_model_path)
    
    # Update model input shape if needed
    if input_shape is not None:
        model.graph.input[0].type.tensor_type.shape.dim[0].dim_value = input_shape[0]  # batch size
        model.graph.input[0].type.tensor_type.shape.dim[1].dim_value = input_shape[1]  # channels
        model.graph.input[0].type.tensor_type.shape.dim[2].dim_value = input_shape[2]  # height
        model.graph.input[0].type.tensor_type.shape.dim[3].dim_value = input_shape[3]  # width
    
    # Apply basic graph optimizations
    optimized_model = optimizer.optimize(model, 
                                         ['eliminate_identity',
                                          'eliminate_nop_transpose',
                                          'fuse_consecutive_transposes',
                                          'fuse_bn_into_conv',
                                          'fuse_pad_into_conv',
                                          'fuse_add_bias_into_conv'])
    
    # Save the optimized model
    onnx.save(optimized_model, output_model_path)
    print(f"Optimized model saved to {output_model_path}")
    
    return True

def create_dml_onnx_session(model_path, enable_optimization=True, dml_device_id=0):
    """
    Create an optimized ONNX runtime session using DirectML
    Args:
        model_path: Path to the ONNX model
        enable_optimization: Whether to enable graph optimization
        dml_device_id: DirectML device ID to use (0 for default GPU)
    Returns:
        ONNX Runtime InferenceSession
    """
    # Set up session options
    sess_options = ort.SessionOptions()
    
    if enable_optimization:
        # Enable graph optimizations
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Enable memory optimization - beneficial for DirectML
        sess_options.enable_mem_pattern = True
        sess_options.enable_mem_reuse = True
        
        # DML often performs better with sequential execution for some models
        # But we can experiment with both - parallel may work better in some cases
        sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        
        # Use fewer threads as DML handles parallelism on GPU
        sess_options.intra_op_num_threads = 1  # Using 1 for DML is often optimal
        
    # Additional DML-specific options
    sess_options.add_session_config_entry("session.use_compiled_network", "1")
    
    # Check for DirectML provider and use it if available
    providers = []
    if 'DmlExecutionProvider' in ort.get_available_providers():
        # Configure DirectML provider options
        dml_options = {
            'device_id': dml_device_id,  # Specify GPU device
            'enable_dynamic_graph_fusion': True,  # DML-specific dynamic graph fusion
            'disable_metacommands': False,  # Enable DirectML metacommands for better performance
            'enable_formatless_output': True,  # Improves performance for certain operations
            'use_arena_allocator': True,  # Use arena allocator for better memory management
        }
        providers.append(('DmlExecutionProvider', dml_options))

    # Always add CPU as fallback
    providers.append('CPUExecutionProvider')
        
    # Create ONNX Runtime session
    session = ort.InferenceSession(model_path, sess_options, providers=providers)
    
    # Log which provider is being used
    print(f"ONNX Runtime using: {session.get_providers()}")
    
    return session

def preprocess_image(image_path, target_size=(1280, 1280)):
    """
    Preprocess an image for ONNX inference
    Args:
        image_path: Path to input image
        target_size: Target size for the image
    Returns:
        Preprocessed image tensor
    """
    # Load and resize image
    img = Image.open(image_path).convert('RGB')
    original_size = img.size  # (width, height)
    img = img.resize(target_size, Image.BILINEAR)
    
    # Convert to numpy array and normalize
    img = np.array(img, dtype=np.float32)
    img = img / 255.0
    img = (img - np.array([0.485, 0.456, 0.406], dtype=np.float32)) / np.array([0.229, 0.224, 0.225], dtype=np.float32)
    
    # Transpose to CHW format and add batch dimension
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    
    return img, (original_size[1], original_size[0])  # Return (height, width)

def postprocess_output(output, original_size=None):
    """
    Postprocess the ONNX output to get the alpha matte
    Args:
        output: ONNX model output
        original_size: Original image size (H, W)
    Returns:
        Alpha matte as numpy array
    """
    # Get the prediction output (first output in our ONNX model)
    alpha = output[0]
    
    # Move to CPU and convert to numpy
    alpha = alpha.squeeze()  # Remove batch dimension
    alpha = np.clip(alpha, 0, 1)  # Clip values to [0,1]
    
    # If original size is provided, resize back to original dimensions
    if original_size is not None:
        alpha = Image.fromarray((alpha * 255).astype(np.uint8))
        alpha = alpha.resize((original_size[1], original_size[0]), Image.BILINEAR)
        alpha = np.array(alpha) / 255.0
    
    return alpha

def check_available_providers():
    """
    Check and print all available ONNX Runtime execution providers
    """
    providers = ort.get_available_providers()
    print("\nAvailable ONNX Runtime Providers:")
    for i, provider in enumerate(providers):
        print(f"{i+1}. {provider}")
    return providers

def check_model_input_info(model_path):
    """
    Check and print information about model inputs and outputs
    Args:
        model_path: Path to the ONNX model
    """
    # Create a minimal session just to query input/output info
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Get input details
    inputs = session.get_inputs()
    print("\nModel Input Details:")
    for i, input_info in enumerate(inputs):
        print(f"Input {i}:")
        print(f"  Name: {input_info.name}")
        print(f"  Shape: {input_info.shape}")
        print(f"  Type: {input_info.type}")
    
    # Get output details
    outputs = session.get_outputs()
    print("\nModel Output Details:")
    for i, output_info in enumerate(outputs):
        print(f"Output {i}:")
        print(f"  Name: {output_info.name}")
        print(f"  Shape: {output_info.shape}")
        print(f"  Type: {output_info.type}")

def get_available_optimization_passes():
    """
    Get a list of available optimization passes in the current onnxoptimizer installation
    Returns:
        List of available optimization pass names
    """
    try:
        # Try to get all available passes
        all_passes = optimizer.get_available_passes()
        return all_passes
    except AttributeError:
        # Fallback to known common passes that usually work
        fallback_passes = [
            'eliminate_identity',
            'eliminate_nop_transpose',
            'fuse_consecutive_transposes',
            'fuse_bn_into_conv',
            'fuse_add_bias_into_conv',
            'eliminate_unused_initializer',
            'fuse_matmul_add_bias_into_gemm'
        ]
        print("Warning: Could not get list of available optimization passes.")
        print("Using fallback list of common passes.")
        return fallback_passes

def optimize_model_for_dml(input_model_path, output_model_path=None, target_precision=None):
    """
    Optimize an ONNX model specifically for DirectML execution
    Args:
        input_model_path: Path to original ONNX model
        output_model_path: Path to save optimized model (default: adds '_dml' suffix)
        target_precision: Target precision for model quantization (None, 'FP16', or 'INT8')
    """
    if output_model_path is None:
        # Generate default output path by adding '_dml' suffix
        base, ext = os.path.splitext(input_model_path)
        output_model_path = f"{base}_dml{ext}"
    
    print(f"Optimizing model for DirectML: {input_model_path} -> {output_model_path}")
    
    # Load the model
    model = onnx.load(input_model_path)
    
    # Try different optimization strategies, starting with the most conservative
    strategies = [
        # Strategy 1: Most conservative - only basic optimizations less likely to cause issues
        ['eliminate_identity', 'eliminate_nop_transpose', 'fuse_bn_into_conv'],
        
        # Strategy 2: Medium - add some more common optimizations
        ['eliminate_identity', 'eliminate_nop_transpose', 'fuse_bn_into_conv', 
         'fuse_add_bias_into_conv', 'fuse_consecutive_transposes'],
         
        # Strategy 3: Skip optimization entirely and just do precision conversion
        []
    ]
    
    success = False
    optimized_model = model  # Default to original model if all strategies fail
    
    for i, strategy in enumerate(strategies):
        if not strategy:  # Empty strategy means skip optimization
            print(f"Strategy {i+1}: Skipping graph optimization")
            optimized_model = model
            success = True
            break
            
        try:
            print(f"Strategy {i+1}: Trying optimization with passes: {strategy}")
            optimized_model = optimizer.optimize(model, strategy)
            print(f"Strategy {i+1} succeeded!")
            success = True
            break
        except Exception as e:
            print(f"Strategy {i+1} failed with error: {str(e)}")
            continue
    
    if not success:
        print("All optimization strategies failed. Using original model.")
        optimized_model = model
    
    # Convert to FP16 precision if requested (often improves DirectML performance)
    if target_precision == 'FP16':
        try:
            from onnxconverter_common import float16
            print("Converting model to FP16 precision...")
            optimized_model = float16.convert_float_to_float16(
                optimized_model,
                keep_io_types=True
            )
            print("Model converted to FP16 precision successfully")
        except Exception as e:
            print(f"Warning: FP16 conversion failed with error: {str(e)}")
            print("Proceeding with FP32 model")
    
    # Save the optimized model
    try:
        onnx.save(optimized_model, output_model_path)
        print(f"Model saved to {output_model_path}")
    except Exception as e:
        print(f"Error saving model: {str(e)}")
        # Try to save with shape inference as a fallback
        try:
            print("Trying to save with shape inference...")
            from onnx import shape_inference
            inferred_model = shape_inference.infer_shapes(optimized_model)
            onnx.save(inferred_model, output_model_path)
            print(f"Model saved with shape inference to {output_model_path}")
        except Exception as e2:
            print(f"Shape inference and save failed: {str(e2)}")
            print("Optimization failed. Original model will be used.")
            return input_model_path
    
    return output_model_path

# Example usage
if __name__ == "__main__":
    input_model = "model.onnx"
    dml_model = "model_dml.onnx"
    
    # Check for available providers
    available_providers = check_available_providers()
    
    # Check if DirectML is available
    if 'DmlExecutionProvider' not in available_providers:
        print("\nWARNING: DirectML is not available in your ONNX Runtime installation.")
        print("You may need to install the DirectML version of ONNX Runtime:")
        print("pip install onnxruntime-directml")
        print("Continuing with CPU provider as fallback...\n")
    
    # Check input model info
    print("Original model information:")
    check_model_input_info(input_model)
    
    # Try to optimize the model for DirectML - if optimization fails, it will use the original model
    optimized_model_path = optimize_model_for_dml(input_model, dml_model, target_precision='FP16')
    
    # Verify the model path we're using
    print(f"\nUsing model: {optimized_model_path}")
    
    # Check optimized model info (if optimization succeeded)
    if optimized_model_path == dml_model:
        print("\nDML-optimized model information:")
        try:
            check_model_input_info(dml_model)
        except Exception as e:
            print(f"Error checking optimized model: {str(e)}")
            print("Falling back to original model")
            optimized_model_path = input_model
    
    # Create a DML-optimized session
    # Test with simplified settings to avoid errors
    try:
        print("\nCreating DirectML session...")
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_BASIC
        
        providers = []
        if 'DmlExecutionProvider' in ort.get_available_providers():
            # Use minimal DML options to avoid errors
            dml_options = {'device_id': 0}
            providers.append(('DmlExecutionProvider', dml_options))
        providers.append('CPUExecutionProvider')
        
        sess = ort.InferenceSession(optimized_model_path, sess_options, providers=providers)
        print(f"Session created successfully using: {sess.get_providers()}")
    except Exception as e:
        print(f"Error creating session: {str(e)}")
        print("Attempting with CPU provider only...")
        try:
            sess = ort.InferenceSession(input_model, providers=['CPUExecutionProvider'])
            print("CPU session created successfully")
        except Exception as e2:
            print(f"Error creating CPU session: {str(e2)}")
            print("Unable to continue. Please check your model and ONNX Runtime installation.")
            exit(1)
    
    # Get input name from session metadata
    input_name = sess.get_inputs()[0].name
    
    # Process an image
    image_path = "input.png"
    try:
        print(f"\nProcessing image: {image_path}")
        img, original_size = preprocess_image(image_path)
    except Exception as e:
        print(f"Error preprocessing image: {str(e)}")
        print("Using dummy input data for testing...")
        # Create dummy input with the right shape
        input_shape = sess.get_inputs()[0].shape
        if input_shape[0] == 'batch' or input_shape[0] == 'B' or input_shape[0] == -1:
            input_shape[0] = 1
        img = np.random.random(input_shape).astype(np.float32)
        original_size = (720, 1280)  # Dummy size
    
    # Run simple inference test without benchmarking
    print("\nRunning inference with DirectML...")
    try:
        start_time = time.time()
        outputs = sess.run(None, {input_name: img})
        end_time = time.time()
        print(f"Inference completed in {end_time - start_time:.4f} seconds")
        
        # Try to get alpha matte
        try:
            alpha = postprocess_output(outputs, original_size)
            alpha_img = Image.fromarray((alpha * 255).astype(np.uint8))
            alpha_img.save("output_alpha_dml.png")
            print(f"Alpha matte saved to output_alpha_dml.png")
        except Exception as e:
            print(f"Error in postprocessing: {str(e)}")
            print("Raw output shape:", [o.shape if hasattr(o, 'shape') else 'unknown shape' for o in outputs])
    except Exception as e:
        print(f"Error during inference: {str(e)}")
        print("Please check model compatibility with DirectML")