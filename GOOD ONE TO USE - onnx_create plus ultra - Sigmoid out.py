import os
import torch
import torch.nn as nn
import argparse
import onnx

from lib import *
from utils.misc import *
from data.dataloader import *
from data.custom_transforms import *

def export_to_onnx(config_path, checkpoint_dir, onnx_path, input_size=(1280, 1280), opset_version=17):
    try:
        if os.path.exists(onnx_path):
            print(f"✓ ONNX model already exists: {onnx_path}")
            print(f"Model size: {os.path.getsize(onnx_path) / (1024 * 1024):.1f} MB")
            return

        opt = load_config(config_path)

        model = eval(opt.Model.name)(**opt.Model)
        checkpoint_path = os.path.join(checkpoint_dir, 'latest.pth')
        model.load_state_dict(torch.load(checkpoint_path, map_location='cpu'), strict=True)
        model.eval()

        class CustomWrapper(nn.Module):
            def __init__(self, base_model):
                super().__init__()
                self.model = base_model
                self.model.eval()
                if hasattr(self.model, 'forward_inference'):
                    self.model.forward = self.model.forward_inference

            def forward(self, x):
                _, _, H, W = x.shape
                patch_size = 4
                new_H = ((H + patch_size - 1) // patch_size) * patch_size
                new_W = ((W + patch_size - 1) // patch_size) * patch_size

                if new_H != H or new_W != W:
                    pad_H = new_H - H
                    pad_W = new_W - W
                    x = torch.nn.functional.pad(x, (0, pad_W, 0, pad_H))

                output = self.model({'image': x})
                
                # CRITICAL CHANGE: Return raw logits without sigmoid!
                # This improves FP16 numerical stability significantly
                pred = output.get('pred', output['saliency'][3])  # No sigmoid here!
                
                # Remove any existing sigmoid/normalization - return raw logits
                # The sigmoid will be applied during inference outside the model
                return pred  # Raw logits output

        wrapper = CustomWrapper(model)
        dummy_input = torch.randn(1, 3, *input_size)

        print(f"Exporting model WITHOUT sigmoid activation for better FP16 compatibility...")
        print(f"Input size: {input_size}")
        print(f"Output: Raw logits (sigmoid will be applied during inference)")

        torch.onnx.export(
            wrapper,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['logits'],  # Changed from 'output' to 'logits' for clarity
            dynamic_axes={
                'input': {0: 'batch_size'},
                'logits': {0: 'batch_size'}
            }
        )

        print(f"✅ Model exported successfully to {onnx_path}")
        
        # Verify ONNX model
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print(f"✅ ONNX model is valid")
        print(f"📏 Input shape: [1, 3, {input_size[0]}, {input_size[1]}]")
        print(f"📤 Output: Raw logits (apply sigmoid during inference for final result)")
        print(f"💾 Model size: {os.path.getsize(onnx_path) / (1024 * 1024):.1f} MB")
        
        # Test the raw model to ensure it produces reasonable logits
        print(f"\n🧪 Testing exported ONNX model...")
        test_onnx_model(onnx_path, input_size)

    except Exception as e:
        print(f"❌ Error exporting model: {e}")
        raise

def test_onnx_model(onnx_path, input_size):
    """Test the exported ONNX model to ensure it produces reasonable logits"""
    try:
        import onnxruntime as ort
        import numpy as np
        
        # Load model
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(onnx_path, providers=providers)
        
        # Create test input
        test_input = np.random.randn(1, 3, *input_size).astype(np.float32)
        
        # Run inference
        outputs = session.run(None, {'input': test_input})
        logits = outputs[0]
        
        # Apply sigmoid to convert logits to probabilities
        sigmoid_output = 1.0 / (1.0 + np.exp(-logits))
        
        # Analyze output
        logits_min, logits_max = np.min(logits), np.max(logits)
        logits_mean, logits_std = np.mean(logits), np.std(logits)
        
        sigmoid_min, sigmoid_max = np.min(sigmoid_output), np.max(sigmoid_output)
        sigmoid_mean, sigmoid_std = np.mean(sigmoid_output), np.std(sigmoid_output)
        
        print(f"  📊 Logits range: [{logits_min:.4f}, {logits_max:.4f}]")
        print(f"  📊 Logits mean: {logits_mean:.4f}, std: {logits_std:.4f}")
        print(f"  📊 After sigmoid: [{sigmoid_min:.4f}, {sigmoid_max:.4f}]")
        print(f"  📊 Sigmoid mean: {sigmoid_mean:.4f}, std: {sigmoid_std:.4f}")
        
        # Check for issues
        has_nan = np.isnan(logits).any()
        has_inf = np.isinf(logits).any()
        
        if has_nan or has_inf:
            print(f"  ❌ ISSUE: Model produces NaN or Inf values!")
            return False
        elif logits_std < 0.01:
            print(f"  ⚠️  WARNING: Very low variance in logits")
        else:
            print(f"  ✅ Model produces reasonable logits")
            
        return True
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return False

def export_models_for_aspect_ratios(opset_version=17):
    """Export models for different aspect ratios without sigmoid activation"""
    
    print("🎯 ONNX Multi-Aspect Model Creator (FP16 Optimized)")
    print("📌 Key improvements:")
    print("   • Sigmoid activation moved outside model (better FP16 stability)")
    print("   • Raw logits output for numerical precision")
    print("   • Dynamic batch size support")
    print("=" * 70)
    
    # ATTENTION: Don't take the original model dimensions exactly because it does not work correctly.
    #            Also, with the LR mode, we cannot go smaller then the original model (384x284)
    model_dimensions = [
        (1280, 720), (1280, 960),
        (720, 1280), (960, 1280),
        (1280, 1280), (1056, 1056), (928, 928),
        (800, 800), (672, 672), (480, 480), (400, 400)
    ]

    successful_exports = 0
    total_exports = len(model_dimensions)

    for height, width in model_dimensions:
        print(f"\n🔄 Creating model with dimensions {width}x{height} (width x height)")

        if height < 500 and width < 500:
            config = "configs/extra_dataset/Plus_Ultra_LR.yaml"
            checkpoint = "snapshots/Plus_Ultra_LR"
            print(f"📁 Using LR config: {config}")
        else:
            config = "configs/extra_dataset/Plus_Ultra.yaml"
            checkpoint = "snapshots/Plus_Ultra"
            print(f"📁 Using standard config: {config}")

        print(f"📁 Using checkpoint: {checkpoint}")

        onnx_path = f"InsPyReNet_{width}x{height}_logits.onnx"  # Added _logits suffix for clarity
        
        try:
            export_to_onnx(
                config_path=config,
                checkpoint_dir=checkpoint,
                onnx_path=onnx_path,
                input_size=(height, width),
                opset_version=opset_version
            )
            successful_exports += 1
            print(f"✅ Successfully exported: {onnx_path}")
            
        except Exception as e:
            print(f"❌ Failed to export {width}x{height}: {e}")
            continue

    print(f"\n🎉 Export Summary:")
    print(f"✅ Successful: {successful_exports}/{total_exports}")
    print(f"❌ Failed: {total_exports - successful_exports}/{total_exports}")
    
    if successful_exports > 0:
        print(f"\n📋 Usage Instructions:")
        print(f"1. These models output RAW LOGITS (not probabilities)")
        print(f"2. Apply sigmoid during inference: output = 1.0 / (1.0 + exp(-logits))")
        print(f"3. This approach provides better FP16 numerical stability")
        print(f"4. TensorRT FP16 conversion should work much better now")
        
        print(f"\n🔧 Integration with TensorRT:")
        print(f"   • Convert these models to TensorRT FP16")
        print(f"   • Apply sigmoid in post-processing (CPU or GPU)")
        print(f"   • Much more stable FP16 inference expected")

if __name__ == "__main__":
    export_models_for_aspect_ratios(opset_version=17)